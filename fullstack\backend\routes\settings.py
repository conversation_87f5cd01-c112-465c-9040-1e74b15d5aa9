# fullstack/backend/routes/settings.py
from flask import Blueprint, jsonify, request
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from services.date_utils import get_current_california_date
from services.settings_service import get_default_date, update_default_date

settings_bp = Blueprint('settings', __name__)


@settings_bp.route('/settings/date', methods=['GET'])
def get_date_setting():
    try:
        current_date = get_default_date()
        from services.settings_service import auto_update_enabled
        return jsonify({"success": True, "date": current_date, "auto_update": auto_update_enabled})
    except Exception as e:
        return jsonify({"success": True, "date": get_current_california_date(), "error": str(e), "auto_update": True}), 200


@settings_bp.route('/settings/date', methods=['POST'])
def update_date_setting_route():
    try:
        data = request.get_json() or {}
        new_date = data.get("date")
        admin_password = data.get("admin_password")
        auto_update = data.get("auto_update")
        result = update_default_date(new_date, admin_password, auto_update)
        status = 200 if result.get("success") else (401 if result.get("code") == "INVALID_PASSWORD" else 400)
        return jsonify(result), status
    except Exception as e:
        return jsonify({"success": False, "error": "Internal server error", "code": "INTERNAL_ERROR", "details": str(e)}), 500
