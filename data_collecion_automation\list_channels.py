import discord

TOKEN = "MTM5NzI4ODg3NzM5OTYwNTI1OQ.GbjChY.WQAyO6uGlmctv_m5g8YH8c7KXoI1ZCRs07J6pU"

intents = discord.Intents.default()
intents.message_content = True
intents.messages = True
client = discord.Client(intents=intents)

@client.event
async def on_ready():
    print(f"Logged in as {client.user}")
    print("=" * 60)
    print("DISCOVERING CHANNELS...")
    print("=" * 60)
    
    # Get all guilds (servers) the bot has access to
    for guild in client.guilds:
        print(f"\n📋 SERVER: {guild.name} ({guild.id})")
        print("-" * 40)
        
        # List all channels
        channels = sorted(guild.channels, key=lambda c: c.position if hasattr(c, 'position') else 0)
        
        print("📁 TEXT CHANNELS:")
        for channel in channels:
            if isinstance(channel, discord.TextChannel):
                # Check if bot has read permissions
                permissions = channel.permissions_for(guild.me)
                can_read = permissions.read_messages
                can_send = permissions.send_messages
                
                status = "✅" if can_read else "❌"
                send_status = "📤" if can_send else "🚫"
                
                print(f"  {status} {send_status} #{channel.name} (ID: {channel.id})")
                if not can_read:
                    print(f"     ⚠️  Bot lacks read permissions")
        
        print("\n🎤 VOICE CHANNELS:")
        for channel in channels:
            if isinstance(channel, discord.VoiceChannel):
                print(f"  🔊 {channel.name} (ID: {channel.id})")
        
        print("\n📺 CATEGORY CHANNELS:")
        for channel in channels:
            if isinstance(channel, discord.CategoryChannel):
                print(f"  📂 {channel.name} (ID: {channel.id})")
    
    print("\n" + "=" * 60)
    print("Channel discovery complete!")
    print("=" * 60)
    await client.close()

if __name__ == "__main__":
    print("Channel Discovery Tool")
    print("This will list all channels the bot can access")
    client.run(TOKEN)
