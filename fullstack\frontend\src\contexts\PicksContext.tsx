import React, {
  createContext,
  useState,
  useContext,
  ReactNode,
  useEffect,
  useCallback,
  useRef,
} from "react";
import SmartCache from "../utils/cache";
import { useCurrentDate } from "./DateContext";

export interface SelectedPick {
  id: string; // Unique identifier combining source and pick ID
  sourceType: "pick" | "handicapper"; // Where the pick came from
  sourceId: number; // Original pick/handicapper ID
  playerName: string;
  playerNumber: string;
  betType: string;
  gameInfo: string;
  odds?: number;
  statType?: string;
  confidence?: number;
  handicapperNames?: string[]; // List of associated handicappers for this pick
  handicapperName?: string; // For picks from handicappers
  addedAt: number; // Timestamp when added
  eventDate: string; // Date this pick was added for (YYYY-MM-DD format)
}

interface PicksContextType {
  selectedPicks: SelectedPick[];
  addPick: (pick: Omit<SelectedPick, "id" | "addedAt" | "eventDate">) => void;
  removePick: (pickId: string) => void;
  clearAllPicks: () => void;
  isPickSelected: (
    sourceType: "pick" | "handicapper",
    sourceId: number,
    handicapperName?: string
  ) => boolean;
  isPickSelectedByContent: (
    playerName: string,
    betType: string,
    gameInfo: string
  ) => SelectedPick | null;
  getPicksBySource: (sourceType: "pick" | "handicapper") => SelectedPick[];
  getPicks: () => SelectedPick[];
  totalPicksCount: number;
}

const PicksContext = createContext<PicksContextType | undefined>(undefined);

// Create cache instance for My Picks with longer persistence
const picksCache = new SmartCache("my_picks", {
  freshDuration: 24 * 60 * 60 * 1000, // 24 hours fresh
  maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days max age
  version: "1.0.0",
});

export const PicksProvider: React.FC<{ children: ReactNode }> = ({
  children,
}) => {
  // Get current admin date for filtering picks
  const currentDate = useCurrentDate();

  // Initialize picks from cache synchronously (avoids empty state flash)
  const [allPicks, setAllPicks] = useState<SelectedPick[]>(() => {
    const { data } = picksCache.get<SelectedPick[]>("user_picks");
    if (data && Array.isArray(data)) {
      // Migrate existing picks that don't have eventDate field
      const migratedPicks = data.map((pick: any) => {
        if (!pick.eventDate) {
          console.log(
            "PicksContext: Migrating pick without eventDate:",
            pick.id
          );
          return {
            ...pick,
            eventDate: "2025-07-20", // Default fallback date for existing picks
          };
        }
        return pick;
      });
      console.log(
        "PicksContext: Initialized",
        migratedPicks.length,
        "picks from cache"
      );
      return migratedPicks;
    }
    return [];
  });

  // Filter picks based on current admin date
  const selectedPicks = allPicks.filter(
    (pick) => pick.eventDate === currentDate
  );

  // After initial mount, persist every change (including clear)
  const isFirstRender = useRef(true);
  useEffect(() => {
    if (isFirstRender.current) {
      isFirstRender.current = false;
      // No need to save—data already matches cache
      return;
    }

    picksCache.set(allPicks, "user_picks");
    console.log("PicksContext: Saved", allPicks.length, "total picks to cache");
  }, [allPicks]);

  // Log date filtering when current date changes
  useEffect(() => {
    const filteredCount = allPicks.filter(
      (pick) => pick.eventDate === currentDate
    ).length;
    const totalCount = allPicks.length;
    console.log(`PicksContext: Date filter applied for ${currentDate}`);
    console.log(
      `PicksContext: Showing ${filteredCount} of ${totalCount} total picks`
    );

    if (filteredCount < totalCount) {
      console.log(
        `PicksContext: Filtered out ${
          totalCount - filteredCount
        } picks from other dates`
      );
    }
  }, [currentDate, allPicks]);

  // Generate unique ID for a pick
  const generatePickId = useCallback(
    (
      sourceType: "pick" | "handicapper",
      sourceId: number,
      handicapperName?: string
    ): string => {
      if (sourceType === "handicapper" && handicapperName) {
        return `${sourceType}_${sourceId}_${handicapperName.replace(
          /\s+/g,
          "_"
        )}`;
      }
      return `${sourceType}_${sourceId}`;
    },
    []
  );

  // Add a pick to the selection
  const addPick = useCallback(
    (pick: Omit<SelectedPick, "id" | "addedAt" | "eventDate">) => {
      const pickId = generatePickId(
        pick.sourceType,
        pick.sourceId,
        pick.handicapperName
      );

      // Check if pick already exists for the current date
      const existingPick = selectedPicks.find((p) => p.id === pickId);
      if (existingPick) {
        console.log(
          "PicksContext: Pick already selected for current date:",
          pickId
        );
        return;
      }

      const newPick: SelectedPick = {
        ...pick,
        id: pickId,
        addedAt: Date.now(),
        eventDate: currentDate, // Automatically set to current admin date
      };

      setAllPicks((prev) => [...prev, newPick]);
      console.log(
        "PicksContext: Added pick for date",
        currentDate,
        ":",
        pickId
      );
    },
    [selectedPicks, generatePickId, currentDate]
  );

  // Remove a pick by ID
  const removePick = useCallback((pickId: string) => {
    setAllPicks((prev) => prev.filter((pick) => pick.id !== pickId));
    console.log("PicksContext: Removed pick:", pickId);
  }, []);

  // Clear all picks (only for current date)
  const clearAllPicks = useCallback(() => {
    setAllPicks((prev) =>
      prev.filter((pick) => pick.eventDate !== currentDate)
    );
    console.log("PicksContext: Cleared all picks for date:", currentDate);
  }, [currentDate]);

  // Check if a pick is already selected
  const isPickSelected = useCallback(
    (
      sourceType: "pick" | "handicapper",
      sourceId: number,
      handicapperName?: string
    ): boolean => {
      const pickId = generatePickId(sourceType, sourceId, handicapperName);
      return selectedPicks.some((pick) => pick.id === pickId);
    },
    [selectedPicks, generatePickId]
  );

  // Check if a pick exists by content (regardless of source type)
  const isPickSelectedByContent = useCallback(
    (
      playerName: string,
      betType: string,
      gameInfo: string
    ): SelectedPick | null => {
      const existingPick = selectedPicks.find(
        (pick) =>
          pick.playerName === playerName &&
          pick.betType === betType &&
          pick.gameInfo === gameInfo
      );
      return existingPick || null;
    },
    [selectedPicks]
  );

  // Get picks filtered by source type
  const getPicksBySource = useCallback(
    (sourceType: "pick" | "handicapper"): SelectedPick[] => {
      return selectedPicks.filter((pick) => pick.sourceType === sourceType);
    },
    [selectedPicks]
  );

  // Get all picks (sorted by most recently added)
  const getPicks = useCallback((): SelectedPick[] => {
    return [...selectedPicks].sort((a, b) => b.addedAt - a.addedAt);
  }, [selectedPicks]);

  const contextValue: PicksContextType = {
    selectedPicks,
    addPick,
    removePick,
    clearAllPicks,
    isPickSelected,
    isPickSelectedByContent,
    getPicksBySource,
    getPicks,
    totalPicksCount: selectedPicks.length,
  };

  return (
    <PicksContext.Provider value={contextValue}>
      {children}
    </PicksContext.Provider>
  );
};

export const usePicks = (): PicksContextType => {
  const context = useContext(PicksContext);
  if (context === undefined) {
    throw new Error("usePicks must be used within a PicksProvider");
  }
  return context;
};
