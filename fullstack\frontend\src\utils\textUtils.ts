/**
 * Utility functions for text formatting and transformation
 */

/**
 * Capitalizes each word in a string (title case)
 * @param text - The text to capitalize
 * @returns The text with each word capitalized
 */
export function toTitleCase(text: string): string {
  return text
    .toLowerCase()
    .split(' ')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
}

/**
 * Formats bet type text to ensure proper capitalization
 * Examples: "Over 1 Home runs" -> "Over 1 Home Runs"
 * @param betType - The bet type string to format
 * @returns The formatted bet type string
 */
export function formatBetType(betType: string): string {
  if (!betType) return betType;
  
  // Split by spaces and capitalize each word
  return betType
    .split(' ')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(' ');
}