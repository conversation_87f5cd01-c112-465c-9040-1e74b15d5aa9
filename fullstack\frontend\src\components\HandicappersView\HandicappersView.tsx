import React, { useState } from "react";
import { Hi<PERSON><PERSON>, HiStar } from "react-icons/hi2";
import { getConfidenceColor } from "../../utils/colorUtils";
import { usePicks } from "../../contexts/PicksContext";
import { useFavorites } from "../../contexts/FavoritesContext";
import { useHandicapperProfile } from "../../contexts/HandicapperProfileContext";
import { useAuth } from "../../contexts/AuthContext";
import PickDetailModal from "../PicksView/PickDetailModal";
import FavoriteStar from "./FavoriteStar";
import type { Pick, HandicapperPick, Handicapper } from "../../types";
import type { EventData } from "../../utils/api";
import {
  extractPlayerName,
  formatBetType,
  formatGameInfo,
} from "../../utils/dataTransforms";

// Pick interface for modal compatibility
interface ModalPick {
  id: number;
  playerName: string;
  playerNumber: string;
  betType: string;
  gameInfo: string;
  confidence: number;
  expertCount: number;
  additionalExperts: number;
  handicapperNames: string[];
}

interface HandicappersViewProps {
  handicappers: Handicapper[];
  events: EventData[];
}

// Function to find the full handicapper list for a given pick by matching against original events
const findFullHandicapperList = (
  pick: HandicapperPick,
  events: EventData[],
  currentHandicapper: string
): string[] => {
  // Find the original event that matches this pick
  const matchingEvent = events.find((event) => {
    const eventPlayerName = extractPlayerName(event.event_id);
    const eventBetType = formatBetType(event);
    const eventGameInfo = formatGameInfo(event);

    return (
      eventPlayerName === pick.playerName &&
      eventBetType === pick.betType &&
      eventGameInfo === pick.gameInfo
    );
  });

  if (matchingEvent && matchingEvent.handicappers) {
    // Return the full handicapper list with current handicapper first
    const otherHandicappers = matchingEvent.handicappers.filter(
      (name) => name !== currentHandicapper
    );
    return [currentHandicapper, ...otherHandicappers];
  }

  // Fallback to just the current handicapper if no match found
  return [currentHandicapper];
};

// Utility function to convert handicapper Pick to ModalPick format
const convertHandicapperPickToModalPick = (
  pick: HandicapperPick,
  handicapperName: string,
  events: EventData[]
): ModalPick => {
  const fullHandicapperList = findFullHandicapperList(
    pick,
    events,
    handicapperName
  );

  return {
    id: pick.id,
    playerName: pick.playerName,
    playerNumber: pick.playerNumber,
    betType: pick.betType,
    gameInfo: pick.gameInfo,
    confidence: pick.confidence || 75,
    expertCount: Math.min(fullHandicapperList.length, 9), // Cap at 9 for display
    additionalExperts: Math.max(0, fullHandicapperList.length - 9),
    handicapperNames: fullHandicapperList, // Include all handicappers who made this pick
  };
};

function HandicappersView({ handicappers, events }: HandicappersViewProps) {
  const { addPick, removePick, isPickSelected } = usePicks();
  const { favorites, toggleFavorite, isFavorite } = useFavorites();
  const { selectHandicapper } = useHandicapperProfile();
  const { navigateToView } = useAuth();
  const [selectedPick, setSelectedPick] = useState<ModalPick | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const displayHandicappers = React.useMemo(() => {
    const favoriteHandicappers = handicappers
      .filter((h) => isFavorite(h.id))
      .sort((a, b) => parseFloat(b.accuracy) - parseFloat(a.accuracy));

    const nonFavoriteHandicappers = handicappers
      .filter((h) => !isFavorite(h.id))
      .sort((a, b) => parseFloat(b.accuracy) - parseFloat(a.accuracy));

    return [...favoriteHandicappers, ...nonFavoriteHandicappers];
  }, [handicappers, favorites, isFavorite]);

  const handlePickClick = (pick: HandicapperPick, handicapperName: string) => {
    const modalPick = convertHandicapperPickToModalPick(
      pick,
      handicapperName,
      events
    );
    setSelectedPick(modalPick);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedPick(null);
  };

  const handleHandicapperClick = (
    handicapper: Handicapper,
    event: React.MouseEvent
  ) => {
    // Don't navigate if clicking on a pick or the favorite star
    const target = event.target as HTMLElement;
    if (target.closest(".pick-card") || target.closest(".favorite-star")) {
      return;
    }

    selectHandicapper(handicapper.id);
    navigateToView("handicapperProfile");
  };

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {displayHandicappers.map((handicapper) => (
        <div
          key={handicapper.id}
          className="bg-[#233e6c] rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 ease-linear hover:cursor-pointer hover:scale-[101.5%] relative"
          onClick={(event) => handleHandicapperClick(handicapper, event)}
        >
          <div className="flex items-center gap-4 mb-6">
            <div className="w-16 h-16 rounded-full bg-gray-700 flex items-center justify-center overflow-hidden">
              <HiUser className="w-10 h-10 text-gray-400" />
            </div>
            <div className="flex-1">
              <h3 className="text-white text-xl font-bold">
                {handicapper.name}
              </h3>
              <p className="text-white text-sm font-bold">
                {handicapper.sports}
              </p>
              <div className="flex items-center gap-2 mt-1">
                <div className="flex">
                  {Array.from({ length: 5 }).map((_, index) => (
                    <HiStar
                      key={index}
                      className={`w-4 h-4 ${
                        index < handicapper.rating
                          ? "text-yellow-400"
                          : "text-gray-600"
                      }`}
                    />
                  ))}
                </div>
                <span className="text-gray-400 text-sm">
                  {handicapper.accuracy} Accuracy
                </span>
              </div>
            </div>
            <div className="absolute top-4 right-4 favorite-star">
              <FavoriteStar
                isFavorite={isFavorite(handicapper.id)}
                onToggle={() =>
                  toggleFavorite(
                    handicapper.id,
                    handicapper.name,
                    handicapper.accuracy
                  )
                }
              />
            </div>
          </div>

          <div className="grid grid-cols-3 gap-3">
            {handicapper.picks.map((pick) => (
              <div
                key={pick.id}
                className="pick-card bg-[#061844] rounded-lg p-3 text-center h-48 w-full flex flex-col justify-between hover:bg-[#0a1a52] hover:scale-[102%] hover:shadow-lg transition-all duration-200 ease-linear hover:cursor-pointer"
                onClick={() => handlePickClick(pick, handicapper.name)}
              >
                <div className="flex flex-col items-center">
                  <div
                    className="w-12 h-12 rounded-full mb-2 flex items-center justify-center bg-gradient-to-b from-gray-800 to-gray-900 relative overflow-hidden"
                    style={{
                      border: `2px solid ${getConfidenceColor(
                        pick.confidence || 75
                      )}`,
                    }}
                  >
                    <div
                      className="font-semibold text-[100%] z-10 m-auto"
                      style={{
                        color: getConfidenceColor(pick.confidence || 75),
                      }}
                    >
                      {Math.round(pick.confidence || 75)}%
                    </div>
                  </div>
                  <h4 className="text-white text-xs font-semibold mb-1 line-clamp-2">
                    {pick.playerName}
                  </h4>
                  <p
                    className="text-xs font-medium mb-1"
                    style={{ color: getConfidenceColor(pick.confidence || 75) }}
                  >
                    {pick.betType}
                  </p>
                </div>
                <p className="text-gray-400 text-[10px] text-center whitespace-pre-line">
                  {pick.gameInfo.replace(/\s\|\s([^|]*)$/, "\n$1")}
                </p>
                <button
                  onClick={(e) => {
                    e.stopPropagation(); // Prevent modal from opening when clicking button
                    const isSelected = isPickSelected(
                      "handicapper",
                      pick.id,
                      handicapper.name
                    );
                    if (isSelected) {
                      // Remove the pick if it's already selected
                      const pickId = `handicapper_${
                        pick.id
                      }_${handicapper.name.replace(/\s+/g, "_")}`;
                      removePick(pickId);
                    } else {
                      // Add the pick if it's not selected
                      const fullHandicapperList = findFullHandicapperList(
                        pick,
                        events,
                        handicapper.name
                      );
                      addPick({
                        sourceType: "handicapper",
                        sourceId: pick.id,
                        playerName: pick.playerName,
                        playerNumber: pick.playerNumber,
                        betType: pick.betType,
                        gameInfo: pick.gameInfo,
                        confidence: pick.confidence || 75,
                        handicapperName: handicapper.name,
                        handicapperNames: fullHandicapperList,
                      });
                    }
                  }}
                  className={`text-xs font-bold px-3 py-2 rounded-lg shadow-md hover:shadow-lg transition-all duration-300 ease-linear w-full mt-auto hover:cursor-pointer ${
                    isPickSelected("handicapper", pick.id, handicapper.name)
                      ? "bg-green-500 text-white hover:bg-green-600"
                      : "bg-white hover:bg-gray-300 text-[#061844]"
                  }`}
                >
                  {isPickSelected("handicapper", pick.id, handicapper.name)
                    ? "Added ✓"
                    : "Add to List"}
                </button>
              </div>
            ))}
          </div>
        </div>
      ))}

      {/* Pick Detail Modal */}
      <PickDetailModal
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        pick={selectedPick}
        sourceType="handicapper"
        handicapperName={selectedPick?.handicapperNames[0]}
      />
    </div>
  );
}

export default HandicappersView;
