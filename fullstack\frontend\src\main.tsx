import React from "react";
import ReactDOM from "react-dom/client";
import App from "./App.tsx";
import "./index.css";
import { AuthProvider } from "./contexts/AuthContext.tsx";
import { PicksProvider } from "./contexts/PicksContext.tsx";
import { FavoritesProvider } from "./contexts/FavoritesContext.tsx";
import { SidebarProvider } from "./contexts/SidebarContext.tsx";
import { HandicapperProfileProvider } from "./contexts/HandicapperProfileContext.tsx";
import { DateProvider } from "./contexts/DateContext.tsx";

ReactDOM.createRoot(document.getElementById("root") as HTMLElement).render(
  <React.StrictMode>
    <DateProvider>
      <AuthProvider>
        <PicksProvider>
          <FavoritesProvider>
            <SidebarProvider>
              <HandicapperProfileProvider>
                <App />
              </HandicapperProfileProvider>
            </SidebarProvider>
          </FavoritesProvider>
        </PicksProvider>
      </AuthProvider>
    </DateProvider>
  </React.StrictMode>
);
