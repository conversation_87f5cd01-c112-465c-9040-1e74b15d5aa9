# fullstack/backend/background/scheduler.py
from typing import List
import os
from datetime import datetime, timedelta

try:
    from apscheduler.schedulers.background import BackgroundScheduler
    from apscheduler.triggers.cron import CronTrigger
except Exception:
    BackgroundScheduler = None  # type: ignore
    CronTrigger = None  # type: ignore

from data_science_modules.planet_scale_port import get_connection


def start_scheduler(app):
    """Start background healing jobs if APScheduler is available."""
    if BackgroundScheduler is None:
        return

    sched = BackgroundScheduler(timezone="America/Los_Angeles")

    def heal_unknown_for_dates(dates: List[str]):
        try:
            with get_connection() as conn:
                cur = conn.cursor()
                cur.execute(
                    f"""
                    SELECT event_id, event_date, league, team_a, team_b
                    FROM events
                    WHERE event_date IN ({','.join(['%s']*len(dates))})
                      AND (LOWER(COALESCE(team_a,'')) IN ('','unknown','null','none','other')
                           OR LOWER(COALESCE(team_b,'')) IN ('','unknown','null','none','other'))
                    ORDER BY event_date DESC
                    LIMIT 300
                    """,
                    tuple(dates),
                )
                rows = cur.fetchall() or []
                updated = 0
                for (event_id, event_date, league, team_a, team_b) in rows:
                    try:
                        from fullstack.backend.services.events_service import fill_missing_teams
                        new_a, new_b = fill_missing_teams(league or "", event_date, team_a, team_b)
                        if not new_a or not new_b or (new_a == team_a and new_b == team_b):
                            continue
                        cur2 = conn.cursor()
                        cur2.execute("UPDATE events SET team_a=%s, team_b=%s WHERE event_id=%s", (new_a, new_b, event_id))
                        conn.commit()
                        updated += 1
                    except Exception:
                        continue
                if updated:
                    print(f"[Scheduler] Updated {updated} events with filled teams.")
        except Exception:
            return

    def job_heal_today():
        try:
            from zoneinfo import ZoneInfo
            base = datetime.now(ZoneInfo("America/Los_Angeles"))
        except Exception:
            base = datetime.utcnow()
        dates = [base.strftime("%Y-%m-%d")]
        heal_unknown_for_dates(dates)

    def job_heal_recent():
        try:
            from zoneinfo import ZoneInfo
            base = datetime.now(ZoneInfo("America/Los_Angeles"))
        except Exception:
            base = datetime.utcnow()
        dates = [(base - timedelta(days=i)).strftime("%Y-%m-%d") for i in range(0, 3)]
        heal_unknown_for_dates(dates)

    try:
        sched.add_job(job_heal_today, CronTrigger(day_of_week="mon-sun", hour="9-12", minute="*/30"))
        sched.add_job(job_heal_recent, CronTrigger(day_of_week="mon-sun", hour=0, minute=30))
        sched.start()
    except Exception:
        pass

