import React, { useEffect, useState } from "react";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>rrowLeft,
  Hi<PERSON>ser,
  HiStar,
  HiTrash,
  HiChevronRight,
  HiUserGroup,
  HiListBullet,
} from "react-icons/hi2";
import { BsPin } from "react-icons/bs";
import { useSidebar } from "../../contexts/SidebarContext";
import { useHandicapperProfile } from "../../contexts/HandicapperProfileContext";
import { useFavorites } from "../../contexts/FavoritesContext";
import { useAuth } from "../../contexts/AuthContext";
import { useHandicapperPicks } from "../../hooks/useTodaysEvents";
import { usePicks, SelectedPick } from "../../contexts/PicksContext";
import { getConfidenceColor } from "../../utils/colorUtils";
import { IoShirtOutline } from "react-icons/io5";
import FavoriteStar from "../../components/HandicappersView/FavoriteStar";
import PickDetailModal from "../../components/PicksView/PickDetailModal";
import type { Pick as ImportedPick } from "../../types";
import { useCurrentDate } from "../../contexts/DateContext";

// ===== DATE CONFIGURATION =====
// Always use the dynamic date setting from the admin date service
// The date service handles whether to use custom date or fallback to default

interface Pick {
  id: number;
  playerName: string;
  playerNumber: string;
  betType: string;
  gameInfo: string;
  confidence?: number;
}

interface HandicapperProfileData {
  id: number;
  name: string;
  accuracy: string;
  sports: string;
  rating?: number;
  picks: Pick[];
}

const HandicapperProfile: React.FC = () => {
  const { selectedId } = useHandicapperProfile();
  const { toggleSidebar, isOpen } = useSidebar();
  const { navigateToView, navigateBack, canNavigateBack } = useAuth();
  const { favoriteHandicappers, toggleFavorite, isFavorite } = useFavorites();
  const { getPicks, removePick, addPick, isPickSelected } = usePicks();
  const [profile, setProfile] = useState<HandicapperProfileData | null>(null);

  // My List dropdown state
  const [isDropdownVisible, setIsDropdownVisible] = useState(false);
  const [isPinned, setIsPinned] = useState(false);
  const [selectedPick, setSelectedPick] = useState<ImportedPick | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [hoverTimeout, setHoverTimeout] = useState<NodeJS.Timeout | null>(null);

  // My List dropdown handlers
  const handleMouseEnter = () => {
    if (hoverTimeout) {
      clearTimeout(hoverTimeout);
      setHoverTimeout(null);
    }
    if (!isPinned) {
      setIsDropdownVisible(true);
    }
  };

  const handleMouseLeave = () => {
    if (!isPinned) {
      // Add a small delay before closing to prevent accidental closures
      const timeout = setTimeout(() => {
        setIsDropdownVisible(false);
      }, 150);
      setHoverTimeout(timeout);
    }
  };

  const handlePickClick = (selectedPick: SelectedPick) => {
    // Convert SelectedPick to ImportedPick format for the modal
    const modalPick: ImportedPick = {
      id: selectedPick.sourceId,
      playerName: selectedPick.playerName,
      playerNumber: selectedPick.playerNumber,
      betType: selectedPick.betType,
      gameInfo: selectedPick.gameInfo,
      confidence: selectedPick.confidence || 50,
      handicapperNames: selectedPick.handicapperNames || [],
      expertCount: 1,
      additionalExperts: 0,
    };
    setSelectedPick(modalPick);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedPick(null);
  };

  const togglePin = () => {
    const newPinnedState = !isPinned;
    setIsPinned(newPinnedState);
    if (newPinnedState) {
      setIsDropdownVisible(true);
    }
  };

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (hoverTimeout) {
        clearTimeout(hoverTimeout);
      }
    };
  }, [hoverTimeout]);

  // PicksDropdown Component
  const PicksDropdown = ({
    isVisible,
    onMouseEnter,
    onMouseLeave,
    onPickClick,
    isPinned,
    togglePin,
  }: {
    isVisible: boolean;
    onMouseEnter: () => void;
    onMouseLeave: () => void;
    onPickClick: (pick: SelectedPick) => void;
    isPinned: boolean;
    togglePin: () => void;
  }) => {
    const picks = getPicks();

    if (!isVisible) {
      return null;
    }

    return (
      <div
        className="absolute top-full right-0 w-80 pt-1 z-50"
        onMouseEnter={onMouseEnter}
        onMouseLeave={onMouseLeave}
      >
        <div className="bg-[#233e6c] border border-gray-600 rounded-lg shadow-xl max-h-96 overflow-hidden">
          <div className="p-3 border-b border-gray-600">
            <div className="flex items-center justify-between">
              <h3 className="text-white font-semibold text-sm">
                My Picks ({picks.length})
              </h3>
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  togglePin();
                }}
                className="text-white hover:text-gray-300 p-1 rounded cursor-pointer transition-transform"
                title={isPinned ? "Unpin" : "Pin"}
              >
                <BsPin
                  className={`w-4 h-4 transition-transform ${
                    isPinned ? "" : "-rotate-45"
                  }`}
                />
              </button>
            </div>
          </div>
          <div className="max-h-80 overflow-y-auto picks-dropdown-scroll">
            {picks.map((pick) => (
              <div
                key={pick.id}
                className="p-3 border-b border-gray-700 last:border-b-0 hover:bg-[#1a2d54] transition-colors group cursor-pointer"
                onClick={(e) => {
                  e.stopPropagation();
                  onPickClick(pick);
                }}
              >
                <div className="flex items-center justify-between">
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <span className="text-white font-medium text-sm truncate">
                        {pick.playerName}
                      </span>
                      <span className="text-gray-400 text-xs">
                        #{pick.playerNumber}
                      </span>
                    </div>
                    <div className="text-gray-300 text-xs mb-1 truncate">
                      {pick.betType}
                    </div>
                    <div className="text-gray-400 text-xs truncate">
                      {pick.gameInfo}
                    </div>
                  </div>
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      removePick(pick.id);
                    }}
                    className="opacity-0 group-hover:opacity-100 text-red-400 hover:text-red-300 hover:bg-red-500/20 p-1 rounded transition-all cursor-pointer"
                    title="Remove pick"
                  >
                    <HiTrash className="w-4 h-4" />
                  </button>
                </div>
              </div>
            ))}
            {picks.length === 0 && (
              <div className="p-4 text-center text-gray-400 text-sm">
                No picks added yet
              </div>
            )}
          </div>
        </div>
      </div>
    );
  };

  // Get current date from DateContext (always use dynamic date setting)
  const currentDate = useCurrentDate();
  
  // Always use the dynamic date setting from the admin date service
  const targetDate = currentDate;

  // Use the new hook to get picks for this handicapper with the same date as AddPicksPage
  const {
    picks,
    handicapperName,
    loading: picksLoading,
    error: picksError,
  } = useHandicapperPicks(selectedId, targetDate);

  useEffect(() => {
    if (selectedId === null) return;

    // Log date configuration for debugging
    console.log("🏆 HandicapperProfile DATE CONFIG:");
    console.log("   - Using dynamic date setting:", currentDate);
    console.log("   - Passing to hook:", targetDate);

    // Try to get handicapper from favorites first
    const favoriteHandicapper = favoriteHandicappers.find(
      (h) => h.id === selectedId
    );

    let name = handicapperName;
    let accuracy = "N/A";

    // If it's in favorites, use the favorited data for name and accuracy
    if (favoriteHandicapper) {
      name = favoriteHandicapper.name;
      accuracy = favoriteHandicapper.accuracy;
    } else if (
      handicapperName &&
      handicapperName !== `Handicapper ${selectedId}`
    ) {
      // If we got a real name from the picks data, use that
      name = handicapperName;
      // Calculate rough accuracy based on number of picks (mock logic)
      const roughAccuracy = Math.min(95, 70 + Math.floor(picks.length * 2));
      accuracy = `${roughAccuracy}%`;
    }

    setProfile({
      id: selectedId,
      name: name,
      accuracy: accuracy,
      sports: "Multiple Sports",
      rating: Math.min(5, Math.max(3, Math.floor(parseFloat(accuracy) / 20))), // Convert accuracy to 3-5 star rating
      picks: picks,
    });
  }, [selectedId, favoriteHandicappers, handicapperName, picks, targetDate]);

  if (selectedId === null) {
    return (
      <div className="min-h-screen bg-[#061844] flex items-center justify-center text-white">
        <div className="text-center">
          <h2 className="text-xl font-semibold mb-2">
            No handicapper selected
          </h2>
          <button
            onClick={() => navigateToView("home")}
            className="px-4 py-2 bg-[#233e6c] hover:bg-[#1a2d54] rounded-lg transition-colors"
          >
            Return Home
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-[#061844] text-white">
      <header className="w-full bg-[#061844] border-b border-gray-700">
        <div className="p-4">
          <div className="flex items-center justify-between gap-4">
            {/* Left side - Navigation */}
            <div className="flex items-center gap-3">
              {!isOpen && (
                <HiBars3
                  className="w-8 h-8 text-white bg-[#233e6c] rounded p-1 cursor-pointer hover:bg-[#1a2d54] transition-colors flex-shrink-0"
                  onClick={() => toggleSidebar()}
                />
              )}

              {/* Back Button */}
              <button
                onClick={() => {
                  if (canNavigateBack) {
                    navigateBack();
                  } else {
                    navigateToView("addPicks");
                  }
                }}
                className="flex items-center gap-2 px-3 py-2 bg-[#233e6c] hover:bg-[#1a2d54] text-white rounded-lg transition-all duration-200 shadow-sm font-medium hover:cursor-pointer hover:scale-105"
              >
                <HiArrowLeft className="w-4 h-4" />
                <span className="text-sm hidden sm:inline">Back</span>
              </button>

              {/* View All Handicappers Button */}
              <button
                onClick={() => navigateToView("addPicks")}
                className="flex items-center gap-2 px-3 py-2 bg-[#233e6c] hover:bg-[#1a2d54] text-white rounded-lg transition-all duration-200 shadow-sm font-medium hover:cursor-pointer hover:scale-105"
              >
                <HiUserGroup className="w-4 h-4" />
                <span className="text-sm hidden md:inline">
                  View All Handicappers
                </span>
                <span className="text-sm md:hidden">All</span>
              </button>
            </div>

            {/* Right side - My List and Date indicator */}
            <div className="flex items-center gap-3">
              {/* My List Icon with Dropdown */}
              <div
                className="relative"
                onMouseEnter={handleMouseEnter}
                onMouseLeave={handleMouseLeave}
              >
                <div className="flex items-center gap-2 px-3 py-2 bg-[#233e6c] hover:bg-[#1a2d54] text-white rounded-lg transition-all duration-200 shadow-sm font-medium cursor-pointer">
                  <HiListBullet className="w-4 h-4" />
                  <span className="text-sm hidden sm:inline">My List</span>
                  <span className="bg-[#58C612] text-white px-2 py-0.5 rounded-full text-xs font-bold">
                    {getPicks().length}
                  </span>
                </div>
                <PicksDropdown
                  isVisible={isDropdownVisible || isPinned}
                  onMouseEnter={handleMouseEnter}
                  onMouseLeave={handleMouseLeave}
                  onPickClick={handlePickClick}
                  isPinned={isPinned}
                  togglePin={togglePin}
                />
              </div>

              {/* Date indicator - always show the current date setting */}
              <span className="text-sm text-gray-400 bg-[#233e6c] px-3 py-1 rounded-lg">
                📅 {currentDate}
              </span>
            </div>
          </div>

          {/* Breadcrumb Navigation */}
          <div className="flex items-center gap-2 mt-3 text-sm text-gray-400">
            <button
              onClick={() => navigateToView("home")}
              className="hover:text-white transition-colors cursor-pointer"
            >
              Home
            </button>
            <HiChevronRight className="w-4 h-4" />
            <button
              onClick={() => navigateToView("addPicks")}
              className="hover:text-white transition-colors cursor-pointer"
            >
              Add Picks
            </button>
            <HiChevronRight className="w-4 h-4" />
            <span className="text-white font-medium">
              {profile?.name || "Handicapper Profile"}
            </span>
          </div>
        </div>
      </header>

      <main className="p-6 max-w-6xl mx-auto">
        {picksLoading ? (
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#58C612] mx-auto mb-4"></div>
              <div className="text-gray-400">
                Loading handicapper profile...
              </div>
              <div className="text-gray-500 text-sm mt-2">
                📅 Using date: {currentDate}
              </div>
            </div>
          </div>
        ) : profile ? (
          (() => {
            // Filter recent picks to exclude ones that are already in user's picks
            // Use the profile name if available, otherwise fall back to handicapperName
            const currentHandicapperName = profile?.name || handicapperName;
            const availableRecentPicks = picks.filter(
              (pick) =>
                !isPickSelected("handicapper", pick.id, currentHandicapperName)
            );

            // Get user's picks from this specific handicapper (from PicksContext, not recent picks)
            const userPicksFromHandicapper = getPicks().filter(
              (pick) =>
                pick.sourceType === "handicapper" &&
                pick.handicapperName === currentHandicapperName
            );

            return (
              <>
                {/* Profile Header */}
                <div className="bg-[#233e6c] rounded-xl p-6 shadow-lg mb-8 relative">
                  <div className="flex items-center gap-6 mb-6">
                    <div className="w-20 h-20 rounded-full bg-gray-700 flex items-center justify-center overflow-hidden">
                      <HiUser className="w-12 h-12 text-gray-400" />
                    </div>
                    <div className="flex-1">
                      <h2 className="text-white text-3xl font-bold mb-2">
                        {profile.name}
                      </h2>
                      <p className="text-white text-lg font-semibold mb-2">
                        {profile.sports}
                      </p>
                      <div className="flex items-center gap-4">
                        {profile.rating && (
                          <div className="flex items-center gap-2">
                            <div className="flex">
                              {Array.from({ length: 5 }).map((_, index) => (
                                <HiStar
                                  key={index}
                                  className={`w-5 h-5 ${
                                    index < profile.rating!
                                      ? "text-yellow-400"
                                      : "text-gray-600"
                                  }`}
                                />
                              ))}
                            </div>
                          </div>
                        )}
                        <span className="text-gray-300 text-lg font-medium">
                          {profile.accuracy} Accuracy
                        </span>
                      </div>
                    </div>
                    <div className="absolute top-4 right-4">
                      <FavoriteStar
                        isFavorite={isFavorite(profile.id)}
                        onToggle={() =>
                          toggleFavorite(
                            profile.id,
                            profile.name,
                            profile.accuracy
                          )
                        }
                      />
                    </div>
                  </div>
                </div>

                {/* My Picks from this Handicapper Section */}
                <div className="mb-12">
                  <div className="flex items-center gap-3 mb-6">
                    <div className="w-1 h-8 bg-[#58C612] rounded-full"></div>
                    <h2 className="text-3xl font-bold text-white">
                      My Picks from {profile.name}
                    </h2>
                    <span className="bg-[#58C612] text-white px-3 py-1 rounded-full text-sm font-bold">
                      {userPicksFromHandicapper.length}
                    </span>
                  </div>
                  {userPicksFromHandicapper.length > 0 ? (
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                      {userPicksFromHandicapper.map((userPick) => (
                        <div
                          key={userPick.id}
                          className="bg-[#233e6c] rounded-lg p-4 shadow-lg hover:shadow-xl hover:scale-[102%] transition-all duration-300 cursor-pointer"
                        >
                          <div className="flex flex-col items-center mb-3">
                            <div
                              className="w-14 h-14 rounded-full mb-3 flex items-center justify-center bg-gradient-to-b from-gray-800 to-gray-900 relative overflow-hidden"
                              style={{
                                border: `2px solid ${getConfidenceColor(
                                  userPick.confidence || 75
                                )}`,
                              }}
                            >
                              <IoShirtOutline
                                className="w-10 h-10 absolute"
                                style={{
                                  color: getConfidenceColor(
                                    userPick.confidence || 75
                                  ),
                                }}
                              />
                              <div className="text-white font-bold text-sm z-10 relative">
                                {userPick.playerNumber}
                              </div>
                            </div>
                            <h4 className="font-semibold mb-1 text-center line-clamp-2 text-white">
                              {userPick.playerName}
                            </h4>
                            <p
                              className="text-sm font-medium mb-2 text-center"
                              style={{
                                color: getConfidenceColor(
                                  userPick.confidence || 75
                                ),
                              }}
                            >
                              {userPick.betType}
                            </p>
                          </div>
                          <p className="text-gray-400 text-xs line-clamp-3 text-center">
                            {userPick.gameInfo}
                          </p>
                          {userPick.confidence && (
                            <div className="mt-3 text-center">
                              <span
                                className="text-xs font-semibold px-2 py-1 rounded"
                                style={{
                                  color: getConfidenceColor(
                                    userPick.confidence
                                  ),
                                }}
                              >
                                {userPick.confidence}% Confidence
                              </span>
                            </div>
                          )}
                          <div className="mt-4 text-center">
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                removePick(userPick.id);
                              }}
                              className="p-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-all duration-300 hover:scale-105 shadow-md hover:shadow-lg hover:cursor-pointer"
                              title="Remove from my picks"
                            >
                              <HiTrash className="w-6 h-6 hover:cursor-pointer" />
                            </button>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-12">
                      <p className="text-gray-400 text-lg">
                        You haven't added any picks from {profile.name} yet.
                      </p>
                      <p className="text-gray-500 text-sm mt-2">
                        Add picks from their Recent Picks section below to see
                        them here.
                      </p>
                    </div>
                  )}
                </div>

                <div className="mt-16">
                  <div className="flex items-center gap-3 mb-6">
                    <div className="w-1 h-8 bg-blue-500 rounded-full"></div>
                    <h2 className="text-3xl font-bold text-white">
                      Recent Picks
                    </h2>
                    <span className="bg-blue-500 text-white px-3 py-1 rounded-full text-sm font-bold">
                      {picksLoading ? "..." : availableRecentPicks.length}
                    </span>
                  </div>
                  {picksError ? (
                    <div className="text-center py-12">
                      <p className="text-red-400 text-lg mb-2">
                        Error loading picks: {picksError}
                      </p>
                      <button
                        onClick={() => window.location.reload()}
                        className="px-4 py-2 bg-[#233e6c] hover:bg-[#1a2d54] rounded-lg transition-colors"
                      >
                        Retry
                      </button>
                    </div>
                  ) : availableRecentPicks &&
                    availableRecentPicks.length > 0 ? (
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                      {availableRecentPicks.map((pick) => (
                        <div
                          key={pick.id}
                          className="bg-[#233e6c] rounded-lg p-4 shadow-lg hover:shadow-xl hover:scale-[102%] transition-all duration-300 cursor-pointer"
                        >
                          <div className="flex flex-col items-center mb-3">
                            <div
                              className="w-14 h-14 rounded-full mb-3 flex items-center justify-center bg-gradient-to-b from-gray-800 to-gray-900 relative overflow-hidden"
                              style={{
                                border: `2px solid ${getConfidenceColor(
                                  pick.confidence || 75
                                )}`,
                              }}
                            >
                              <IoShirtOutline
                                className="w-10 h-10 absolute"
                                style={{
                                  color: getConfidenceColor(
                                    pick.confidence || 75
                                  ),
                                }}
                              />
                              <div className="text-white font-bold text-sm z-10 relative">
                                {pick.playerNumber}
                              </div>
                            </div>
                            <h4 className="font-semibold mb-1 text-center line-clamp-2 text-white">
                              {pick.playerName}
                            </h4>
                            <p
                              className="text-sm font-medium mb-2 text-center"
                              style={{
                                color: getConfidenceColor(
                                  pick.confidence || 75
                                ),
                              }}
                            >
                              {pick.betType}
                            </p>
                          </div>
                          <p className="text-gray-400 text-xs line-clamp-3 text-center">
                            {pick.gameInfo}
                          </p>
                          {pick.confidence && (
                            <div className="mt-3 text-center">
                              <span
                                className="text-xs font-semibold px-2 py-1 rounded"
                                style={{
                                  color: getConfidenceColor(pick.confidence),
                                }}
                              >
                                {pick.confidence}% Confidence
                              </span>
                            </div>
                          )}
                          <div className="mt-4 text-center">
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                // Add the pick to user's selection
                                addPick({
                                  sourceType: "handicapper",
                                  sourceId: pick.id,
                                  playerName:
                                    pick.playerName || "Unknown Player",
                                  playerNumber: pick.playerNumber || "?",
                                  betType: pick.betType || "Standard Bet",
                                  gameInfo:
                                    pick.gameInfo || "Game info unavailable",
                                  confidence: pick.confidence || 75,
                                  handicapperName: currentHandicapperName,
                                });
                              }}
                              className="cursor-pointer shadow-md hover:shadow-lg transition-all duration-300 ease-linear text-sm font-bold px-4 py-2 rounded-lg whitespace-nowrap bg-white hover:bg-gray-300 text-[#061844]"
                            >
                              Add to List
                            </button>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-12">
                      <p className="text-gray-400 text-lg">
                        No recent picks available for this handicapper.
                      </p>
                      <p className="text-gray-500 text-sm mt-2">
                        📅 Checked date: {currentDate}
                      </p>
                    </div>
                  )}
                </div>
              </>
            );
          })()
        ) : (
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <div className="text-gray-400">
                Handicapper profile not found.
              </div>
            </div>
          </div>
        )}
      </main>

      {/* Floating Action Button for Quick Navigation */}
      <div className="fixed bottom-6 right-6 z-50">
        <button
          onClick={() => navigateToView("addPicks")}
          className="group cursor-pointer shadow-md hover:shadow-lg transition-all duration-300 ease-linear text-sm font-bold px-4 py-2 rounded-lg whitespace-nowrap bg-[#58C612] text-white flex items-center gap-2 hover:scale-105"
          title="Browse All Handicappers"
        >
          <HiUserGroup className="w-4 h-4" />
          <span className="hidden lg:inline group-hover:inline transition-all duration-200">
            Browse Handicappers
          </span>
        </button>
      </div>

      {/* Pick Detail Modal */}
      <PickDetailModal
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        pick={selectedPick}
      />
    </div>
  );
};

export default HandicapperProfile;
