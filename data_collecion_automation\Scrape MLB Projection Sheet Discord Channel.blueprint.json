{"name": "Scrape MLB Projection Sheet Discord Channel", "flow": [{"id": 324, "module": "gateway:CustomWebHook", "version": 1, "parameters": {"hook": 1114445, "maxResults": 1}, "mapper": {}, "metadata": {"designer": {"x": -551, "y": -68}, "restore": {"parameters": {"hook": {"data": {"editable": "true"}, "label": "doberman-discordbot-2"}}}, "parameters": [{"name": "hook", "type": "hook:gateway-webhook", "label": "Webhook", "required": true}, {"name": "maxResults", "type": "number", "label": "Maximum number of results"}], "interface": [{"name": "batch_type", "type": "text"}, {"name": "total_images", "type": "number"}, {"name": "images", "spec": {"spec": [{"name": "channel", "type": "text"}, {"name": "content", "type": "text"}, {"name": "timestamp", "type": "text"}, {"name": "image_url", "type": "text"}, {"name": "image_filename", "type": "text"}, {"name": "is_historical", "type": "boolean"}, {"name": "message_id", "type": "text"}, {"name": "author", "type": "text"}], "type": "collection"}, "type": "array"}, {"name": "scrape_timestamp", "type": "text"}]}}, {"id": 325, "module": "builtin:BasicFeeder", "version": 1, "parameters": {}, "mapper": {"array": "{{324.images}}"}, "metadata": {"designer": {"x": -244, "y": -66}, "restore": {"expect": {"array": {"mode": "edit"}}}, "expect": [{"mode": "edit", "name": "array", "spec": [], "type": "array", "label": "Array"}]}}, {"id": 349, "module": "open-router:makeAnApiCall", "version": 1, "parameters": {"__IMTCONN__": 4422719}, "mapper": {"url": "/v1/chat/completions", "body": "{\"model\":\"openai/gpt-4.1-nano\",\"stream\":false,\"messages\":[{\"role\":\"system\",\"content\":\"You are an accurate parser for complete MLB projection sheets. Your task is to copy every piece of wording and specific numbers and decimals from any images given to you. You will need to replicate the entire thing, as text based, to be parsed. It is crucial that you get every bit of data and sort the wording accordingly to make sense. Never write in markdown, only return plaintext.\"},{\"role\":\"user\",\"content\":[{\"type\":\"text\",\"text\":\"Parse this MLB projections sheet to give me back the extracted data. Also please specify who the home_sp home starting player is and the away_sp away starting player (if applicable). Try to be as precise and specific with the wording as possible.\"}{{if(325.image_url; \", {\"\"type\"\":\"\"image_url\"\",\"\"image_url\"\":{\"\"url\"\":\"\"\" + trim(325.image_url) + \"\"\"}}\"; )}}]}]}", "method": "POST", "headers": [{"key": "Content-Type", "value": "application/json"}]}, "metadata": {"designer": {"x": 130, "y": -67}, "restore": {"expect": {"qs": {"mode": "chose"}, "method": {"mode": "chose", "label": "POST"}, "headers": {"mode": "chose", "items": [null]}}, "parameters": {"__IMTCONN__": {"data": {"scoped": "true", "connection": "open-router-4ur2vj"}, "label": "My OpenRouter connection"}}}, "parameters": [{"name": "__IMTCONN__", "type": "account:open-router3,open-router-4ur2vj", "label": "Connection", "required": true}], "expect": [{"name": "url", "type": "text", "label": "URL", "required": true}, {"name": "method", "type": "select", "label": "Method", "required": true, "validate": {"enum": ["GET", "POST", "PUT", "PATCH", "DELETE"]}}, {"name": "headers", "spec": [{"name": "key", "type": "text", "label": "Key"}, {"name": "value", "type": "text", "label": "Value"}], "type": "array", "label": "Headers"}, {"name": "qs", "spec": [{"name": "key", "type": "text", "label": "Key"}, {"name": "value", "type": "text", "label": "Value"}], "type": "array", "label": "Query String"}, {"name": "body", "type": "any", "label": "Body"}]}, "onerror": [{"id": 345, "module": "builtin:Ignore", "version": 1, "metadata": {"designer": {"x": 207, "y": 260}}}]}, {"id": 328, "module": "open-router:makeAnApiCall", "version": 1, "parameters": {"__IMTCONN__": 4422719}, "filter": {"name": "Contains \"Total Runs Scored:\"", "conditions": [[{"a": "{{349.body.choices[].message.content}}", "b": "Total Runs Scored:", "o": "text:contain"}]]}, "mapper": {"url": "/v1/chat/completions", "method": "POST", "headers": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"model\":\"openai/gpt-5-mini\", \"providers\": [\"chutes\"], \"stream\":false,\"messages\":[{\"role\":\"system\",\"content\":\"You are an accurate parser for complete MLB projection sheets. Return ONLY valid JSON matching this EXACT schema: {\\\"projections\\\":[{\\\"game_date\\\":\\\"YYYY-MM-DD\\\",\\\"away_team\\\":\\\"TeamName\\\",\\\"home_team\\\":\\\"TeamName\\\",\\\"away_sp\\\":\\\"Player Name\\\",\\\"home_sp\\\":\\\"Player Name\\\",\\\"away_score\\\":4.25,\\\"home_score\\\":3.75,\\\"total_runs_scored\\\":8.00,\\\"home_team_run_diff\\\":-0.50,\\\"winning_team\\\":\\\"TeamName\\\",\\\"game_xHRs\\\":1.25,\\\"home_win_pct\\\":45.5,\\\"xRFI\\\":0.95,\\\"player_name\\\":\\\"Player Name\\\",\\\"is_pitcher\\\":1,\\\"AB\\\":4.0,\\\"Hits\\\":1.2,\\\"Singles\\\":0.8,\\\"Doubles\\\":0.3,\\\"Triples\\\":0.1,\\\"Homeruns\\\":0.2,\\\"TB\\\":1.8,\\\"Walks\\\":0.4,\\\"SB\\\":0.1,\\\"Runs\\\":0.6,\\\"RBIs\\\":0.5,\\\"HRR\\\":2.1,\\\"OBP\\\":0.35,\\\"HitterStrikeouts\\\":0.9,\\\"Pitches\\\":85.0,\\\"Outs\\\":15.0,\\\"BF\\\":22.5,\\\"Strikeouts\\\":4.2,\\\"PitcherWalks\\\":2.1,\\\"ER\\\":2.8,\\\"HA\\\":5.3,\\\"xRFI_Chance\\\":null}]}. CRITICAL RULES: 1) Use null (not empty strings) for missing numeric values, 2) Use YYYY-MM-DD date format, 3) Team names: use only nickname without city (Dodgers not Los Angeles Dodgers), 4) is_pitcher: 1 for pitchers, 0 for hitters (NUMBER not string), 5) All stat values are NUMBERS except player/team names which are strings, 6) Extract EVERY player with ALL available stats, 7) Ensure valid JSON syntax - no trailing commas or syntax errors. Try to fill in as many columns as possible, especially don't forget the xRFI_Chance because it should be in every response. 8) Also ensure to fill in all of the away_sp and home_sp columns for each row so there's less null. CRITICAL: input xRFI_Chance value to its column always.\"},{\"role\":\"user\",\"content\":[{\"type\":\"text\",\"text\":\"Parse this COMPLETE MLB projection sheet. Extract ALL players (both hitters and pitchers) with their full projected stats. It is CRITICAL that you respond with entirely correct and valid JSON syntax. Always use double quotes for property values, etc. And, do not wrap the JSON in a markdown codeblock (starting with ```json and ending with ```. Instead, just return the value inside of the codeblock, not wrapped. Current datetime reference: '{{now}}' . Image file name: {{325.image_filename}}. If the post was before 11am it would be for the date specified in the image file name, but if it states after 11 am then the image would be extracted as the next day. \\n\\nProjection Sheet Text:\\n: {{349.body.choices[].message.content}}\"}]}"}, "metadata": {"designer": {"x": 428, "y": -51}, "restore": {"parameters": {"__IMTCONN__": {"label": "My OpenRouter connection", "data": {"scoped": "true", "connection": "open-router-4ur2vj"}}}, "expect": {"method": {"mode": "chose", "label": "POST"}, "headers": {"mode": "chose", "items": [null]}, "qs": {"mode": "chose"}}}, "parameters": [{"name": "__IMTCONN__", "type": "account:open-router3,open-router-4ur2vj", "label": "Connection", "required": true}], "expect": [{"name": "url", "type": "text", "label": "URL", "required": true}, {"name": "method", "type": "select", "label": "Method", "required": true, "validate": {"enum": ["GET", "POST", "PUT", "PATCH", "DELETE"]}}, {"name": "headers", "type": "array", "label": "Headers", "spec": [{"name": "key", "type": "text", "label": "Key"}, {"name": "value", "type": "text", "label": "Value"}]}, {"name": "qs", "type": "array", "label": "Query String", "spec": [{"name": "key", "type": "text", "label": "Key"}, {"name": "value", "type": "text", "label": "Value"}]}, {"name": "body", "type": "any", "label": "Body"}]}, "onerror": [{"id": 327, "module": "builtin:Ignore", "version": 1, "metadata": {"designer": {"x": 491, "y": 281}}}]}, {"id": 211, "module": "json:ParseJSON", "version": 1, "parameters": {"type": ""}, "mapper": {"json": "{{trim(replace(replace(328.body.choices[1].message.content; \"```json\"; ); \"```\"; ))}}"}, "metadata": {"designer": {"x": 756, "y": -59}, "restore": {"parameters": {"type": {"label": "Choose a data structure"}}}, "parameters": [{"name": "type", "type": "udt", "label": "Data structure"}], "expect": [{"name": "json", "type": "text", "label": "JSON string", "required": true}]}, "onerror": [{"id": 344, "module": "builtin:Ignore", "version": 1, "metadata": {"designer": {"x": 790, "y": 243}}}]}, {"id": 303, "module": "http:ActionSendData", "version": 3, "parameters": {"handleErrors": true, "useNewZLibDeCompress": true}, "mapper": {"ca": "", "qs": [], "url": "https://project-parlay-mattfavela.replit.app/api/external/mlb_projections", "data": "{ \"projections\": {{`211`}} }", "gzip": true, "method": "post", "headers": [{"name": "Content-Type", "value": "application/json"}, {"name": "X-API-Key", "value": "pk_ext_api_2025_ppadmin"}], "timeout": "300", "useMtls": false, "authPass": "", "authUser": "", "bodyType": "raw", "contentType": "application/json", "serializeUrl": false, "shareCookies": false, "parseResponse": true, "followRedirect": true, "useQuerystring": false, "followAllRedirects": false, "rejectUnauthorized": true}, "metadata": {"designer": {"x": 1112, "y": -47}, "restore": {"expect": {"qs": {"mode": "chose"}, "method": {"mode": "chose", "label": "POST"}, "headers": {"mode": "chose", "items": [null, null]}, "bodyType": {"label": "Raw"}, "contentType": {"label": "JSON (application/json)"}}}, "parameters": [{"name": "handleErrors", "type": "boolean", "label": "Evaluate all states as errors (except for 2xx and 3xx )", "required": true}, {"name": "useNewZLibDeCompress", "type": "hidden"}], "expect": [{"name": "url", "type": "url", "label": "URL", "required": true}, {"name": "serializeUrl", "type": "boolean", "label": "Serialize URL", "required": true}, {"name": "method", "type": "select", "label": "Method", "required": true, "validate": {"enum": ["get", "head", "post", "put", "patch", "delete", "options"]}}, {"name": "headers", "spec": [{"name": "name", "type": "text", "label": "Name", "required": true}, {"name": "value", "type": "text", "label": "Value"}], "type": "array", "label": "Headers"}, {"name": "qs", "spec": [{"name": "name", "type": "text", "label": "Name", "required": true}, {"name": "value", "type": "text", "label": "Value"}], "type": "array", "label": "Query String"}, {"name": "bodyType", "type": "select", "label": "Body type", "validate": {"enum": ["raw", "x_www_form_urlencoded", "multipart_form_data"]}}, {"name": "parseResponse", "type": "boolean", "label": "Parse response", "required": true}, {"name": "authUser", "type": "text", "label": "User name"}, {"name": "authPass", "type": "password", "label": "Password"}, {"name": "timeout", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Timeout", "validate": {"max": 300, "min": 1}}, {"name": "shareCookies", "type": "boolean", "label": "Share cookies with other HTTP modules", "required": true}, {"name": "ca", "type": "cert", "label": "Self-signed certificate"}, {"name": "rejectUnauthorized", "type": "boolean", "label": "Reject connections that are using unverified (self-signed) certificates", "required": true}, {"name": "followRedirect", "type": "boolean", "label": "Follow redirect", "required": true}, {"name": "useQuerystring", "type": "boolean", "label": "Disable serialization of multiple same query string keys as arrays", "required": true}, {"name": "gzip", "type": "boolean", "label": "Request compressed content", "required": true}, {"name": "useMtls", "type": "boolean", "label": "Use Mutual TLS", "required": true}, {"name": "contentType", "type": "select", "label": "Content type", "validate": {"enum": ["text/plain", "application/json", "application/xml", "text/xml", "text/html", "custom"]}}, {"name": "data", "type": "buffer", "label": "Request content"}, {"name": "followAllRedirects", "type": "boolean", "label": "Follow all redirect", "required": true}]}, "onerror": [{"id": 343, "module": "builtin:Ignore", "version": 1, "metadata": {"designer": {"x": 1159, "y": 278}}}]}], "metadata": {"instant": true, "version": 1, "scenario": {"roundtrips": 1, "maxErrors": 3, "autoCommit": true, "autoCommitTriggerLast": true, "sequential": false, "slots": null, "confidential": false, "dataloss": false, "dlq": false, "freshVariables": false}, "designer": {"orphans": []}, "zone": "us2.make.com", "notes": []}}