import discord
import requests
import datetime

TOKEN = "INSERT TOKEN HERE"
WEBHOOK_URL = "https://hook.us2.make.com/qm7ixs6m662340qkecek9dh5g5qkqov5"
TARGET_CHANNEL_IDS = ["1358859416970989711","1353413269510950933"]  # Replace with actual channel IDs

intents = discord.Intents.default()
intents.message_content = True
intents.messages = True
client = discord.Client(intents=intents)

@client.event
async def on_ready():
    print(f"Logged in as {client.user}")

@client.event
async def on_message(message):
    if message.author.bot:
        return

    if str(message.channel.id) in TARGET_CHANNEL_IDS:
        data = {
            "channel": message.channel.name,
            "content": message.content,
            "timestamp": message.created_at.isoformat()
        }

        try:
            print(data)
            response = requests.post(WEBHOOK_URL, json=data)
            response.raise_for_status()
            #print(f"Sent to Make: {data}")
        except requests.exceptions.RequestException as e:
            print(f"Error sending to Make.com: {e}")

client.run(TOKEN)
