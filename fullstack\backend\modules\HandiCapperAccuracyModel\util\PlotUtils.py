from fullstack.backend.modules.HandiCapperAccuracyModel.database.AccuracyModelDatabase import (
    AccuracyModelDatabase,
)
import json
import matplotlib
matplotlib.use('Agg')  # Use non-GUI backend for web server
import matplotlib.pyplot as plt
import numpy as np
from fullstack.backend.data_science_modules.kde_distributions_port import fetch_distributions
import io
import base64

class PlotUtils:
    def __init__(self):
        self.db = AccuracyModelDatabase()

    def plot_pdf_cdf(
        self,
        sample_range,
        normalized_density,
        cdf,
        x_lower,
        x_upper,
        player_name,
        stat_type,
        target_date,
        save_path="pick_pdf_cdf.png",
    ):
        """
        Plots the PDF and CDF graphs and highlights the most likely 15% probability zone.

        Args:
            sample_range (np.ndarray): X-axis values used for both PDF and CDF.
            normalized_density (np.ndarray): PDF values.
            cdf (np.ndarray): CDF values.
            x_lower (float): Lower bound of 15% most probable zone.
            x_upper (float): Upper bound of 15% most probable zone.
            player_name (str): Name of the player (for title).
            stat_type (str): Stat type (e.g., Points, Assists).
            target_date (str): Game date (for title).
            save_path (str): Path to save the plot image.
        """
        fig, axs = plt.subplots(1, 2, figsize=(14, 5))

        # PDF plot
        axs[0].plot(sample_range, normalized_density, label="PDF", linewidth=2)
        axs[0].axvspan(
            x_lower, x_upper, color="orange", alpha=0.3, label="Most Likely 15% Zone"
        )
        axs[0].axvline(
            (x_lower + x_upper) / 2, color="green", linestyle="--", label="Zone Center"
        )
        axs[0].set_title("PDF with Most Likely 15% Integrated Probability Zone")
        axs[0].set_xlabel("Stat Value")
        axs[0].set_ylabel("Density")
        axs[0].grid(True)
        axs[0].legend()

        # CDF plot
        axs[1].plot(sample_range, cdf, color="green", linewidth=2)
        axs[1].set_title(f"CDF of {player_name}'s {stat_type} on {target_date}")
        axs[1].set_xlabel(f"{stat_type}")
        axs[1].set_ylabel("Cumulative Probability")
        axs[1].grid(True)

        plt.tight_layout()
        plt.savefig(save_path)
        plt.close()

    def pack_distribution_data(self, sample_range, pdf, cdf):
        pdf_dict = {
            f"{round(x, 2)}": round(y, 8) for x, y in zip(sample_range, pdf)
        }

        cdf_dict = {
            f"{round(x, 2)}": round(y, 8) for x, y in zip(sample_range, cdf)
        }

        return {
            "pdf_json": json.dumps(pdf_dict),
            "cdf_json": json.dumps(cdf_dict),
            "odds_pdf_json": json.dumps({}),
            "odds_cdf_json": json.dumps({}),
        }

    def unpack_distribution_data(self, event_id):
        packed_data = fetch_distributions(event_id)

        player_name = packed_data["player_name"]
        stat_type = packed_data["stat_type"]
        game_date = packed_data["game_date"]
        pdf_dict = packed_data["pdf"]  # Already parsed by fetch_distributions()
        cdf_dict = packed_data["cdf"]  # Already parsed by fetch_distributions()

        x_vals = sorted(pdf_dict.keys(), key=lambda x: float(x))
        sample_range = np.array([float(x) for x in x_vals])
        pdf = np.array([pdf_dict[x] for x in x_vals])
        cdf = np.array([cdf_dict[x] for x in x_vals])

        x_density_pairs = list(zip(sample_range, pdf))
        x_density_pairs.sort(key=lambda x: -x[1])

        cumulative = 0.0
        included_x = []
        width = sample_range[1] - sample_range[0]
        for x, dens in x_density_pairs:
            cumulative += dens * width
            included_x.append(x)
            if cumulative >= 0.15:
                break
        x_lower = min(included_x)
        x_upper = max(included_x)

        # --- Plot to in-memory buffer ---
        fig, ax1 = plt.subplots()

        ax1.plot(sample_range, pdf, color='blue', label='PDF')
        ax1.set_xlabel(f'{stat_type} - {player_name}')
        ax1.set_ylabel('PDF', color='blue')
        ax1.tick_params(axis='y', labelcolor='blue')
        ax1.axvspan(x_lower, x_upper, alpha=0.2, color='blue')

        plt.title(f'{player_name} - {stat_type} on {game_date}')
        plt.tight_layout()

        buf = io.BytesIO()
        plt.savefig(buf, format='png')
        plt.close(fig)
        buf.seek(0)

        # Encode buffer to base64
        encoded = base64.b64encode(buf.read()).decode('utf-8')
        return encoded
