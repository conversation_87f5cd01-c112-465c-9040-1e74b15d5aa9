from typing import Dict, List, Optional, Tu<PERSON>
import numpy as np
from fullstack.backend.modules.HandiCapperAccuracyModel.database.AccuracyModelDatabase import (
    AccuracyModelDatabase,
)
from sklearn.linear_model import LogisticRegression

class LogisticRegressionModel:
    def __init__(self):
        self.db = AccuracyModelDatabase()

    def close(self):
        self.db.close()

    def train_logistic_model(self):
        print("📥 Fetching training data...")
        training_data = self.db.fetch_all_events()
        print(f"✅ Total training events fetched: {len(training_data)}")

        # Check label diversity
        # TODO: make it into another function
        labels = [e["actual_result"] for e in training_data]
        label_dist = dict(zip(*np.unique(labels, return_counts=True)))
        print("📊 Label distribution:", label_dist)

        print("🔧 Preparing training matrix for logistic regression...")
        X, y, logistic_expert_names = self._prepare_training_matrix(training_data)

        print("🤖 Training logistic regression model...")
        model = self._train_logistic_weights(X, y)
        print("✅ Logistic model trained.")
        return model, logistic_expert_names

    def _prepare_training_matrix(self, events: List[Dict]):
        expert_set = set()
        for event in events:
            for prediction in event["expert_predictions"]:
                expert_set.add(prediction[0])  # expert_name
        experts = sorted(expert_set)
        expert_index = {name: i for i, name in enumerate(experts)}

        # TODO: better variable names
        x_axis = []
        y_axis = []

        for event in events:
            row = [0] * len(experts)
            for prediction in event["expert_predictions"]:
                name = prediction[0]
                pred_value = prediction[1]
                confidence = prediction[2]

                # Normalize potential Decimal or string types to native numeric
                try:
                    conf_float = float(confidence) if confidence is not None else 0.0
                except Exception:
                    conf_float = 0.0

                try:
                    pred_int = int(pred_value)
                except Exception:
                    pred_int = 0

                signal = (1 if pred_int == 1 else -1) * conf_float
                row[expert_index[name]] = signal
            x_axis.append(row)
            # Ensure labels are ints (avoid Decimal types from DB)
            try:
                y_axis.append(int(event["actual_result"]))
            except Exception:
                y_axis.append(0)

        return np.array(x_axis), np.array(y_axis), experts

    # TODO: better parameter name
    def _train_logistic_weights(self, x_axis, y_axis):
        model = LogisticRegression()
        model.fit(x_axis, y_axis)
        return model
