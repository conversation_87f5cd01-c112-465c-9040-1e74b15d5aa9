# fullstack/backend/routes/__init__.py
"""
Routes package initializer.

Provide a register_routes(app) helper to be called from app start-up once
blueprints are available. Keep import-time work minimal.
"""


def register_routes(app):
    """Register package blueprints with the Flask app (best-effort)."""
    try:
        from .picks import picks_bp
        from .optimizer import optimizer_bp
        from .settings import settings_bp
        from .frontend import frontend_bp
        from .events import events_bp
        from .experts import experts_bp
        from .external import external_bp
        from .admin import admin_bp
        from .model import model_bp

        app.register_blueprint(picks_bp, url_prefix="/api")
        app.register_blueprint(optimizer_bp, url_prefix="/api")
        app.register_blueprint(settings_bp, url_prefix="/api")
        app.register_blueprint(events_bp, url_prefix="/api")
        app.register_blueprint(experts_bp, url_prefix="/api")
        app.register_blueprint(external_bp, url_prefix="/api")
        app.register_blueprint(admin_bp, url_prefix="/api")
        app.register_blueprint(model_bp, url_prefix="/api")
        app.register_blueprint(frontend_bp)
    except Exception:
        # Silence import/registration errors to preserve previous behavior
        # where register_routes was a no-op if blueprints unavailable.
        pass
    return