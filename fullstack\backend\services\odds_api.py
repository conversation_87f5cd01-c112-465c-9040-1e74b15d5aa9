# fullstack/backend/services/odds_api.py
import os
import requests
from typing import Dict, Optional, Tuple
from datetime import datetime
from .teams import _teams_match

SPORT_ALIASES: Dict[str, str] = {
    "NBA": "basketball_nba",
    "NCAAB": "basketball_ncaab",
    "WNBA": "basketball_wnba",
    "MLB": "baseball_mlb",
    "NFL": "americanfootball_nfl",
    "NCAAF": "americanfootball_ncaaf",
    "NHL": "icehockey_nhl",
    "EPL": "soccer_epl",
    "MLS": "soccer_usa_mls",
    "UEFA": "soccer_uefa_european_championship",
    "UFC": "mma_mixed_martial_arts",
    "MMA": "mma_mixed_martial_arts",
    "ATP": "tennis_atp_french_open",
    "WTA": "tennis_wta_french_open",
}


def resolve_sport_key(league: Optional[str]) -> Optional[str]:
    if not league:
        return None
    upper = league.strip().upper()
    if upper in SPORT_ALIASES:
        return SPORT_ALIASES[upper]

    api_key = os.getenv("ODDS_API_KEY")
    if not api_key:
        return None

    try:
        resp = requests.get(
            "https://api.the-odds-api.com/v4/sports/",
            params={"apiKey": api_key},
            timeout=10,
        )
        if resp.status_code == 429:
            print("[WARNING] Odds API /sports rate-limited (429)")
            return None
        resp.raise_for_status()
        sports = resp.json() or []

        def norm(s: str) -> str:
            return " ".join((s or "").lower().replace("-", " ").split())

        target = norm(league)

        for sp in sports:
            if norm(sp.get("key", "")) == target:
                return sp.get("key")
        for sp in sports:
            if norm(sp.get("title", "")) == target:
                return sp.get("key")
        for sp in sports:
            if target and (target in norm(sp.get("title", "")) or target in norm(sp.get("key", ""))):
                return sp.get("key")
        print(f"[WARNING] Could not resolve sport key dynamically for league: {league}")
        return None
    except Exception as e:
        print(f"[WARNING] Failed to resolve sport key via /sports: {e}")
        return None


def get_event_start_from_odds_api(league: str,
                                  team_a: Optional[str],
                                  team_b: Optional[str],
                                  preferred_date: Optional[str]) -> Tuple[Optional[str], Optional[str]]:
    try:
        api_key = os.getenv("ODDS_API_KEY")
        if not api_key:
            print("[WARNING] ODDS_API_KEY not set; skipping Odds API lookup")
            return None, None
        sport_key = resolve_sport_key(league)
        if not sport_key:
            print(f"[WARNING] Unknown league for Odds API mapping (after dynamic lookup): {league}")
            return None, None
        base_url = f"https://api.the-odds-api.com/v4/sports/{sport_key}/events"
        params = {"apiKey": api_key}
        if preferred_date:
            try:
                from datetime import datetime as _dt
                from zoneinfo import ZoneInfo as _ZI
                dt_start_pt = _dt.strptime(f"{preferred_date} 00:00:00", "%Y-%m-%d %H:%M:%S").replace(tzinfo=_ZI("America/Los_Angeles"))
                dt_end_pt = _dt.strptime(f"{preferred_date} 23:59:59", "%Y-%m-%d %H:%M:%S").replace(tzinfo=_ZI("America/Los_Angeles"))
                params["commenceTimeFrom"] = dt_start_pt.astimezone(_ZI("UTC")).strftime("%Y-%m-%dT%H:%M:%SZ")
                params["commenceTimeTo"] = dt_end_pt.astimezone(_ZI("UTC")).strftime("%Y-%m-%dT%H:%M:%SZ")
            except Exception:
                pass
        resp = requests.get(base_url, params=params, timeout=10)
        if resp.status_code == 429:
            print("[WARNING] Odds API rate-limited (429)")
            return None, None
        resp.raise_for_status()
        events = resp.json() or []
        if not events and preferred_date:
            try:
                resp2 = requests.get(base_url, params={"apiKey": api_key}, timeout=10)
                resp2.raise_for_status()
                events = resp2.json() or []
            except Exception as e2:
                print(f"[WARNING] Secondary Odds API fetch failed: {e2}")
        for ev in events:
            home = ev.get("home_team")
            away = ev.get("away_team")
            if home and away and team_a and team_b:
                pair_match = (
                    (_teams_match(team_a, home) and _teams_match(team_b, away)) or
                    (_teams_match(team_a, away) and _teams_match(team_b, home))
                )
                if not pair_match:
                    continue
                commence = ev.get("commence_time")
                if not commence or "T" not in commence:
                    continue
                date_part, time_part = commence.split("T", 1)
                time_part = time_part.replace("Z", "")
                try:
                    from datetime import datetime as _dt
                    from zoneinfo import ZoneInfo as _ZI
                    dt_utc = _dt.strptime(f"{date_part} {time_part[:8]}", "%Y-%m-%d %H:%M:%S").replace(tzinfo=_ZI("UTC"))
                    dt_pt = dt_utc.astimezone(_ZI("America/Los_Angeles"))
                    if preferred_date and dt_pt.strftime("%Y-%m-%d") != preferred_date:
                        continue
                    return dt_pt.strftime("%Y-%m-%d"), dt_pt.strftime("%Y-%m-%d %H:%M:%S")
                except Exception:
                    return date_part, f"{date_part} {time_part[:8]}"
        known_team = None
        if team_a and not team_b:
            known_team = team_a
        elif team_b and not team_a:
            known_team = team_b
        elif team_a and (str(team_b).strip().lower() in ("unknown", "null", "none", "")):
            known_team = team_a
        elif team_b and (str(team_a).strip().lower() in ("unknown", "null", "none", "")):
            known_team = team_b
        if known_team:
            candidates = []
            for ev in events:
                home = ev.get("home_team")
                away = ev.get("away_team")
                if not home or not away:
                    continue
                if _teams_match(known_team, home) or _teams_match(known_team, away):
                    commence = ev.get("commence_time")
                    if not commence or "T" not in commence:
                        continue
                    date_part, time_part = commence.split("T", 1)
                    time_part = time_part.replace("Z", "")
                    try:
                        from datetime import datetime as _dt
                        from zoneinfo import ZoneInfo as _ZI
                        dt_utc = _dt.strptime(f"{date_part} {time_part[:8]}", "%Y-%m-%d %H:%M:%S").replace(tzinfo=_ZI("UTC"))
                        dt_pt = dt_utc.astimezone(_ZI("America/Los_Angeles"))
                        if preferred_date and dt_pt.strftime("%Y-%m-%d") != preferred_date:
                            continue
                        candidates.append(dt_pt)
                    except Exception:
                        try:
                            from datetime import datetime as _dt
                            dt_naive = _dt.strptime(f"{date_part} {time_part[:8]}", "%Y-%m-%d %H:%M:%S")
                            candidates.append(dt_naive)
                        except Exception:
                            continue
            if candidates:
                chosen = min(candidates)
                return chosen.strftime("%Y-%m-%d"), chosen.strftime("%Y-%m-%d %H:%M:%S")
        if preferred_date:
            single_on_date = []
            for ev in events:
                commence = ev.get("commence_time")
                if not commence or "T" not in commence:
                    continue
                date_part = commence.split("T", 1)[0]
                if date_part == preferred_date:
                    single_on_date.append(ev)
            if len(single_on_date) == 1:
                commence = single_on_date[0].get("commence_time")
                date_part, time_part = commence.split("T", 1)
                time_part = time_part.replace("Z", "")
                try:
                    from datetime import datetime as _dt
                    from zoneinfo import ZoneInfo as _ZI
                    dt_utc = _dt.strptime(f"{date_part} {time_part[:8]}", "%Y-%m-%d %H:%M:%S").replace(tzinfo=_ZI("UTC"))
                    dt_pt = dt_utc.astimezone(_ZI("America/Los_Angeles"))
                    return dt_pt.strftime("%Y-%m-%d"), dt_pt.strftime("%Y-%m-%d %H:%M:%S")
                except Exception:
                    return date_part, f"{date_part} {time_part[:8]}"
        try:
            scores_url = f"https://api.the-odds-api.com/v4/sports/{sport_key}/scores"
            score_params = {"apiKey": api_key, "daysFrom": 3}
            scores_resp = requests.get(scores_url, params=score_params, timeout=10)
            if scores_resp.status_code != 429:
                scores_resp.raise_for_status()
                scores = scores_resp.json() or []
                for sc in scores:
                    home = sc.get("home_team")
                    away = sc.get("away_team")
                    if home and away and team_a and team_b:
                        pair_match = (
                            (_teams_match(team_a, home) and _teams_match(team_b, away)) or
                            (_teams_match(team_a, away) and _teams_match(team_b, home))
                        )
                        if not pair_match:
                            continue
                        commence = sc.get("commence_time")
                        if not commence or "T" not in commence:
                            continue
                        date_part, time_part = commence.split("T", 1)
                        time_part = time_part.replace("Z", "")
                        try:
                            from datetime import datetime as _dt
                            from zoneinfo import ZoneInfo as _ZI
                            dt_utc = _dt.strptime(f"{date_part} {time_part[:8]}", "%Y-%m-%d %H:%M:%S").replace(tzinfo=_ZI("UTC"))
                            dt_pt = dt_utc.astimezone(_ZI("America/Los_Angeles"))
                            if preferred_date and dt_pt.strftime("%Y-%m-%d") != preferred_date:
                                continue
                            return dt_pt.strftime("%Y-%m-%d"), dt_pt.strftime("%Y-%m-%d %H:%M:%S")
                        except Exception:
                            return date_part, f"{date_part} {time_part[:8]}"
        except Exception as se:
            print(f"[WARNING] Odds API scores backup failed: {se}")
        return None, None
    except Exception as e:
        print(f"[ERROR] Odds API lookup failed: {e}")
        return None, None


def refresh_todays_odds_and_models():
    """Fire-and-forget odds refresh and model run for today's events."""
    try:
        from datetime import datetime as _dt
        from zoneinfo import ZoneInfo as _ZI
        from collections import defaultdict
        from data_science_modules.planet_scale_port import get_connection
        from modules.HandiCapperAccuracyModel import main_model
        
        try:
            today_pt = _dt.now(_ZI("America/Los_Angeles")).strftime("%Y-%m-%d")
        except Exception:
            today_pt = datetime.now().strftime("%Y-%m-%d")

        # Load today's events with metadata
        rows = []
        try:
            conn = get_connection()
            cur = conn.cursor()
            cur.execute(
                """
                SELECT event_id, league, team_a, team_b, pick_type, stat_type, stat_threshold
                FROM events
                WHERE event_date = %s
                """,
                (today_pt,)
            )
            rows = cur.fetchall() or []
            conn.close()
        except Exception as e:
            print(f"[WARNING] Query of today's events failed: {e}")
            return

        if not rows:
            return

        # Group by league for fewer /events calls
        by_league = defaultdict(list)
        for (eid, league, ta, tb, ptype, stype, sth) in rows:
            by_league[str(league or '')].append((eid, league, ta or '', tb or '', str(ptype or ''), str(stype or ''), sth))

        api_key = os.getenv("ODDS_API_KEY")
        session = requests.Session()

        def implied_from_decimal(price: float) -> float:
            try:
                if price and price > 1.0:
                    return round(1.0 / float(price), 4)
            except Exception:
                pass
            return 0.5

        for league_key, items in by_league.items():
            sport_key = resolve_sport_key(league_key)
            if not sport_key or not api_key:
                continue

            # Fetch today's odds-api events for this sport
            events_url = f"https://api.the-odds-api.com/v4/sports/{sport_key}/events"
            params = {"apiKey": api_key}
            try:
                # Narrow to today window in UTC
                try:
                    dt_start_pt = _dt.strptime(f"{today_pt} 00:00:00", "%Y-%m-%d %H:%M:%S").replace(tzinfo=_ZI("America/Los_Angeles"))
                    dt_end_pt = _dt.strptime(f"{today_pt} 23:59:59", "%Y-%m-%d %H:%M:%S").replace(tzinfo=_ZI("America/Los_Angeles"))
                    params["commenceTimeFrom"] = dt_start_pt.astimezone(_ZI("UTC")).strftime("%Y-%m-%dT%H:%M:%SZ")
                    params["commenceTimeTo"] = dt_end_pt.astimezone(_ZI("UTC")).strftime("%Y-%m-%dT%H:%M:%SZ")
                except Exception:
                    pass

                ev_resp = session.get(events_url, params=params, timeout=10)
                if ev_resp.status_code == 429:
                    print("[WARNING] Odds API events rate-limited")
                    continue
                ev_resp.raise_for_status()
                odds_events = ev_resp.json() or []
            except Exception as e:
                print(f"[WARNING] Failed to fetch Odds API events for {league_key}: {e}")
                continue

            # Index odds events for lookup
            def match_event(ta: str, tb: str):
                for ev in odds_events:
                    home = ev.get("home_team") or ""
                    away = ev.get("away_team") or ""
                    if _teams_match(ta, home) and _teams_match(tb, away):
                        return ev
                    if _teams_match(ta, away) and _teams_match(tb, home):
                        return ev
                return None

            for (eid, _lg, team_a, team_b, pick_type, stat_type, stat_thr) in items:
                try:
                    matched = match_event(team_a, team_b)
                    if not matched:
                        continue
                    ev_id = matched.get("id")
                    if not ev_id:
                        continue

                    # Determine markets to request
                    markets = None
                    selector = None
                    if pick_type == "MoneyLine":
                        markets = "h2h"
                        selector = "h2h"
                    elif pick_type == "Spread":
                        markets = "spreads"
                        selector = "spreads"
                    elif pick_type == "Total":
                        markets = "totals"
                        selector = "totals"
                    else:
                        # Skip complex player props in this refresh
                        continue

                    odds_url = f"https://api.the-odds-api.com/v4/sports/{sport_key}/events/{ev_id}/odds"
                    oparams = {
                        "apiKey": api_key,
                        "regions": "us",
                        "oddsFormat": "decimal",
                        "markets": markets,
                        "dateFormat": "iso"
                    }
                    oresp = session.get(odds_url, params=oparams, timeout=10)
                    if oresp.status_code == 404:
                        continue
                    if oresp.status_code == 429:
                        print("[WARNING] Odds API odds rate-limited")
                        continue
                    oresp.raise_for_status()
                    odds_doc = oresp.json()

                    # Choose a bookmaker (prefer draftkings if present)
                    bookmakers = odds_doc.get("bookmakers", [])
                    chosen = None
                    for bk in bookmakers:
                        if bk.get("key") == "draftkings":
                            chosen = bk
                            break
                    if not chosen and bookmakers:
                        chosen = bookmakers[0]
                    if not chosen:
                        continue

                    markets_list = chosen.get("markets", [])
                    target_market = None
                    for mk in markets_list:
                        if mk.get("key") == selector:
                            target_market = mk
                            break
                    if not target_market:
                        continue

                    outcomes = target_market.get("outcomes", [])
                    crowd_prob = None

                    if selector == "h2h":
                        # Map price to the event's team_a if possible; otherwise take favorite prob
                        prob_by_name = {}
                        for oc in outcomes:
                            nm = (oc.get("name") or oc.get("description") or "").strip()
                            price = float(oc.get("price")) if oc.get("price") is not None else None
                            if price:
                                prob_by_name[nm] = implied_from_decimal(price)
                        # Try team_a exact/fuzzy match
                        if prob_by_name:
                            chosen_prob = None
                            for nm, pr in prob_by_name.items():
                                if _teams_match(team_a, nm):
                                    chosen_prob = pr
                                    break
                            if chosen_prob is None:
                                chosen_prob = max(prob_by_name.values())
                            crowd_prob = chosen_prob
                    elif selector == "totals":
                        # Find matching threshold if provided
                        best_over = None
                        for oc in outcomes:
                            nm = (oc.get("name") or oc.get("description") or "").strip().lower()
                            point = oc.get("point")
                            price = float(oc.get("price")) if oc.get("price") is not None else None
                            if price is None:
                                continue
                            if stat_thr is not None and point is not None:
                                try:
                                    if abs(float(point) - float(stat_thr)) > 1e-6:
                                        continue
                                except Exception:
                                    pass
                            if nm.startswith("over"):
                                best_over = implied_from_decimal(price)
                        # If our event indicates Under, invert over prob
                        if best_over is not None:
                            if str(stat_type).lower() == "under":
                                crowd_prob = 1.0 - best_over
                            else:
                                crowd_prob = best_over
                    elif selector == "spreads":
                        # Spreads: choose probability for team_a at the listed spread if matches
                        chosen_prob = None
                        for oc in outcomes:
                            nm = (oc.get("name") or "").strip()
                            price = float(oc.get("price")) if oc.get("price") is not None else None
                            if price is None:
                                continue
                            if _teams_match(team_a, nm):
                                chosen_prob = implied_from_decimal(price)
                                break
                        crowd_prob = chosen_prob

                    if crowd_prob is None:
                        continue

                    # Upsert crowd_probability
                    try:
                        conn2 = get_connection()
                        cur2 = conn2.cursor()
                        cur2.execute(
                            """
                            INSERT INTO crowd_predictions (event_id, crowd_probability)
                            VALUES (%s, %s)
                            ON DUPLICATE KEY UPDATE crowd_probability = VALUES(crowd_probability)
                            """,
                            (eid, float(crowd_prob))
                        )
                        conn2.commit()
                        conn2.close()
                    except Exception as we:
                        print(f"[WARNING] Failed to upsert crowd_probability for {eid}: {we}")

                    # Run model
                    try:
                        print(f"🔁 Running model with refreshed odds for {eid}")
                        main_model(eid)
                    except Exception as me:
                        print(f"[WARNING] Model refresh failed for {eid}: {me}")
                        
                except Exception as each:
                    print(f"[WARNING] Refresh pass failed for {eid}: {each}")
                    
    except Exception as bg:
        print(f"[WARNING] Background odds refresh failed: {bg}")

