import { useReducer, useEffect, useCallback } from "react";
import type { ManualPick } from "../types/manualPick";
import { fetchExperts, submitManualPick } from "../services/manualPickService";

interface FormState {
  player: string;
  statType: string;
  line: string;
  odds: string;
  expert: string;
  sportsbook: string;
  experts: string[];
  isLoading: boolean;
  error: string | null;
}

type FormAction =
  | { type: "SET_FIELD"; field: keyof FormState; value: string }
  | { type: "SET_EXPERTS"; experts: string[] }
  | { type: "SET_LOADING"; isLoading: boolean }
  | { type: "SET_ERROR"; error: string | null }
  | { type: "RESET_FORM" };

const initialState: FormState = {
  player: "",
  statType: "",
  line: "",
  odds: "",
  expert: "",
  sportsbook: "",
  experts: [],
  isLoading: false,
  error: null,
};

function formReducer(state: FormState, action: FormAction): FormState {
  switch (action.type) {
    case "SET_FIELD":
      return { ...state, [action.field]: action.value };
    case "SET_EXPERTS":
      return { ...state, experts: action.experts };
    case "SET_LOADING":
      return { ...state, isLoading: action.isLoading };
    case "SET_ERROR":
      return { ...state, error: action.error };
    case "RESET_FORM":
      return initialState;
    default:
      return state;
  }
}

const useManualPickForm = (date: string) => {
  const [state, dispatch] = useReducer(formReducer, initialState);

  // Fetch experts when date changes
  useEffect(() => {
    const loadExperts = async () => {
      dispatch({ type: "SET_LOADING", isLoading: true });
      try {
        const experts = await fetchExperts(date);
        dispatch({ type: "SET_EXPERTS", experts: experts.map(e => e.name) });
      } catch (error) {
        dispatch({ type: "SET_ERROR", error: (error as Error).message });
      } finally {
        dispatch({ type: "SET_LOADING", isLoading: false });
      }
    };

    loadExperts();
  }, [date]);

  const handleChange = useCallback((field: keyof FormState, value: string) => {
    dispatch({ type: "SET_FIELD", field, value });
  }, []);

  const handleSubmit = useCallback(async () => {
    dispatch({ type: "SET_LOADING", isLoading: true });
    try {
      const pick: ManualPick = {
        player: state.player,
        statType: state.statType,
        line: parseFloat(state.line),
        odds: parseInt(state.odds, 10),
        expert: state.expert,
        sportsbook: state.sportsbook,
        date,
      };
      
      await submitManualPick({ ...pick, admin_password: "SECURE_PASSWORD" });
      dispatch({ type: "RESET_FORM" });
      return true;
    } catch (error) {
      dispatch({ type: "SET_ERROR", error: (error as Error).message });
      return false;
    } finally {
      dispatch({ type: "SET_LOADING", isLoading: false });
    }
  }, [state, date]);

  return {
    ...state,
    handleChange,
    handleSubmit,
  };
};

export default useManualPickForm;