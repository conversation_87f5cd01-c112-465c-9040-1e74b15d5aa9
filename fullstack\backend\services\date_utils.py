# fullstack/backend/services/date_utils.py
import os
from datetime import datetime
try:
    from zoneinfo import ZoneInfo
except Exception:
    ZoneInfo = None  # Will fallback to naive datetime


def get_current_california_date() -> str:
    """Return current date string in YYYY-MM-DD for America/Los_Angeles.
    Mirrors logic in app.py, with safe fallbacks.
    """
    try:
        if ZoneInfo is None:
            return datetime.now().strftime("%Y-%m-%d")
        california_time = datetime.now(ZoneInfo("America/Los_Angeles"))
        return california_time.strftime("%Y-%m-%d")
    except Exception as e:
        print(f"❌ Error getting California date, using fallback: {e}")
        return datetime.now().strftime("%Y-%m-%d")


def validate_date_format(date_string: str):
    """
    Comprehensive date validation and sanitization.
    Returns (is_valid: bool, sanitized: str | None, error_message: str | None)
    """
    import re
    from datetime import datetime, date

    if not date_string:
        return False, None, "Date is required"
    if not isinstance(date_string, str):
        return False, None, "Date must be a string"

    sanitized = date_string.strip()

    if not re.match(r'^\d{4}-\d{2}-\d{2}$', sanitized):
        return False, None, f"Invalid date format. Please use YYYY-MM-DD format (e.g., {get_current_california_date()})"

    try:
        parsed_date = datetime.strptime(sanitized, "%Y-%m-%d")
        year = parsed_date.year
        if year < 2020 or year > 2030:
            return False, None, "Date must be between 2020 and 2030"
        today = date.today()
        date_obj = parsed_date.date()
        days_diff = (date_obj - today).days
        if days_diff < -365:
            return False, None, "Date cannot be more than 1 year in the past"
        if days_diff > 365:
            return False, None, "Date cannot be more than 1 year in the future"
        return True, sanitized, None
    except ValueError as e:
        return False, None, f"Invalid date: {str(e)}"

