# fullstack/backend/services/confidence.py
from typing import Any
from modules.HandiCapperAccuracyModel import main_model


def confidence_score(decimal_odds: float, expert_confidence: Any, expert_accuracy: Any) -> float:
    implied_prob = 1 / decimal_odds
    try:
        c = float(expert_confidence) / 100.0
    except Exception:
        c = 0.0
    try:
        a = float(expert_accuracy) / 100.0
    except Exception:
        a = 0.0
    score = 100 * (a * c + (1 - a) * implied_prob)
    return score


def calculate_dynamic_confidence(event_id: str, expert_confidence) -> float:
    try:
        try:
            expert_confidence = float(expert_confidence)
        except Exception:
            expert_confidence = 0.0

        prediction_result = main_model(event_id)
        model_prob = prediction_result.get("combined_prob")
        if model_prob is None:
            model_prob = prediction_result.get("bayesian_prob", 0.5)
        try:
            model_prob = float(model_prob)
        except Exception:
            model_prob = 0.5

        difference = model_prob - expert_confidence
        clamped_difference = max(min(difference, 0.25), -0.25)
        scaled_confidence = ((clamped_difference + 0.25) / 0.5) * 100
        scaled_confidence = max(0.0, min(100.0, scaled_confidence))
        return round(scaled_confidence, 2)
    except Exception as e:
        print(f"[ERROR] Dynamic confidence calculation failed for {event_id}: {e}")
        try:
            return float(expert_confidence) * 100.0 if float(expert_confidence) <= 1.0 else float(expert_confidence)
        except Exception:
            return 75.0


def calculate_dynamic_confidence_vs_expert(event_id: str, expert_confidence_percentage: float) -> float:
    """
    Calculate dynamic confidence based on ML model predictions vs expert confidence.
    
    Args:
        event_id (str): The event ID to get ML predictions for
        expert_confidence_percentage (float): Expert confidence as percentage (0-100)
    
    Returns:
        float: Scaled confidence value (0-100)
    """
    try:
        expert_confidence_decimal = expert_confidence_percentage / 100.0
        
        # Call ML model to get predictions
        prediction_result = main_model(event_id)
        
        # Extract model probability (prefer combined_prob, fallback to other probabilities)
        model_prob = prediction_result.get("combined_prob")
        if model_prob is None:
            model_prob = prediction_result.get("bayesian_prob", 0.5)
        
        # Ensure model_prob is float
        try:
            model_prob = float(model_prob)
        except Exception:
            model_prob = 0.5
        
        # Calculate difference: model_prob - expert_confidence
        difference = model_prob - expert_confidence_decimal
        
        # Clamp difference to [-0.25, 0.25] range for mapping bounds (±25 percentage points)
        clamped_difference = max(min(difference, 0.25), -0.25)
        
        # Scale clamped difference to 0-100% where -25% = 0% and 25% = 100%
        scaled_confidence = ((clamped_difference + 0.25) / 0.5) * 100
        # Guard rails
        scaled_confidence = max(0.0, min(100.0, scaled_confidence))
        
        return round(scaled_confidence, 2)
        
    except Exception as e:
        print(f"[ERROR] Dynamic confidence calculation failed for {event_id}: {e}")
        # Fallback to original expert confidence
        return float(expert_confidence_percentage) if expert_confidence_percentage else 50.0

