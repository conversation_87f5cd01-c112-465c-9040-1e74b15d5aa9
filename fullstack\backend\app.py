import os
import sys
import json
import random
import sqlite3
import requests
from datetime import datetime
try:
    from zoneinfo import ZoneInfo
except ImportError:
    # Fallback for Python < 3.9
    from datetime import timezone, timedelta

    class ZoneInfo:

        def __init__(self, name):
            if name == "America/Los_Angeles":
                # PST is UTC-8, PDT is UTC-7
                # This is a simplified implementation - for production use pytz
                self.offset = timedelta(hours=-8)  # Simplified as PST
            else:
                self.offset = timedelta(0)

        def utcoffset(self, dt):
            return self.offset


# Ensure the repo root is importable when running this file directly (python app.py)
try:
    _this_dir = os.path.dirname(os.path.abspath(__file__))
    _repo_root = os.path.abspath(os.path.join(_this_dir, os.pardir, os.pardir))
    if _repo_root not in sys.path:
        sys.path.insert(0, _repo_root)
except Exception:
    pass


from itertools import combinations
from flask import Flask, request, jsonify, render_template, send_from_directory
from flask_cors import CORS
from typing import Dict, Optional, Tuple
from functools import wraps
from fullstack.backend.config import get_config, load_env
from fullstack.backend.routes import register_routes
from werkzeug.exceptions import BadRequest
import re

# Application factory - load env and apply config when creating the app
def create_app():
    # Load environment variables from .env unless explicitly skipped
    if os.getenv("SKIP_DOTENV") != "1":
        try:
            load_env()
        except Exception:
            # Non-fatal: continue even if dotenv loading fails
            pass

    # Instantiate Flask app (kept identical to previous module-level creation)
    app = Flask(__name__,
                static_folder=os.path.join(os.path.dirname(__file__),
                                           "../frontend/dist"),
                static_url_path="/")

    # Apply CORS and any configuration provided by get_config()
    CORS(app, origins=["http://localhost:5173", "http://localhost:5174"])
    try:
        cfg = get_config()
        if isinstance(cfg, dict):
            app.config.update(cfg)
    except Exception:
        # Best-effort: do not fail app creation for config issues
        pass

    # Allow external route registration (no-op by default)
    try:
        register_routes(app)
    except Exception:
        pass

    return app

from modules.ppObjects import Pick, BoostPromo, ProtectedPromo
from modules.RROptimizer import analyze_all_splits
from modules.HandiCapperAccuracyModel import main_model
import threading
from modules.HandiCapperAccuracyModel.util.PlotUtils import PlotUtils
try:
    from apscheduler.schedulers.background import BackgroundScheduler
    from apscheduler.triggers.cron import CronTrigger
except Exception:
    BackgroundScheduler = None  # type: ignore
    CronTrigger = None  # type: ignore
from data_science_modules.planet_scale_port import get_todays_events_with_handicappers, submit_event, generate_event_id, generate_admin_event_id, get_connection, get_dict_connection, normalize_stat_type_for_storage
try:
    from data_science_modules.OddsFetcher import get_mlb_events, find_event_commence_time
    print("[INFO] Imported OddsFetcher helpers")
except Exception as e:
    print(f"[WARNING] OddsFetcher helpers unavailable: {e}")
    get_mlb_events = None
    find_event_commence_time = None
try:
    from services.data_validation import validate_and_clean_pick_data, clean_team_name
    print("[INFO] Successfully imported data validation service")
except ImportError as e:
    print(f"[ERROR] Failed to import data validation service: {e}")

    def validate_and_clean_pick_data(pick_data):
        errors = []
        cleaned_data = pick_data.copy()
        optional_fields = ['player_name', 'stat_type', 'stat_threshold']
        for field in optional_fields:
            if cleaned_data.get(field) == "" or cleaned_data.get(field) == 0:
                cleaned_data[field] = None
        if cleaned_data.get('pick_type') in ['MoneyLine', 'Spread', 'Total']:
            if cleaned_data.get('player_name') and cleaned_data.get('player_name') != "":
                errors.append("MoneyLine/Spread/Total picks should not have player_name")
            cleaned_data['player_name'] = None
            cleaned_data['stat_type'] = None
            cleaned_data['stat_threshold'] = None
        return cleaned_data, errors

    def clean_team_name(team_name: str) -> str:
        return (team_name or "").strip()


# Module-level app for backwards compatibility with existing imports
app = create_app()


# Date management moved to state.py
from fullstack.backend.state import get_current_california_date, get_default_date, update_default_date, auto_update_enabled

# Date validation moved to services/date_utils.py
from fullstack.backend.services.date_utils import validate_date_format

# Event utility functions moved to services/events_service.py
from fullstack.backend.services.events_service import _fetch_unique_games_by_date


# Event utility functions moved to services/events_service.py


# Global object lists
pick_objects = []
boost_promo_objects = []
protected_promo_objects = []
next_id = 1
user_accuracy = 0.0

_WORKSPACE_ROOT = os.path.dirname(os.path.abspath(__file__))
if _WORKSPACE_ROOT not in sys.path:
    sys.path.append(_WORKSPACE_ROOT)

_PROJECT_ROOT = os.path.dirname(_WORKSPACE_ROOT)
if _PROJECT_ROOT not in sys.path:
    sys.path.append(_PROJECT_ROOT)

DB_PATH = os.path.join(_WORKSPACE_ROOT, "data", "pick_confidence.db")
_DB_DIR = os.path.dirname(DB_PATH)
if not os.path.exists(_DB_DIR):
    os.makedirs(_DB_DIR)
    print(f"Created data directory: {_DB_DIR}")

# Note: PlanetScale doesn't allow DDL modifications, so we rely on application-level duplicate prevention
print(
    "[INFO] Using application-level duplicate prevention for expert_predictions"
)

origin_profiles = {
    "ChalkBoardPI": 0.80,
    "HarryLock": 0.71,
    "DanGamblePOD": 0.78,
    "DanGambleAIEdge": 90.0,
    "GameScript": 0.80,
    "Winible": 0.83,
    "DoberMan": 0.76,
    "JoshMiller": 0.60,
    "Me": lambda: user_accuracy
}


# Confidence calculation functions moved to services/confidence.py
from fullstack.backend.services.confidence import confidence_score, calculate_dynamic_confidence

# Event ID correction functions moved to services/events_service.py
from fullstack.backend.services.events_service import correct_event_id_format

# Round robin utility function
from itertools import combinations
def generate_round_robin_subparlays(pick_list, subgroup_size):
    """Returns a list of subparlays (combinations) of picks."""
    return [list(combo) for combo in combinations(pick_list, subgroup_size)]


def get_all_objects():
    return pick_objects, boost_promo_objects, protected_promo_objects


# Odds API integration for accurate start times
# Odds API functions moved to services/odds_api.py
from fullstack.backend.services.odds_api import resolve_sport_key, get_event_start_from_odds_api


# Team utility functions moved to services/teams.py
from fullstack.backend.services.teams import _normalize_team_name, _teams_match, team_to_nickname


def get_event_start_from_odds_api(league: str,
                                  team_a: Optional[str],
                                  team_b: Optional[str],
                                  preferred_date: Optional[str]) -> Tuple[Optional[str], Optional[str]]:
    try:
        api_key = os.getenv("ODDS_API_KEY")
        if not api_key:
            print("[WARNING] ODDS_API_KEY not set; skipping Odds API lookup")
            return None, None

        sport_key = resolve_sport_key(league)
        if not sport_key:
            print(f"[WARNING] Unknown league for Odds API mapping (after dynamic lookup): {league}")
            return None, None

        base_url = f"https://api.the-odds-api.com/v4/sports/{sport_key}/events"
        params = {"apiKey": api_key}

        if preferred_date:
            try:
                from datetime import datetime as _dt
                from zoneinfo import ZoneInfo as _ZI
                dt_start_pt = _dt.strptime(f"{preferred_date} 00:00:00", "%Y-%m-%d %H:%M:%S").replace(tzinfo=_ZI("America/Los_Angeles"))
                dt_end_pt = _dt.strptime(f"{preferred_date} 23:59:59", "%Y-%m-%d %H:%M:%S").replace(tzinfo=_ZI("America/Los_Angeles"))
                params["commenceTimeFrom"] = dt_start_pt.astimezone(_ZI("UTC")).strftime("%Y-%m-%dT%H:%M:%SZ")
                params["commenceTimeTo"] = dt_end_pt.astimezone(_ZI("UTC")).strftime("%Y-%m-%dT%H:%M:%SZ")
            except Exception:
                pass

        resp = requests.get(base_url, params=params, timeout=10)
        if resp.status_code == 429:
            print("[WARNING] Odds API rate-limited (429)")
            return None, None
        resp.raise_for_status()
        events = resp.json() or []

        if not events and preferred_date:
            try:
                resp2 = requests.get(base_url, params={"apiKey": api_key}, timeout=10)
                resp2.raise_for_status()
                events = resp2.json() or []
            except Exception as e2:
                print(f"[WARNING] Secondary Odds API fetch failed: {e2}")

        for ev in events:
            home = ev.get("home_team")
            away = ev.get("away_team")
            if home and away and team_a and team_b:
                pair_match = (
                    (_teams_match(team_a, home) and _teams_match(team_b, away)) or
                    (_teams_match(team_a, away) and _teams_match(team_b, home))
                )
                if not pair_match:
                    continue
                commence = ev.get("commence_time")
                if not commence or "T" not in commence:
                    continue
                # commence is UTC (e.g., 2025-08-08T02:00:00Z). Convert to PT.
                date_part, time_part = commence.split("T", 1)
                time_part = time_part.replace("Z", "")
                try:
                    from datetime import datetime as _dt
                    from zoneinfo import ZoneInfo as _ZI
                    dt_utc = _dt.strptime(f"{date_part} {time_part[:8]}", "%Y-%m-%d %H:%M:%S").replace(tzinfo=_ZI("UTC"))
                    dt_pt = dt_utc.astimezone(_ZI("America/Los_Angeles"))
                    if preferred_date and dt_pt.strftime("%Y-%m-%d") != preferred_date:
                        continue
                    return dt_pt.strftime("%Y-%m-%d"), dt_pt.strftime("%Y-%m-%d %H:%M:%S")
                except Exception:
                    # Fallback: return naive values
                    return date_part, f"{date_part} {time_part[:8]}"

        # Secondary attempt: handle cases where only one team is known
        known_team = None
        if team_a and not team_b:
            known_team = team_a
        elif team_b and not team_a:
            known_team = team_b
        elif team_a and (str(team_b).strip().lower() in ("unknown", "null", "none", "")):
            known_team = team_a
        elif team_b and (str(team_a).strip().lower() in ("unknown", "null", "none", "")):
            known_team = team_b

        if known_team:
            candidates = []
            for ev in events:
                home = ev.get("home_team")
                away = ev.get("away_team")
                if not home or not away:
                    continue
                if _teams_match(known_team, home) or _teams_match(known_team, away):
                    commence = ev.get("commence_time")
                    if not commence or "T" not in commence:
                        continue
                    date_part, time_part = commence.split("T", 1)
                    time_part = time_part.replace("Z", "")
                    try:
                        from datetime import datetime as _dt
                        from zoneinfo import ZoneInfo as _ZI
                        dt_utc = _dt.strptime(f"{date_part} {time_part[:8]}", "%Y-%m-%d %H:%M:%S").replace(tzinfo=_ZI("UTC"))
                        dt_pt = dt_utc.astimezone(_ZI("America/Los_Angeles"))
                        if preferred_date and dt_pt.strftime("%Y-%m-%d") != preferred_date:
                            continue
                        candidates.append(dt_pt)
                    except Exception:
                        try:
                            from datetime import datetime as _dt
                            dt_naive = _dt.strptime(f"{date_part} {time_part[:8]}", "%Y-%m-%d %H:%M:%S")
                            candidates.append(dt_naive)
                        except Exception:
                            continue
            if candidates:
                chosen = min(candidates)
                return chosen.strftime("%Y-%m-%d"), chosen.strftime("%Y-%m-%d %H:%M:%S")

        # If we couldn't match on teams, but we have a preferred_date, and exactly one event falls on that date,
        # use that commence_time as a best-effort fallback.
        if preferred_date:
            single_on_date = []
            for ev in events:
                commence = ev.get("commence_time")
                if not commence or "T" not in commence:
                    continue
                date_part = commence.split("T", 1)[0]
                if date_part == preferred_date:
                    single_on_date.append(ev)
            if len(single_on_date) == 1:
                commence = single_on_date[0].get("commence_time")
                date_part, time_part = commence.split("T", 1)
                time_part = time_part.replace("Z", "")
                try:
                    from datetime import datetime as _dt
                    from zoneinfo import ZoneInfo as _ZI
                    dt_utc = _dt.strptime(f"{date_part} {time_part[:8]}", "%Y-%m-%d %H:%M:%S").replace(tzinfo=_ZI("UTC"))
                    dt_pt = dt_utc.astimezone(_ZI("America/Los_Angeles"))
                    return dt_pt.strftime("%Y-%m-%d"), dt_pt.strftime("%Y-%m-%d %H:%M:%S")
                except Exception:
                    return date_part, f"{date_part} {time_part[:8]}"

        # Backup: try scores endpoint (includes commence_time as well)
        try:
            scores_url = f"https://api.the-odds-api.com/v4/sports/{sport_key}/scores"
            score_params = {"apiKey": api_key, "daysFrom": 3}
            scores_resp = requests.get(scores_url, params=score_params, timeout=10)
            if scores_resp.status_code != 429:
                scores_resp.raise_for_status()
                scores = scores_resp.json() or []
                for sc in scores:
                    home = sc.get("home_team")
                    away = sc.get("away_team")
                    if home and away and team_a and team_b:
                        pair_match = (
                            (_teams_match(team_a, home) and _teams_match(team_b, away)) or
                            (_teams_match(team_a, away) and _teams_match(team_b, home))
                        )
                        if not pair_match:
                            continue
                        commence = sc.get("commence_time")
                        if not commence or "T" not in commence:
                            continue
                        date_part, time_part = commence.split("T", 1)
                        time_part = time_part.replace("Z", "")
                        try:
                            from datetime import datetime as _dt
                            from zoneinfo import ZoneInfo as _ZI
                            dt_utc = _dt.strptime(f"{date_part} {time_part[:8]}", "%Y-%m-%d %H:%M:%S").replace(tzinfo=_ZI("UTC"))
                            dt_pt = dt_utc.astimezone(_ZI("America/Los_Angeles"))
                            if preferred_date and dt_pt.strftime("%Y-%m-%d") != preferred_date:
                                continue
                            return dt_pt.strftime("%Y-%m-%d"), dt_pt.strftime("%Y-%m-%d %H:%M:%S")
                        except Exception:
                            return date_part, f"{date_part} {time_part[:8]}"
        except Exception as se:
            print(f"[WARNING] Odds API scores backup failed: {se}")

        return None, None
    except Exception as e:
        print(f"[ERROR] Odds API lookup failed: {e}")
        return None, None


# Helper: fill missing teams for MoneyLine/Spread using TheOdds API
def _is_unknown_team(value: Optional[str]) -> bool:
    v = (value or "").strip().lower()
    return v in ("", "unknown", "null", "none", "other")


def _format_nickname(name: Optional[str]) -> str:
    try:
        nm = (name or "").strip()
        from services.data_validation import clean_team_name as _clean
        cleaned = _clean(nm)
        return cleaned if cleaned else "Unknown"
    except Exception:
        return (name or "Unknown").strip() or "Unknown"


def fill_missing_teams_with_odds_api(league: str,
                                     event_date: Optional[str],
                                     team_a: Optional[str],
                                     team_b: Optional[str]) -> Tuple[str, str]:
    try:
        # If both are known, just normalize to nicknames
        if not _is_unknown_team(team_a) and not _is_unknown_team(team_b):
            return _format_nickname(team_a), _format_nickname(team_b)

        api_key = os.getenv("ODDS_API_KEY")
        sport_key = resolve_sport_key(league)
        if not api_key or not sport_key:
            return _format_nickname(team_a), _format_nickname(team_b)

        params = {"apiKey": api_key}
        if event_date:
            try:
                from datetime import datetime as _dt
                from zoneinfo import ZoneInfo as _ZI
                dt_start_pt = _dt.strptime(f"{event_date} 00:00:00", "%Y-%m-%d %H:%M:%S").replace(tzinfo=_ZI("America/Los_Angeles"))
                dt_end_pt = _dt.strptime(f"{event_date} 23:59:59", "%Y-%m-%d %H:%M:%S").replace(tzinfo=_ZI("America/Los_Angeles"))
                params["commenceTimeFrom"] = dt_start_pt.astimezone(_ZI("UTC")).strftime("%Y-%m-%dT%H:%M:%SZ")
                params["commenceTimeTo"] = dt_end_pt.astimezone(_ZI("UTC")).strftime("%Y-%m-%dT%H:%M:%SZ")
            except Exception:
                pass

        url = f"https://api.the-odds-api.com/v4/sports/{sport_key}/events"
        resp = requests.get(url, params=params, timeout=10)
        if resp.status_code == 429:
            return _format_nickname(team_a), _format_nickname(team_b)
        resp.raise_for_status()
        events = resp.json() or []

        known = None
        known_is_a = False
        if not _is_unknown_team(team_a):
            known = team_a
            known_is_a = True
        elif not _is_unknown_team(team_b):
            known = team_b
            known_is_a = False

        # If both unknown and only one event that day, fill both from that single event
        if known is None:
            if len(events) == 1:
                ev = events[0]
                ha = _format_nickname(ev.get("home_team"))
                aw = _format_nickname(ev.get("away_team"))
                return aw, ha  # default away vs home ordering similar to "TeamA,TeamB"
            return _format_nickname(team_a), _format_nickname(team_b)

        # Try to find the event containing the known team
        matches = []
        for ev in events:
            home = ev.get("home_team") or ""
            away = ev.get("away_team") or ""
            if _teams_match(known, home) or _teams_match(known, away):
                matches.append(ev)

        if not matches:
            try:
                scores_url = f"https://api.the-odds-api.com/v4/sports/{sport_key}/scores"
                score_params = {"apiKey": api_key, "daysFrom": 5}
                s_resp = requests.get(scores_url, params=score_params, timeout=10)
                if s_resp.status_code != 429:
                    s_resp.raise_for_status()
                    scores = s_resp.json() or []
                    for sc in scores:
                        home = sc.get("home_team") or ""
                        away = sc.get("away_team") or ""
                        if _teams_match(known, home) or _teams_match(known, away):
                            matches.append(sc)
            except Exception:
                pass

        if not matches:
            try:
                url2 = f"https://api.the-odds-api.com/v4/sports/{sport_key}/events"
                r2 = requests.get(url2, params={"apiKey": api_key}, timeout=10)
                if r2.status_code != 429:
                    r2.raise_for_status()
                    ev2 = r2.json() or []
                    for ev in ev2:
                        home = ev.get("home_team") or ""
                        away = ev.get("away_team") or ""
                        if not (home and away):
                            continue
                        if not (_teams_match(known, home) or _teams_match(known, away)):
                            continue
                        if event_date:
                            ts = ev.get("commence_time", "")
                            if "T" in ts:
                                dpart, tpart = ts.split("T", 1)
                                tpart = tpart.replace("Z", "")
                                try:
                                    from datetime import datetime as _dt
                                    from zoneinfo import ZoneInfo as _ZI
                                    dt_utc = _dt.strptime(f"{dpart} {tpart[:8]}", "%Y-%m-%d %H:%M:%S").replace(tzinfo=_ZI("UTC"))
                                    dt_pt = dt_utc.astimezone(_ZI("America/Los_Angeles"))
                                    if dt_pt.strftime("%Y-%m-%d") != event_date:
                                        continue
                                except Exception:
                                    if dpart != event_date:
                                        continue
                        matches.append(ev)
            except Exception:
                pass

        # Prefer the closest to midday if multiple on same day
        def _parse_iso(ts: str) -> Optional[datetime]:
            try:
                if ts.endswith("Z"):
                    ts = ts[:-1]
                return datetime.strptime(ts, "%Y-%m-%dT%H:%M:%S")
            except Exception:
                return None

        midday = None
        if event_date:
            try:
                midday = datetime.strptime(f"{event_date} 12:00:00", "%Y-%m-%d %H:%M:%S")
            except Exception:
                midday = None

        if len(matches) > 1 and midday is not None:
            matches.sort(key=lambda ev: abs(((_parse_iso(ev.get("commence_time", "")) or midday) - midday).total_seconds()))

        chosen = matches[0]
        home = _format_nickname(chosen.get("home_team"))
        away = _format_nickname(chosen.get("away_team"))

        if known_is_a:
            if _teams_match(team_a or "", home):
                return _format_nickname(team_a), away
            if _teams_match(team_a or "", away):
                return _format_nickname(team_a), home
            # If formatting changed the comparison, still return opponent safely
            return _format_nickname(team_a), (away if not _is_unknown_team(away) else home)
        else:
            if _teams_match(team_b or "", home):
                return away, _format_nickname(team_b)
            if _teams_match(team_b or "", away):
                return home, _format_nickname(team_b)
            return (away if not _is_unknown_team(away) else home), _format_nickname(team_b)
    except Exception:
        return _format_nickname(team_a), _format_nickname(team_b)


# First-pass DB inference on the same date before calling TheOdds API
def fill_missing_teams_from_db(league: str,
                               event_date: Optional[str],
                               team_a: Optional[str],
                               team_b: Optional[str]) -> Tuple[Optional[str], Optional[str]]:
    try:
        if not event_date or (not _is_unknown_team(team_a) and not _is_unknown_team(team_b)):
            return None, None

        try:
            from data_science_modules.planet_scale_port import get_connection
        except Exception:
            return None, None

        known = None
        known_is_a = False
        if not _is_unknown_team(team_a):
            known = (team_a or "").strip()
            known_is_a = True
        elif not _is_unknown_team(team_b):
            known = (team_b or "").strip()
            known_is_a = False
        else:
            return None, None

        known_lc = known.lower()
        placeholders = {"unknown", "null", "", "none", "over", "under"}

        def is_real(v: Optional[str]) -> bool:
            return (v or "").strip().lower() not in placeholders

        with get_connection() as conn:
            cur = conn.cursor()

            candidates: list[str] = []

            # When team_b is unknown and team_a is known → look for rows with same date/league & team_a==known
            if known_is_a:
                cur.execute(
                    """
                    SELECT team_b, COUNT(*) AS c
                    FROM events
                    WHERE event_date=%s AND league=%s AND LOWER(team_a)=LOWER(%s)
                    GROUP BY team_b
                    ORDER BY c DESC
                    LIMIT 5
                    """,
                    (event_date, league, known),
                )
                for tb, _cnt in cur.fetchall() or []:
                    if is_real(tb):
                        candidates.append(str(tb))

                # Also check reversed entries (opponent listed first elsewhere)
                cur.execute(
                    """
                    SELECT team_a, COUNT(*) AS c
                    FROM events
                    WHERE event_date=%s AND league=%s AND LOWER(team_b)=LOWER(%s)
                    GROUP BY team_a
                    ORDER BY c DESC
                    LIMIT 5
                    """,
                    (event_date, league, known),
                )
                for ta2, _cnt in cur.fetchall() or []:
                    if is_real(ta2) and not _teams_match(ta2, known):
                        candidates.append(str(ta2))

                if candidates:
                    opp = _format_nickname(candidates[0])
                    return _format_nickname(team_a), opp

            # When team_a is unknown and team_b is known
            else:
                cur.execute(
                    """
                    SELECT team_a, COUNT(*) AS c
                    FROM events
                    WHERE event_date=%s AND league=%s AND LOWER(team_b)=LOWER(%s)
                    GROUP BY team_a
                    ORDER BY c DESC
                    LIMIT 5
                    """,
                    (event_date, league, known),
                )
                for ta, _cnt in cur.fetchall() or []:
                    if is_real(ta):
                        candidates.append(str(ta))

                cur.execute(
                    """
                    SELECT team_b, COUNT(*) AS c
                    FROM events
                    WHERE event_date=%s AND league=%s AND LOWER(team_a)=LOWER(%s)
                    GROUP BY team_b
                    ORDER BY c DESC
                    LIMIT 5
                    """,
                    (event_date, league, known),
                )
                for tb2, _cnt in cur.fetchall() or []:
                    if is_real(tb2) and not _teams_match(tb2, known):
                        candidates.append(str(tb2))

                if candidates:
                    opp = _format_nickname(candidates[0])
                    return opp, _format_nickname(team_b)

        return None, None
    except Exception:
        return None, None


# Wrapper that tries DB inference first, then Odds API
def fill_missing_teams(league: str,
                       event_date: Optional[str],
                       team_a: Optional[str],
                       team_b: Optional[str]) -> Tuple[str, str]:
    # 1) Try DB inference on same date
    db_a, db_b = fill_missing_teams_from_db(league, event_date, team_a, team_b)
    if db_a is not None or db_b is not None:
        final_a = _format_nickname(db_a if db_a is not None else team_a)
        final_b = _format_nickname(db_b if db_b is not None else team_b)
        return final_a, final_b

    # 2) Fallback to Odds API
    a1, b1 = fill_missing_teams_with_odds_api(league, event_date, team_a, team_b)
    if not _is_unknown_team(a1) and not _is_unknown_team(b1):
        return a1, b1

    # 3) Final normalization pass: if either side is still Unknown/null, prefer known opponent from other side
    #    or keep original non-empty value if provided.
    normalized_a = a1 if not _is_unknown_team(a1) else _format_nickname(team_a)
    normalized_b = b1 if not _is_unknown_team(b1) else _format_nickname(team_b)
    return normalized_a, normalized_b


def _migrate_event_and_predictions(old_event_id: str,
                                   new_event_id: str,
                                   new_team_a: str,
                                   new_team_b: str) -> None:
    try:
        from data_science_modules.planet_scale_port import get_connection
    except Exception:
        return

    try:
        with get_connection() as conn:
            cur = conn.cursor()

            # If a canonical row already exists, upsert predictions; else we'll just rename IDs
            cur.execute("SELECT 1 FROM events WHERE event_id=%s", (new_event_id,))
            new_exists = cur.fetchone() is not None

            if new_exists:
                # Upsert expert_predictions
                cur.execute(
                    """
                    SELECT expert_name, prediction, confidence, team, league, game_date, stat_threshold, prediction_time
                    FROM expert_predictions WHERE event_id=%s
                    """,
                    (old_event_id,),
                )
                rows = cur.fetchall() or []
                for (expert_name, prediction, confidence, team, league, game_date, stat_threshold, prediction_time) in rows:
                    cur.execute(
                        """
                        INSERT INTO expert_predictions
                        (event_id, expert_name, prediction, confidence, team, league, game_date, stat_threshold, prediction_time)
                        VALUES (%s,%s,%s,%s,%s,%s,%s,%s,%s)
                        ON DUPLICATE KEY UPDATE
                          prediction=VALUES(prediction),
                          confidence=VALUES(confidence),
                          team=VALUES(team),
                          league=VALUES(league),
                          game_date=VALUES(game_date),
                          stat_threshold=VALUES(stat_threshold),
                          prediction_time=VALUES(prediction_time)
                        """,
                        (new_event_id, expert_name, prediction, confidence, team, league, game_date, stat_threshold, prediction_time),
                    )

                # Upsert crowd_predictions
                cur.execute("SELECT crowd_probability FROM crowd_predictions WHERE event_id=%s", (old_event_id,))
                cp = cur.fetchone()
                if cp:
                    (crowd_probability,) = cp
                    cur.execute(
                        """
                        INSERT INTO crowd_predictions (event_id, crowd_probability)
                        VALUES (%s, %s)
                        ON DUPLICATE KEY UPDATE crowd_probability=VALUES(crowd_probability)
                        """,
                        (new_event_id, crowd_probability),
                    )

                # Delete old rows and keep canonical row
                cur.execute("DELETE FROM expert_predictions WHERE event_id=%s", (old_event_id,))
                cur.execute("DELETE FROM crowd_predictions WHERE event_id=%s", (old_event_id,))
                cur.execute("DELETE FROM events WHERE event_id=%s", (old_event_id,))

            else:
                # Rename IDs across tables and update team names on the event
                cur.execute("UPDATE expert_predictions SET event_id=%s WHERE event_id=%s", (new_event_id, old_event_id))
                cur.execute("UPDATE crowd_predictions  SET event_id=%s WHERE event_id=%s", (new_event_id, old_event_id))
                cur.execute(
                    "UPDATE events SET event_id=%s, team_a=%s, team_b=%s WHERE event_id=%s",
                    (new_event_id, new_team_a, new_team_b, old_event_id),
                )

            # Ensure normalized team names on canonical row and fill blank expert team values
            cur.execute("UPDATE events SET team_a=%s, team_b=%s WHERE event_id=%s", (new_team_a, new_team_b, new_event_id))
            cur.execute(
                """
                UPDATE expert_predictions
                SET team=%s
                WHERE event_id=%s AND (team IS NULL OR team='' OR LOWER(team) IN ('unknown','null','none','other'))
                """,
                (new_team_a, new_event_id),
            )

            conn.commit()
    except Exception:
        # Best-effort cleanup; ignore failures in GET path
        pass

# Infer missing teams using time hint from the inbound payload
def infer_missing_teams_from_odds_api(league: str,
                                      preferred_date: Optional[str],
                                      raw_prediction_time: Optional[str]) -> Tuple[Optional[str], Optional[str]]:
    try:
        if not raw_prediction_time:
            return None, None
        api_key = os.getenv("ODDS_API_KEY")
        if not api_key:
            return None, None
        sport_key = resolve_sport_key(league)
        if not sport_key:
            return None, None

        # Expect formats like "YYYY-MM-DD HH:MM:SS America/Los_Angeles"
        parts = str(raw_prediction_time).split()
        if len(parts) < 3:
            return None, None
        date_str, time_str, tz_str = parts[0], parts[1], " ".join(parts[2:])
        try:
            local_tz = ZoneInfo(tz_str)
        except Exception:
            return None, None

        try:
            local_dt = datetime.strptime(f"{date_str} {time_str}", "%Y-%m-%d %H:%M:%S")
        except ValueError:
            try:
                local_dt = datetime.strptime(f"{date_str} {time_str}", "%Y-%m-%d %H:%M")
            except ValueError:
                return None, None

        # Localize and convert to UTC
        local_dt = local_dt.replace(tzinfo=local_tz)
        utc_dt = local_dt.astimezone(ZoneInfo("UTC"))

        # Build a +/- 90 minute window around the hinted time
        from datetime import timedelta
        window_before = (utc_dt - timedelta(minutes=90)).strftime("%Y-%m-%dT%H:%M:%SZ")
        window_after = (utc_dt + timedelta(minutes=90)).strftime("%Y-%m-%dT%H:%M:%SZ")

        params = {
            "apiKey": api_key,
            "commenceTimeFrom": window_before,
            "commenceTimeTo": window_after,
        }
        url = f"https://api.the-odds-api.com/v4/sports/{sport_key}/events"
        resp = requests.get(url, params=params, timeout=10)
        if resp.status_code == 429:
            return None, None
        resp.raise_for_status()
        events = resp.json() or []
        if not events:
            return None, None

        # If a single event in window, assume it's the match
        if len(events) == 1:
            ev = events[0]
            return ev.get("home_team"), ev.get("away_team")

        # If multiple, pick the one closest in time
        def parse_iso(ts: str) -> Optional[datetime]:
            try:
                if ts.endswith("Z"):
                    ts = ts[:-1]
                return datetime.strptime(ts, "%Y-%m-%dT%H:%M:%S").replace(tzinfo=ZoneInfo("UTC"))
            except Exception:
                return None

        closest = None
        best_diff = None
        for ev in events:
            ts = parse_iso(ev.get("commence_time", ""))
            if not ts:
                continue
            diff = abs((ts - utc_dt).total_seconds())
            if best_diff is None or diff < best_diff:
                best_diff = diff
                closest = ev
        if closest:
            return closest.get("home_team"), closest.get("away_team")

        return None, None
    except Exception:
        return None, None


# API Key Authentication Middleware
def validate_api_key(api_key: str) -> bool:
    """
    Validates the provided API key against the environment variable.

    Args:
        api_key (str): The API key to validate

    Returns:
        bool: True if valid, False otherwise
    """
    expected_key = os.getenv("EXTERNAL_API_KEY")
    if not expected_key:
        print(
            "[ERROR] EXTERNAL_API_KEY not configured in environment variables")
        return False

    is_valid = api_key == expected_key
    if not is_valid:
        print(
            f"[WARNING] Invalid API key attempt from {request.remote_addr if request else 'unknown'}"
        )

    return is_valid


def require_api_key(f):
    """
    Decorator to require API key authentication for endpoints.
    Expects X-API-Key header with valid API key.
    """

    @wraps(f)
    def decorated_function(*args, **kwargs):
        api_key = request.headers.get('X-API-Key')

        if not api_key:
            print(
                f"[WARNING] Missing API key in request from {request.remote_addr}"
            )
            return jsonify({
                "success":
                False,
                "message":
                "Missing API key. Include X-API-Key header."
            }), 401

        if not validate_api_key(api_key):
            print(
                f"[WARNING] Invalid API key provided from {request.remote_addr}"
            )
            return jsonify({
                "success": False,
                "message": "Invalid API key."
            }), 401

        return f(*args, **kwargs)

    return decorated_function


# insert_picks route moved to routes/external.py


# MLB projections route moved to routes/external.py


# Second MLB projections route moved to routes/external.py



# moved to routes/settings.py
# @app.route("/api/settings/date", methods=["GET"])
def get_date_setting():
    """
    Get the current default date setting with comprehensive error handling.

    Returns:
        JSON response with current date setting and fallback handling
    """
    try:
        current_date = get_default_date()

        # Validate the stored date before returning it
        is_valid, sanitized_date, error_message = validate_date_format(
            current_date)

        if not is_valid:
            print(
                f"[WARNING] Stored date is invalid: {current_date}, error: {error_message}"
            )
            # Reset to current California date
            global current_default_date
            current_default_date = get_current_california_date()
            current_date = current_default_date

            return jsonify({
                "success": True,
                "date": current_date,
                "warning":
                f"Date was reset to default due to invalid stored value: {error_message}",
                "auto_update": auto_update_enabled
            })

        return jsonify({
            "success": True,
            "date": current_date,
            "last_updated": datetime.now().isoformat(),
            "auto_update": auto_update_enabled
        })

    except Exception as e:
        print(f"[ERROR] Failed to get date setting: {e}")

        # Graceful fallback to current California date
        fallback_date = get_current_california_date()

        return jsonify({
            "success": True,  # Still return success with fallback
            "date": fallback_date,
            "warning": "Using fallback date due to service error",
            "error_details": str(e) if app.debug else None,
            "auto_update": auto_update_enabled
        }), 200  # Return 200 with fallback instead of 500


# moved to routes/settings.py
# @app.route("/api/settings/date", methods=["POST"])
def update_date_setting():
    """
    Update the default date setting with comprehensive validation and error handling.

    Expected JSON payload:
    {
        "date": "YYYY-MM-DD",
        "admin_password": "ppadmin42"
    }

    Returns:
        JSON response with success status, message, and detailed error information
    """
    try:
        # Validate request content type
        if not request.is_json:
            return jsonify({
                "success": False,
                "error": "Request must be JSON",
                "code": "INVALID_CONTENT_TYPE"
            }), 400

        data = request.get_json()

        if not data:
            return jsonify({
                "success": False,
                "error": "No JSON data provided",
                "code": "EMPTY_REQUEST"
            }), 400

        if not isinstance(data, dict):
            return jsonify({
                "success": False,
                "error": "Request data must be a JSON object",
                "code": "INVALID_REQUEST_FORMAT"
            }), 400

        new_date = data.get("date")
        admin_password = data.get("admin_password")

        # Validate required fields
        if not new_date:
            return jsonify({
                "success": False,
                "error": "Date field is required",
                "code": "MISSING_DATE"
            }), 400

        if not admin_password:
            return jsonify({
                "success": False,
                "error": "Admin password field is required",
                "code": "MISSING_PASSWORD"
            }), 400

        # Log the update attempt (without password)
        print(
            f"[INFO] Date update attempt: {new_date} from IP {request.remote_addr}"
        )

        # Call the update function with comprehensive validation
        result = update_default_date(new_date, admin_password)

        # Determine appropriate HTTP status code based on error type
        if result["success"]:
            return jsonify(result), 200
        else:
            error_code = result.get("code", "UNKNOWN_ERROR")

            if error_code in [
                    "MISSING_PASSWORD", "INVALID_PASSWORD_TYPE",
                    "INVALID_PASSWORD"
            ]:
                return jsonify(result), 401  # Unauthorized
            elif error_code in ["INVALID_DATE", "MISSING_DATE"]:
                return jsonify(result), 400  # Bad Request
            else:
                return jsonify(result), 500  # Internal Server Error

    except ValueError as e:
        print(f"[ERROR] Value error in update_date_setting: {e}")
        return jsonify({
            "success": False,
            "error": "Invalid request data format",
            "code": "VALUE_ERROR",
            "details": str(e) if app.debug else None
        }), 400

    except TypeError as e:
        print(f"[ERROR] Type error in update_date_setting: {e}")
        return jsonify({
            "success": False,
            "error": "Invalid data types in request",
            "code": "TYPE_ERROR",
            "details": str(e) if app.debug else None
        }), 400

    except Exception as e:
        print(f"[ERROR] Unexpected error in update_date_setting: {e}")
        return jsonify({
            "success": False,
            "error": "Internal server error occurred",
            "code": "INTERNAL_ERROR",
            "details": str(e) if app.debug else None
        }), 500


# moved to routes/frontend.py
# @app.route('/', defaults={'path': ''})
# @app.route('/<path:path>')
def ServeReactApp(path):
    FilePath = os.path.join(app.static_folder, path)
    if path != "" and os.path.exists(FilePath):
        return send_from_directory(app.static_folder, path)
    return send_from_directory(app.static_folder, 'index.html')


# Ensure that API routes are defined BEFORE this point,
# or that this catch-all is specific enough not to interfere with static assets if static_url_path is also '/'.
# If static_url_path="/", Flask automatically tries to serve static files first.
# If /favicon.ico or /assets/index-*.js is requested, Flask's static file handler should serve it.


# moved to routes/optimizer.py
# @app.route("/api/optimize_split", methods=["GET"])
def optimize_split():
    sorted_picks = sorted(pick_objects,
                          key=lambda x: getattr(x, "confidence_score", 0),
                          reverse=True)
    print(
        f"Optimizer: Processing {len(pick_objects)} picks. Sorted: {[p.name for p in sorted_picks]}"
    )

    if len(sorted_picks) < 2:
        print("Optimizer: Not enough picks to run analysis (need at least 2).")
        return jsonify({
            "best_score": 0.0,
            "best_config": "Not enough picks to optimize (Need at least 2).",
            "sorted_picks": [p.to_dict() for p in sorted_picks],
            "subparlays": []
        }), 200  # Return 200 so frontend can parse the message

    best_score, best_label = analyze_all_splits(sorted_picks)
    # total_capital = request.json.get("total_capital", 0)

    picks_to_use = []
    subgroup_size = 2  # Default

    # Ensure best_label is a string before string operations
    if isinstance(best_label, str):
        if "Full List" in best_label:
            picks_to_use = sorted_picks
        elif "Left" in best_label:
            try:
                split_index_str = best_label.split("index")[1].strip()
                split_index = int(split_index_str)
                picks_to_use = sorted_picks[:split_index]
            except (IndexError, ValueError) as e:
                print(
                    f"Optimizer: Error parsing split_index from left-sided '{best_label}': {e}. Defaulting picks_to_use."
                )
                picks_to_use = []
        elif "Right" in best_label:
            try:
                split_index_str = best_label.split("index")[1].strip()
                split_index = int(split_index_str)
                picks_to_use = sorted_picks[split_index:]
            except (IndexError, ValueError) as e:
                print(
                    f"Optimizer: Error parsing split_index from right-sided '{best_label}': {e}. Defaulting picks_to_use."
                )
                picks_to_use = []
        else:
            print(
                f"Optimizer: best_label '{best_label}' did not match expected patterns. Defaulting picks_to_use to empty."
            )
            picks_to_use = []

        import re
        match = re.search(r"Size (\d+)", best_label)
        if match:
            subgroup_size = int(match.group(1))
        else:
            print(
                f"Optimizer: Could not extract 'Size X' from best_label '{best_label}'. Defaulting subgroup_size to {subgroup_size}."
            )
    else:
        # This case should ideally not be hit if len(sorted_picks) >= 2 because analyze_all_splits should return a string label.
        # However, as a fallback if best_label is unexpectedly not a string (e.g. None, though covered by initial check).
        print(
            f"Optimizer: best_label was not a string ('{best_label}'). Defaulting picks_to_use and subgroup_size."
        )
        picks_to_use = []
        subgroup_size = 2
        best_label = "Error: Invalid optimization label received."
        best_score = 0.0

    subparlays = generate_round_robin_subparlays(picks_to_use, subgroup_size)
    final_best_score = best_score if isinstance(best_score,
                                                (int, float)) else 0.0

    return jsonify({
        "best_score":
        final_best_score,
        "best_config":
        best_label,
        "sorted_picks": [p.to_dict() for p in sorted_picks],
        "subparlays": [[p.to_dict() for p in sub] for sub in subparlays]
    })


# moved to routes/optimizer.py
# @app.route("/api/process", methods=["POST"])
def process():
    global next_id
    data = request.get_json()

    try:
        print("[INFO] PAYLOAD RECEIVED:", data)

        name = data.get("name", "").strip()
        pick_origins = data.get("pick_origin", [])  # [{ name, confidence }]
        print("odds:" + str(data.get("odds", 0)))
        odds = float(data.get("odds", 0))
        leagues = data.get("league", [])
        reusable = data.get("reusable", True)
        capital_limit = int(data.get("capital_limit", 0))
        mutual_exclusion = int(data.get("mutual_exclusion", -1))
        pick_type = data.get("pick_type", "MoneyLine")
        player_team = data.get("player_team", "None")
        stat_type = data.get("stat_type", "MoneyLine")

        if not name or not odds or not pick_origins or not leagues:
            return jsonify({
                "response": "Missing required fields",
                "success": False
            }), 400

        implied_prob = round(1 / odds, 4)  # crowd probability from odds

        # Use eventDate from payload if provided, otherwise fall back to today's date
        event_date = data.get("eventDate")
        if not event_date:
            event_date = datetime.today().strftime("%Y-%m-%d")
            print(
                f"[WARNING] No eventDate provided in payload, using today's date: {event_date}"
            )
        else:
            print(f"[INFO] Using eventDate from payload: {event_date}")

        expert_predictions = []
        total_score = 0

        for origin_obj in pick_origins:
            origin = origin_obj.get("name")
            print(origin_obj.get("confidence"))
            origin_conf = origin_obj.get("confidence")

            if not origin:
                continue  # Skip invalid entries

            # Ensure origin_conf is a usable float
            try:
                used_conf = float(origin_conf)
            except Exception as e:
                used_conf = 75.0  # fallback if None or not a number

            # Normalize the origin key by removing spaces
            origin_key = origin.replace(" ", "")

            if origin_key not in origin_profiles:
                raise KeyError(
                    f"Origin key '{origin_key}' not found in origin_profiles")

            origin_accuracy = origin_profiles[origin_key]() if callable(
                origin_profiles[origin_key]) else origin_profiles[origin_key]

            norm_conf = used_conf / 100.0
            # Extract shared prediction direction
            prediction = int(data.get("prediction",
                                      1))  # 1 = Higher, 0 = Lower

            for origin_obj in pick_origins:
                origin = origin_obj.get("name")
                origin_conf = origin_obj.get("confidence")

                if not origin:
                    continue  # Skip invalid entries

                # Ensure origin_conf is a usable float
                try:
                    used_conf = float(origin_conf)
                except Exception:
                    used_conf = 75.0  # fallback if None or not a number

                # 🧠 Historical accuracy
                origin_accuracy = origin_profiles[origin]() if callable(
                    origin_profiles[origin]) else origin_profiles[origin]

                norm_conf = used_conf / 100.0

                # ⬅️ Use the shared prediction value for all experts
                expert_predictions.append((origin, prediction, norm_conf))

                # 🧮 Confidence score calculation
                score = confidence_score(odds, used_conf, origin_accuracy)
                total_score += score

        # final_score = round(total_score / len(expert_predictions), 2) if expert_predictions else 0
        # print(final_score)

        if pick_type == "MoneyLine":
            team_a = name
            team_b = "Other"
            player_team = "None"
        else:
            team_a = "Over"
            team_b = "Under"

        print("🧠 Parsed expert predictions:", expert_predictions)

        status_messages = []
        success_count = 0

        # Process each league for this pick

        for league in leagues:
            event_id = generate_event_id(name, league)
            # Check if the event already exists
            conn = sqlite3.connect(DB_PATH)
            cursor = conn.cursor()
            cursor.execute("SELECT 1 FROM events WHERE event_id = ?",
                           (event_id, ))
            exists = cursor.fetchone() is not None
            conn.close()

            if not exists:
                # Insert event & predictions as usual
                print(
                    f"[DEBUG] process: New event submission - event_id={event_id}, implied_prob={implied_prob}, pick_origins_count={len(pick_origins)}"
                )
                success, message = submit_event(
                    event_id=event_id,
                    event_date=event_date,
                    league=league,
                    team_a=team_a,
                    team_b=team_b,
                    crowd_probability=implied_prob,
                    expert_predictions=expert_predictions,
                    actual_result=None,
                    pick_type=pick_type,
                    player_team=player_team,
                    stat_type=stat_type,
                    player_name=
                    None,  # /api/process doesn't handle player props typically
                    stat_threshold=None)
                print(
                    f"[DEBUG] process: submit_event returned - success={success}, message={message}"
                )
            else:
                # Just insert additional expert predictions and update crowd prob
                try:
                    conn = sqlite3.connect(DB_PATH)
                    cursor = conn.cursor()

                    # Update crowd_probability (optional)
                    cursor.execute(
                        """
                        INSERT OR REPLACE INTO crowd_predictions (event_id, crowd_probability)
                        VALUES (?, ?)
                    """, (event_id, implied_prob))

                    # Append new expert predictions
                    for expert_name, prediction, confidence in expert_predictions:
                        # Prevent phantom ChartGeneration predictions
                        if expert_name.lower() == "chartgeneration":
                            continue

                        cursor.execute(
                            """
                            INSERT OR REPLACE INTO expert_predictions (event_id, expert_name, prediction, confidence)
                            VALUES (?, ?, ?, ?)
                        """, (event_id, expert_name, prediction, confidence))

                        # Ensure expert exists in reliability table
                        cursor.execute(
                            "SELECT 1 FROM expert_reliability WHERE expert_name = ?",
                            (expert_name, ))
                        if not cursor.fetchone():
                            cursor.execute(
                                """
                                INSERT INTO expert_reliability (expert_name)
                                VALUES (?)
                            """, (expert_name, ))

                    conn.commit()
                    conn.close()
                    success = True
                    message = "Existing event updated with crowd prediction and new expert predictions."

                except Exception as e:
                    success = False
                    message = f"Error updating existing event: {e}"

            status_messages.append(f"[{league}] {message}")

            # Calculate average expert confidence for this event
            avg_expert_confidence = sum(
                conf for _, _, conf in expert_predictions) / len(
                    expert_predictions) if expert_predictions else 0.75

            # Use dynamic confidence calculation with ML model
            try:
                final_score = calculate_dynamic_confidence(
                    event_id, avg_expert_confidence)
                print(
                    f"[INFO] Dynamic confidence calculated: {final_score} for event {event_id}"
                )
            except Exception as e:
                print(f"[ERROR] Dynamic confidence calculation failed: {e}")
                # Fallback to average expert confidence scaled to 0-100
                final_score = avg_expert_confidence * 100

            # Get additional ML model data for Pick object
            try:
                prediction_result = main_model(event_id)
                ml_prob = prediction_result.get("combined_prob", 0.5)
                logistic_prob = prediction_result.get("logistic_prob", 0.5)
                bayesian_prob = prediction_result.get("bayesian_prob", 0.5)
                bayesian_conf = prediction_result.get("quality_score", 0.5)
            except Exception as e:
                print(f"[ERROR] Failed to get ML model data: {e}")
                ml_prob = logistic_prob = bayesian_prob = bayesian_conf = 0.5

            if success:
                new_pick = Pick(name=name,
                                odds=odds,
                                confidence=final_score,
                                mutual_exclusion_group=mutual_exclusion,
                                league=league,
                                event_id=event_id,
                                bayesian_prob=bayesian_prob,
                                logistic_prob=logistic_prob,
                                bayesian_conf=bayesian_conf,
                                stat_type=stat_type,
                                reusable=reusable,
                                capital_limit=capital_limit)
                pick_objects.append(new_pick)
                next_id += 1
                success_count += 1

        return jsonify({
            "response": " | ".join(status_messages),
            "objects": [p.__dict__ for p in pick_objects],
            "success": success_count == len(leagues)
        })

    except Exception as e:
        print("[ERROR] SERVER ERROR:", e)
        return jsonify({
            "response": f"Server error: {str(e)}",
            "success": False
        }), 500


# moved to routes/optimizer.py
# @app.route("/api/create_boost_promo", methods=["POST"])
def create_boost_promo():
    data = request.get_json()
    boost_percentage = int(data.get("boost_percentage", 0))
    required_picks = int(data.get("required_picks", 0))
    same_sport = data.get("same_sport", False)

    boost = BoostPromo(boost_percentage, required_picks, same_sport)
    boost_promo_objects.append(boost.__dict__)

    return jsonify({
        "response": f"Created Boost Promo: {boost.name}",
        "boost_promos": boost_promo_objects
    })


# moved to routes/optimizer.py
# @app.route("/api/create_protected_promo", methods=["POST"])
def create_protected_promo():
    data = request.get_json()
    protected_amount = int(data.get("protected_amount", 0))
    eligible_leagues = data.get("eligible_leagues", [])

    protected = ProtectedPromo(protected_amount, eligible_leagues)
    protected_promo_objects.append(protected.__dict__)

    return jsonify({
        "response": f"Created Protected Play Promo: {protected.name}",
        "protected_promos": protected_promo_objects
    })


# Edit route moved to routes/picks.py
def edit():
    global pick_objects
    data = request.get_json()
    obj_id = data.get("id")

    for obj in pick_objects:
        if obj.pID == obj_id:
            # Update fields on the Pick object
            obj.name = data.get("name", obj.name)
            obj.decimalOdds = float(data.get("odds", obj.decimalOdds))
            obj.pick_origin = data.get("pick_origin", obj.pick_origin)
            obj.league = data.get("league", obj.league)
            obj.reusable = data.get("reusable", obj.reusable)
            obj.capital_limit = int(
                data.get("capital_limit", obj.capital_limit))
            obj.gameID = int(data.get("mutual_exclusion", obj.gameID))
            obj.pick_type = data.get("pick_type", obj.pick_type)
            obj.player_team = data.get("player_team", obj.player_team)
            obj.stat_type = data.get("stat_type", obj.stat_type)

            name = obj.name
            odds = obj.decimalOdds
            leagues = obj.league
            pick_origins = obj.pick_origin
            pick_type = obj.pick_type
            player_team = obj.player_team
            stat_type = obj.stat_type

            # Determine team_a and team_b based on pick_type
            if pick_type == "MoneyLine":
                team_a = name
                team_b = "Other"
                player_team = "None"
            else:
                team_a = "Over"
                team_b = "Under"

            implied_prob = round(1 / odds, 4)
            today = datetime.today().strftime("%Y-%m-%d")

            # Recalculate expert prediction score
            expert_predictions = []
            total_score = 0

            for origin_obj in pick_origins:
                origin = origin_obj.get("name")
                origin_conf = origin_obj.get("confidence", None)
                prediction = origin_obj.get("prediction",
                                            1)  # default to Higher

                origin_accuracy = origin_profiles[origin]() if callable(
                    origin_profiles[origin]) else origin_profiles[origin]
                norm_conf = origin_conf / 100 if origin_conf is not None else None

                expert_predictions.append((origin, prediction, norm_conf))

                used_conf = origin_conf if origin_conf is not None else 75.0
                score = confidence_score(odds, used_conf, origin_accuracy)
                total_score += score

            final_score = round(total_score / len(expert_predictions),
                                2) if expert_predictions else 0
            obj.confidence = None
            obj.confidence_score = final_score

            # Update the database
            for league in leagues:
                event_id = generate_event_id(name, league)
                obj.event_id = event_id

                try:
                    conn = sqlite3.connect(DB_PATH)
                    cursor = conn.cursor()

                    cursor.execute(
                        """
                        INSERT OR REPLACE INTO events (
                            event_id, event_date, league,
                            team_a, team_b, actual_result,
                            pick_type, player_team, stat_type
                        ) VALUES (?, ?, ?, ?, ?, COALESCE(
                            (SELECT actual_result FROM events WHERE event_id = ?), NULL
                        ), ?, ?, ?)
                    """, (event_id, today, league, team_a, team_b, event_id,
                          pick_type, player_team, stat_type))

                    cursor.execute(
                        """
                        INSERT OR REPLACE INTO crowd_predictions (event_id, crowd_probability)
                        VALUES (?, ?)
                    """, (event_id, implied_prob))

                    cursor.execute(
                        "DELETE FROM expert_predictions WHERE event_id = ?",
                        (event_id, ))
                    for origin, prediction, confidence in expert_predictions:
                        # Prevent phantom ChartGeneration predictions
                        if origin.lower() == "chartgeneration":
                            continue

                        cursor.execute(
                            """
                            INSERT OR REPLACE INTO expert_predictions (event_id, expert_name, prediction, confidence)
                            VALUES (?, ?, ?, ?)
                        """, (event_id, origin, prediction, confidence))

                        cursor.execute(
                            "SELECT 1 FROM expert_reliability WHERE expert_name = ?",
                            (origin, ))
                        if not cursor.fetchone():
                            cursor.execute(
                                """
                                INSERT INTO expert_reliability (expert_name)
                                VALUES (?)
                            """, (origin, ))

                    conn.commit()
                    conn.close()

                except Exception as e:
                    print(f"[ERROR] DB Error while editing {event_id}: {e}")

            break

    return jsonify({"objects": [p.__dict__ for p in pick_objects]})


##### CURRENT SPOT IS CREATING RELIABLE EVENT ID FROM PICK ID ####
def get_event_id_from_pick_id(pick_id):
    for pick in pick_objects:
        print(pick.pID)
        if pick.pID == pick_id:
            return pick.event_id
    return None


# moved to routes/optimizer.py
# @app.route("/api/submit_verified", methods=["POST"])
def submit_verified():
    data = request.get_json()
    verified = data.get("verified", [])

    print(pick_objects)

    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    updated_ids = []
    localID = 0

    for item in verified:
        localID += 1
        pick_id = localID

        # Assume item["actual_result"] is 1 if user marked it "Verified"
        user_verification = item["actual_result"]

        # Find pick object by ID
        pick = next(
            (p
             for p in pick_objects if hasattr(p, "pID") and p.pID == pick_id),
            None)
        if not pick:
            print(f"[ERROR] Pick ID {pick_id} not found in memory.")
            continue

        event_id = getattr(pick, "event_id", None)
        if not event_id:
            print(f"[ERROR] Pick ID {pick_id} has no event_id.")
            continue

        # 🔍 Interpret prediction direction:
        #  - If expert said "Higher" and user marked as 1 → event happened ✅
        #  - If expert said "Lower" and user marked as 1 → event did NOT happen ❌
        # We'll default to the first expert's prediction signal (they're all assumed to match)
        cursor.execute(
            "SELECT prediction FROM expert_predictions WHERE event_id = ? LIMIT 1",
            (event_id, ))
        row = cursor.fetchone()

        if row:
            expert_prediction = row[0]  # 1 = Higher, 0 = Lower

            if expert_prediction == 1:  # Higher = event is expected to occur
                actual_result = 1 if user_verification == 1 else 0
            else:  # Lower = expert expects event NOT to occur
                actual_result = 0 if user_verification == 1 else 1
        else:
            print(
                f"⚠️ No expert prediction found for {event_id}, assuming default."
            )
            actual_result = user_verification
        event_id = get_event_id_from_pick_id(pick_id)
        print(event_id)

        # ✅ Find pick object by ID (from actual class instances)
        pick = next(
            (p
             for p in pick_objects if hasattr(p, "pID") and p.pID == pick_id),
            None)
        if not pick:
            print(f"❌ Pick ID {pick_id} not found in memory.")
            continue

        event_id = getattr(pick, "event_id", None)
        if not event_id:
            print(f"❌ Pick ID {pick_id} has no event_id.")
            continue

        print(
            f"✅ Updating event_id: {event_id} → actual_result: {actual_result}"
        )
        try:
            cursor.execute(
                """
                UPDATE events
                SET actual_result = ?
                WHERE event_id = ?
            """, (actual_result, event_id))

            if cursor.rowcount > 0:
                updated_ids.append(event_id)
            else:
                print(
                    f"⚠️ No rows updated for {event_id} (may not exist in DB)."
                )

        except Exception as e:
            print(f"❌ DB error updating {event_id}: {e}")

    conn.commit()
    conn.close()

    return jsonify(
        {"message": f"Updated {len(updated_ids)} events with actual results."})


# moved to routes/optimizer.py
# @app.route("/api/load_sample_picks", methods=["POST"])
def load_sample_picks():
    global pick_objects
    pick_objects = []

    num_picks = 8
    example_names = [
        "Lakers ML", "Yankees -1.5", "Chiefs +3", "Over 8.5", "Under 220",
        "Dodgers ML", "Ravens -2.5", "Heat +6", "Bills ML", "Nets Over 230"
    ]
    leagues = ["NBA", "NFL", "MLB", "NHL"]

    for i in range(num_picks):
        name = random.choice(example_names) + f" #{i+1}"
        odds = round(random.uniform(1.05, 2.5), 2)
        mutual_exclusion_group = random.randint(0, 5)
        league = random.choice(leagues)
        reusable = random.choice([True, False])
        capital_limit = random.randint(10, 100)
        stat_type = "MoneyLine"
        event_id = f"SAMPLE-{i+1}"

        # Generate synthetic model probabilities
        bayesian_prob = round(random.uniform(0.4, 0.9), 2)
        logistic_prob = round(random.uniform(0.4, 0.9), 2)
        bayesian_conf = round(random.uniform(0.5, 0.9), 2)

        # Calculate final confidence score using model-weighted blend
        combined_prob = round(
            bayesian_conf * bayesian_prob +
            (1 - bayesian_conf) * logistic_prob, 4)
        implied_prob = 1 / odds
        raw_score = combined_prob - implied_prob
        clamped = max(min(raw_score, 0.2), -0.2)
        scaled_score = round((clamped + 0.2) / 0.4 * 100, 2)

        # Create Pick object
        new_pick = Pick(name=name,
                        odds=odds,
                        confidence=scaled_score,
                        mutual_exclusion_group=mutual_exclusion_group,
                        league=league,
                        event_id=event_id,
                        bayesian_prob=bayesian_prob,
                        logistic_prob=logistic_prob,
                        bayesian_conf=bayesian_conf,
                        stat_type=stat_type,
                        reusable=reusable,
                        capital_limit=capital_limit)

        pick_objects.append(new_pick)

    return jsonify({
        "message": f"{num_picks} sample picks loaded.",
        "objects": [p.to_dict() for p in pick_objects]
    })


# moved to routes/picks.py
# @app.route("/api/load_user_picks", methods=["POST"])
def load_user_picks():
    """
    Load user picks from frontend PicksContext into backend pick_objects for optimizer functionality.
    Expects JSON payload with 'picks' array containing user pick data.
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                "message": "No data provided.",
                "objects": []
            }), 400

        user_picks = data.get("picks", [])

        global pick_objects
        pick_objects = []  # Clear existing picks
        Pick.pID_counter = 0  # Reset ID counter

        # Convert user picks to backend Pick objects
        for pick_data in user_picks:
            try:
                # Map PicksContext format to backend Pick format
                new_pick = Pick(
                    name=pick_data.get("playerName", "Unknown Player"),
                    odds=
                    1.5,  # Default odds - could be enhanced to get from pick data
                    confidence=pick_data.get("confidence", 75),
                    mutual_exclusion_group=-1,  # Default group
                    league=["Unknown"],  # Default league as list
                    event_id=pick_data.get("id",
                                           f"user_pick_{len(pick_objects)}"),
                    bayesian_prob=0.5,  # Default probability
                    logistic_prob=0.5,  # Default probability
                    bayesian_conf=0.5,  # Default confidence
                    stat_type=pick_data.get("betType", "Unknown"),
                    reusable=True,
                    capital_limit=0)
                pick_objects.append(new_pick)
            except Exception as e:
                print(f"Error processing pick {pick_data}: {e}")
                continue

        return jsonify({
            "message": f"Loaded {len(pick_objects)} user picks successfully.",
            "objects": [p.to_dict() for p in pick_objects]
        })

    except Exception as e:
        print(f"Error in load_user_picks: {e}")
        return jsonify({
            "message": f"Error loading user picks: {str(e)}",
            "objects": []
        }), 500


# moved to routes/picks.py
# @app.route("/api/clear_picks", methods=["POST"])
def clear_picks():
    global pick_objects
    pick_objects = []
    Pick.pID_counter = 0  # reset ID counter

    # Optional: clear optimizer results too if you're storing those separately
    # e.g., if you eventually save best_score or best_label in global vars

    return jsonify({"message": "All picks cleared.", "objects": pick_objects})


# moved to routes/picks.py
# @app.route("/api/delete", methods=["POST"])
def delete():
    data = request.get_json()
    obj_id = data.get("id")

    global pick_objects
    deleted_pick = None

    # Find the pick
    for obj in pick_objects:
        if obj["id"] == obj_id:
            deleted_pick = obj
            break

    if deleted_pick:
        name = deleted_pick["name"]
        leagues = deleted_pick["league"]
        for league in leagues:
            event_id = generate_event_id(name, league)
            try:
                conn = sqlite3.connect(DB_PATH)
                cursor = conn.cursor()
                cursor.execute(
                    "DELETE FROM expert_predictions WHERE event_id = ?",
                    (event_id, ))
                cursor.execute(
                    "DELETE FROM crowd_predictions WHERE event_id = ?",
                    (event_id, ))
                cursor.execute("DELETE FROM events WHERE event_id = ?",
                               (event_id, ))
                conn.commit()
                conn.close()
            except Exception as e:
                print(f"DB error while deleting event {event_id}: {e}")

        # Remove from in-memory list
        pick_objects = [obj for obj in pick_objects if obj["id"] != obj_id]

    return jsonify({"objects": [p.to_dict() for p in pick_objects]})


# moved to routes/picks.py
# @app.route("/api/update_accuracy", methods=["POST"])
def update_accuracy():
    global user_accuracy
    data = request.get_json()
    try:
        user_accuracy = float(data.get("accuracy", 0.0))
    except ValueError:
        user_accuracy = 0.0
    return jsonify({
        "message": f"Accuracy updated to {user_accuracy}",
        "user_accuracy": user_accuracy
    })


@app.route("/api/handicappers", methods=["GET"])
def get_handicappers():
    """
    Placeholder endpoint to prevent 404 errors.
    Frontend handles handicapper data through local caching.
    """
    return jsonify({
        "success": True,
        "message": "Handicappers endpoint placeholder",
        "handicappers": []
    })


@app.route("/api/handicappers/<int:handicapper_id>", methods=["GET"])
def get_handicapper_profile(handicapper_id):
    """
    Placeholder endpoint to prevent 404 errors.
    Frontend handles handicapper profiles through local caching.
    """
    return jsonify({
        "success": True,
        "message": "Handicapper profile endpoint placeholder",
        "handicapper": {
            "id": handicapper_id,
            "name": f"Handicapper {handicapper_id}",
            "accuracy": "N/A",
            "sports": "Multiple Sports",
            "picks": []
        }
    })


@app.route("/api/favorites", methods=["GET"])
def get_favorites():
    """
    Placeholder endpoint to prevent 404 errors.
    Frontend handles favorites through local storage.
    """
    return jsonify({
        "success": True,
        "message": "Favorites endpoint placeholder",
        "favorites": []
    })


@app.route("/api/favorites", methods=["POST"])
def save_favorites():
    """
    Placeholder endpoint to prevent 404 errors.
    Frontend handles favorites through local storage.
    """
    return jsonify({
        "success": True,
        "message": "Favorites saved (placeholder)",
        "favorites": []
    })


# Todays events route moved to routes/events.py
def get_todays_events():
    """
    API endpoint to retrieve events with handicapper predictions for a specific date.

    Query Parameters:
        date (optional): Date in YYYY-MM-DD format. If not provided, uses today's date.

    Returns:
        JSON response containing a list of events with their associated
        handicapper predictions, or an error message if something goes wrong.
    """
    try:
        # Get date parameter from query string, default to admin-set date if not provided
        custom_date = request.args.get('date')
        if custom_date:
            # Validate date format
            try:
                datetime.strptime(custom_date, "%Y-%m-%d")
                target_date = custom_date
            except ValueError:
                return jsonify({
                    "success": False,
                    "message": "Invalid date format. Please use YYYY-MM-DD format.",
                    "events": []
                }), 400
        else:
            # Use the admin-set date logic (respects auto-update toggle)
            target_date = get_default_date()

        events = get_todays_events_with_handicappers(target_date)

        events_data = []
        for event in events:
            events_data.append(event.to_dict())

        # Intentionally avoid mutating team data here to preserve original inputs

        return jsonify({
            "success": True,
            "message":
            f"Retrieved {len(events_data)} events for {target_date}",
            "events": events_data,
            "date": target_date
        })

    except Exception as e:
        print(f"[ERROR] Error in /api/todays_events endpoint: {e}")
        return jsonify({
            "success": False,
            "message": f"Error retrieving events: {str(e)}",
            "events": []
        }), 500


# External todays games route moved to routes/external.py
def external_todays_games():
    """
    Returns a deduplicated list of today's unique games by league with optional start times when available.

    Query params:
      - date (optional, YYYY-MM-DD). Defaults to current default date.
    """
    try:
        custom_date = request.args.get('date')
        if custom_date:
            try:
                datetime.strptime(custom_date, "%Y-%m-%d")
                target_date = custom_date
            except ValueError:
                return jsonify({
                    "success": False,
                    "message": "Invalid date format. Use YYYY-MM-DD.",
                    "games": []
                }), 400
        else:
            target_date = get_default_date()

        games = _fetch_unique_games_by_date(target_date)

        # Build response without performing any time lookups
        enriched = []
        flattened_rows = []
        for g in games:
            league = g.get('league')
            ta = g.get('team_a')
            tb = g.get('team_b')
            enriched.append({
                'league': league,
                'team_a': ta,
                'team_b': tb,
                'game_date': target_date
            })
            gd = target_date or ''
            # Single-line CSV-style entry per game (no headers), rows separated by '; '
            flattened_rows.append(f"{league},{ta},{tb},{gd}")

        return jsonify({
            'success': True,
            'message': f"Retrieved {len(enriched)} unique games for {target_date}",
            'date': target_date,
            'games': enriched,
            'games_flattened_str': "; ".join(flattened_rows)
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f"Error retrieving games: {str(e)}",
            'games': []
        }), 500


# Add event route moved to routes/events.py
def add_event():
    """Insert a single event (or one per league) into the events table. Minimal logic, password-protected."""
    data = request.get_json()
    print("[DEBUG] /api/add_event payload:", data)
    print(
        f"[DEBUG] crowd_probability received: {data.get('crowd_probability')}")
    source_name = data.get("source")
    print(f"[DEBUG] source_name received: {source_name}")

    if data.get("admin_password") != "ppadmin42":
        return jsonify({"success": False, "message": "Unauthorized"}), 401

    name = data.get("name", "").strip()
    leagues = data.get("league", [])
    event_date = data.get("eventDate") or data.get(
        "event_date") or datetime.today().strftime("%Y-%m-%d")
    pick_type = data.get("pick_type", "MoneyLine")
    player_team = data.get("player_team", "None")
    stat_type_raw = data.get("stat_type", "MoneyLine")
    # Normalize stat type for consistent database storage
    stat_type = normalize_stat_type_for_storage(stat_type_raw)
    player_name = data.get("player_name")
    stat_threshold = data.get("stat_threshold")
    team_a = data.get("team_a", name)  # Default to name if not provided
    team_b = data.get("team_b", "Other")  # Default to "Other" if not provided
    pick_origin = data.get('pick_origin', [])

    if not name or not leagues:
        return jsonify({
            "success": False,
            "message": "Missing required fields"
        }), 400

    # Import PlanetScale connection function
    try:
        from data_science_modules.planet_scale_port import get_connection
    except ImportError:
        return jsonify({
            "success": False,
            "message": "Database connection module not available"
        }), 500

    inserted = 0

    try:
        with get_connection() as conn:
            cursor = conn.cursor()
            event_id = None  # Ensure event_id is defined outside the loop
            expert_predictions_inserted = 0

            # Test database connection
            cursor.execute("SELECT 1")
            test_result = cursor.fetchone()
            print(f"[INFO] Database connection test: {test_result}")

            # Check if tables exist
            cursor.execute("SHOW TABLES LIKE 'events'")
            events_table = cursor.fetchone()
            cursor.execute("SHOW TABLES LIKE 'expert_predictions'")
            predictions_table = cursor.fetchone()
            print(
                f"[INFO] Tables exist - events: {bool(events_table)}, expert_predictions: {bool(predictions_table)}"
            )

            print(f"[INFO] Processing {len(leagues)} leagues: {leagues}")
            print(
                f"[INFO] Expert predictions to insert: {len(pick_origin) if pick_origin else 0}"
            )

            for league in leagues:
                # Use original stat_type for event_id generation (for consistency)
                # but store normalized version in database
                # Fill missing teams for MoneyLine/Spread when Unknown/null provided
                if pick_type in ["MoneyLine", "Spread"]:
                    prev_a, prev_b = team_a, team_b
                    team_a, team_b = fill_missing_teams(
                        league,
                        event_date,
                        team_a,
                        team_b,
                    )
                    # Last-resort normalization to avoid Unknown/null leaking into ID
                    if _is_unknown_team(team_a) and not _is_unknown_team(prev_a):
                        team_a = _format_nickname(prev_a)
                    if _is_unknown_team(team_b) and not _is_unknown_team(prev_b):
                        team_b = _format_nickname(prev_b)

                event_id = generate_admin_event_id(event_date, league,
                                                   pick_type, team_a, team_b,
                                                   player_name, stat_threshold,
                                                   stat_type_raw)
                print(f"[INFO] Generated event_id for {league}: {event_id}")
                print(
                    f"[INFO] Event data: date={event_date}, teams={team_a} vs {team_b}, type={pick_type}"
                )

                # Resolve accurate start times via Odds API per league
                api_game_date, api_prediction_time = get_event_start_from_odds_api(
                    league, team_a, team_b, event_date)
                event_date_for_insert = api_game_date or event_date

                try:
                    insert_query = """INSERT INTO events (
                            event_id, event_date, league,
                            team_a, team_b, pick_type,
                            player_team, stat_type, player_name, stat_threshold)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)"""
                    insert_values = (event_id, event_date_for_insert, league, team_a,
                                     team_b, pick_type, player_team, stat_type,
                                     player_name, stat_threshold)
                    print(f"[INFO] Executing SQL: {insert_query}")
                    print(f"[INFO] With values: {insert_values}")

                    cursor.execute(insert_query, insert_values)
                    rows_affected = cursor.rowcount
                    print(f"[INFO] Rows affected by INSERT: {rows_affected}")

                    if rows_affected > 0:
                        inserted += 1
                        print(f"[SUCCESS] Inserted event: {event_id}")
                    else:
                        print(
                            f"[WARNING] No rows inserted for event: {event_id}"
                        )

                except Exception as insert_error:
                    if "Duplicate entry" in str(insert_error):
                        print(f"[WARNING] Event already exists: {event_id}")
                    else:
                        lower_err = str(insert_error).lower()
                        if "unknown column" in lower_err or "column count" in lower_err:
                            try:
                                fallback_query = """INSERT INTO events (
                                        event_id, event_date, league,
                                        team_a, team_b, pick_type,
                                        player_team, stat_type)
                                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s)"""
                                cursor.execute(
                                    fallback_query,
                                    (
                                        event_id,
                                        event_date_for_insert,
                                        league,
                                        team_a,
                                        team_b,
                                        pick_type,
                                        player_team,
                                        stat_type,
                                    ),
                                )
                                if cursor.rowcount > 0:
                                    inserted += 1
                                    print(
                                        f"[SUCCESS] Fallback inserted event: {event_id}"
                                    )
                            except Exception as fb_err:
                                print(
                                    f"[WARNING] Fallback failed, attempting minimal insert: {fb_err}"
                                )
                                try:
                                    minimal_query = """INSERT INTO events (
                                            event_id, event_date, league, team_a, team_b)
                                        VALUES (%s, %s, %s, %s, %s)"""
                                    cursor.execute(
                                        minimal_query,
                                        (
                                            event_id,
                                            event_date_for_insert,
                                            league,
                                            team_a,
                                            team_b,
                                        ),
                                    )
                                    if cursor.rowcount > 0:
                                        inserted += 1
                                        print(
                                            f"[SUCCESS] Minimal fallback inserted event: {event_id}"
                                        )
                                except Exception as min_err:
                                    print(
                                        f"[ERROR] Minimal fallback insert failed for {event_id}: {min_err}"
                                    )
                                    # Do **not** raise here – continue processing so expert_predictions loop can still run if event exists
                        else:
                            print(
                                f"[ERROR] Failed to insert event {event_id}: {insert_error}"
                            )
                            import traceback
                            traceback.print_exc()
                            raise

                # Insert crowd predictions for this event
                crowd_prob = data.get("crowd_probability")
                try:
                    print(
                        f"[DEBUG] /api/add_event inserting crowd_prediction: event_id={event_id}, crowd_probability={crowd_prob}"
                    )
                    cursor.execute(
                        "INSERT INTO crowd_predictions (event_id, crowd_probability) VALUES (%s, %s) ON DUPLICATE KEY UPDATE crowd_probability = VALUES(crowd_probability)",
                        (event_id, crowd_prob))
                except Exception as crowd_err:
                    print(
                        f"[ERROR] Failed to insert crowd_prediction for {event_id}: {crowd_err}"
                    )
                # After inserting the event, insert expert predictions referencing this specific event_id
                if pick_origin:
                    for expert_pick in pick_origin:
                        expert_name = expert_pick.get("name")
                        if expert_name and expert_name.strip():
                            # Validate and convert data types to match database constraints
                            try:
                                # Convert prediction to integer (0 or 1)
                                prediction = int(data.get("prediction", 1))
                                if prediction not in [0, 1]:
                                    prediction = 1  # Default to 1 if invalid

                                # Store original confidence for potential fallback
                                confidence_raw = expert_pick.get("confidence")
                                if confidence_raw is not None:
                                    # Extract numeric value from confidence
                                    import re
                                    match = re.search(r'(\d+\.?\d*)',
                                                      str(confidence_raw))
                                    if match:
                                        confidence_str = match.group(1)
                                        original_confidence = float(
                                            confidence_str)
                                        # If confidence is > 1, assume it's a percentage and convert
                                        if original_confidence > 1.0:
                                            original_confidence = original_confidence / 100.0
                                        # Ensure it's within valid range
                                        confidence = max(
                                            0.0, min(1.0, original_confidence))
                                    else:
                                        confidence = 0.5  # Default if invalid format
                                else:
                                    confidence = 0.5  # Default if no confidence provided

                                print(
                                    f"🔄 Using original confidence: {confidence*100:.1f}% for {expert_name} (will attempt dynamic calculation after insertion)"
                                )

                                # Convert stat_threshold to float if provided
                                stat_threshold_raw = expert_pick.get(
                                    "stat_threshold")
                                stat_threshold = float(
                                    stat_threshold_raw
                                ) if stat_threshold_raw is not None else None

                                # Handle prediction_time field
                                prediction_time = expert_pick.get(
                                    "prediction_time")
                                if prediction_time:
                                    # If only time is provided, combine with event_date
                                    if len(
                                            prediction_time
                                    ) <= 8:  # Format like "14:30:00" or "14:30"
                                        prediction_time = f"{event_date_for_insert} {prediction_time}"
                                    # If full datetime is provided, use as-is
                                else:
                                    # Prefer Odds API commence time; otherwise leave NULL to avoid misleading defaults
                                    prediction_time = api_prediction_time or None

                                print(
                                    f"🔄 Inserting prediction: expert={expert_name}, prediction={prediction}, confidence={confidence}, time={prediction_time}"
                                )

                                # Prevent phantom ChartGeneration predictions
                                if expert_name.lower() == "chartgeneration":
                                    continue

                                # Check if this expert prediction already exists to prevent duplicates
                                cursor.execute(
                                    "SELECT event_id FROM expert_predictions WHERE event_id = %s AND expert_name = %s",
                                    (event_id, expert_name))
                                existing_record = cursor.fetchone()

                                if existing_record:
                                    print(
                                        f"⚠️ Expert prediction already exists for {expert_name} on {event_id}, updating..."
                                    )
                                    cursor.execute(
                                        """UPDATE expert_predictions SET
                                            prediction = %s, confidence = %s, stat_threshold = %s,
                                            team = %s, prediction_time = %s
                                        WHERE event_id = %s AND expert_name = %s""",
                                        (
                                            prediction,
                                            confidence,
                                            stat_threshold,
                                            expert_pick.get("team"),
                                            prediction_time,
                                            event_id,
                                            expert_name,
                                        ),
                                    )
                                else:
                                    cursor.execute(
                                        """INSERT INTO expert_predictions (
                                            event_id, expert_name, prediction, confidence,
                                            stat_threshold, team, game_date, league, prediction_time
                                        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)""",
                                        (
                                            event_id,
                                            expert_name,
                                            prediction,
                                            confidence,
                                            stat_threshold,
                                            expert_pick.get("team"),
                                            event_date_for_insert,
                                            league,
                                            prediction_time,
                                        ),
                                    )
                                expert_predictions_inserted += 1
                                print(
                                    f"✅ Inserted expert prediction: {expert_name} for {event_id}"
                                )
                            except Exception as pred_error:
                                print(
                                    f"❌ Failed to insert expert prediction for {expert_name}: {pred_error}"
                                )
                                # Try fallback with basic columns only and validated data
                                try:
                                    prediction = int(data.get("prediction", 1))
                                    if prediction not in [0, 1]:
                                        prediction = 1

                                    confidence_raw = expert_pick.get(
                                        "confidence")
                                    if confidence_raw is not None:
                                        confidence = float(confidence_raw)
                                        if confidence > 1.0:
                                            confidence = confidence / 100.0
                                        confidence = max(
                                            0.0, min(1.0, confidence))
                                    else:
                                        confidence = None

                                    # Use Odds API commence time; otherwise leave NULL
                                    fallback_time = api_prediction_time or None

                                    cursor.execute(
                                        """INSERT INTO expert_predictions (
                                            event_id, expert_name, prediction, confidence, prediction_time
                                        ) VALUES (%s, %s, %s, %s, %s)
                                        ON DUPLICATE KEY UPDATE
                                            prediction = VALUES(prediction),
                                            confidence = VALUES(confidence),
                                            prediction_time = VALUES(prediction_time)""",
                                        (
                                            event_id,
                                            expert_name,
                                            prediction,
                                            confidence,
                                            fallback_time,
                                        ),
                                    )
                                    expert_predictions_inserted += 1
                                    print(
                                        f"✅ Inserted expert prediction (basic): {expert_name} for {event_id}"
                                    )
                                except Exception as fallback_error:
                                    print(
                                        f"❌ Failed to insert expert prediction (fallback): {fallback_error}"
                                    )
                                    # Final minimal fallback with only required columns
                                    try:
                                        cursor.execute(
                                            "INSERT INTO expert_predictions (event_id, expert_name, prediction) VALUES (%s, %s, %s) ON DUPLICATE KEY UPDATE prediction = VALUES(prediction)",
                                            (
                                                event_id,
                                                expert_name,
                                                int(data.get("prediction", 1))
                                                if str(
                                                    data.get("prediction",
                                                             1)).isdigit() else
                                                1,
                                            ),
                                        )
                                        expert_predictions_inserted += 1
                                        print(
                                            f"✅ Inserted expert prediction (minimal) for {expert_name} → {event_id}"
                                        )
                                    except Exception as final_err:
                                        print(
                                            f"🚨 All expert prediction insert attempts failed for {expert_name}: {final_err}"
                                        )
                        else:
                            print(
                                f"⚠️ Skipping empty expert name for {event_id}"
                            )

            # After all expert predictions are inserted, attempt to calculate and update dynamic confidence
            if event_id and expert_predictions_inserted > 0:
                print(
                    f"🧮 Attempting dynamic confidence calculation for {event_id} with {expert_predictions_inserted} expert predictions..."
                )
                try:
                    # Get all expert predictions for this event to calculate confidence based on their original values
                    cursor.execute(
                        "SELECT expert_name, confidence FROM expert_predictions WHERE event_id = %s",
                        (event_id, ))
                    expert_records = cursor.fetchall()

                    if expert_records:
                        # Calculate dynamic confidence for each expert
                        for expert_name, original_confidence in expert_records:
                            original_confidence_percentage = original_confidence * 100  # Convert to percentage

                            # --- Dynamic confidence calculation ---
                            dynamic_confidence_percentage = calculate_dynamic_confidence_vs_expert(
                                event_id, original_confidence_percentage)

                            print(
                                f"🔧 Dynamic confidence result: {dynamic_confidence_percentage:.1f}%"
                            )

                            # Only update if dynamic calculation succeeded (not the 50% default fallback)
                            if dynamic_confidence_percentage != 50.0:
                                confidence_decimal = dynamic_confidence_percentage / 100.0

                                # Update this specific expert's confidence
                                cursor.execute(
                                    "UPDATE expert_predictions SET confidence = %s WHERE event_id = %s AND expert_name = %s",
                                    (confidence_decimal, event_id,
                                     expert_name))
                                print(
                                    f"✅ Updated {expert_name} confidence: {original_confidence_percentage:.1f}% → {dynamic_confidence_percentage:.1f}%"
                                )
                            else:
                                print(
                                    f"⚠️ Dynamic confidence calculation failed for {expert_name} - keeping original {original_confidence_percentage:.1f}%"
                                )

                except Exception as conf_error:
                    print(
                        f"❌ Failed to calculate dynamic confidence: {conf_error}"
                    )
                    print(
                        "ℹ️ Expert predictions will keep their original confidence values"
                    )

            conn.commit()
            print(
                f"[SUCCESS] Transaction committed: {inserted} events, {expert_predictions_inserted} expert predictions"
            )

    except Exception as e:
        # The connection context manager should handle rollback on exception.
        print(f"[ERROR] Database transaction failed: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({
            "success": False,
            "message": f"Database transaction failed: {str(e)}"
        }), 500

    return jsonify({
        "success": True,
        "message":
        f"Successfully inserted {inserted} events, {inserted} crowd predictions, and {expert_predictions_inserted} expert predictions.",
        "event_id": event_id,
        "events_inserted": inserted,
        "crowd_predictions_inserted": inserted,
        "expert_predictions_inserted": expert_predictions_inserted
    })


@app.route("/api/experts", methods=["GET"])
def get_all_experts():
    """Return all distinct expert names from expert_predictions table."""
    try:
        with get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT DISTINCT expert_name FROM expert_predictions
                WHERE expert_name IS NOT NULL AND expert_name != ''
                ORDER BY expert_name
            """)
            rows = cursor.fetchall()
            experts = [row[0] for row in rows]
            return jsonify({"success": True, "experts": experts})
    except Exception as e:
        print(f"[ERROR] Error fetching all experts: {e}")
        return jsonify({
            "success": False,
            "message": str(e),
            "experts": []
        }), 500


@app.route("/api/experts_by_date", methods=["GET"])
def experts_by_date():
    """Return distinct expert names that have predictions for the given date."""
    target_date = request.args.get("date") or datetime.today().strftime(
        "%Y-%m-%d")
    try:
        with get_connection() as conn:
            cursor = conn.cursor()

            # Use JOIN with events table to filter by event_date (most reliable approach)
            cursor.execute(
                """
                SELECT DISTINCT ep.expert_name
                FROM expert_predictions ep
                JOIN events e ON ep.event_id = e.event_id
                WHERE e.event_date = %s
                AND ep.expert_name IS NOT NULL
                AND ep.expert_name != ''
            """, (target_date, ))
            rows = cursor.fetchall()
            experts = [row[0] for row in rows]
            return jsonify({
                "success": True,
                "date": target_date,
                "experts": experts
            })

    except Exception as e:
        print(f"[ERROR] Error fetching experts for {target_date}: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({
            "success": False,
            "message": str(e),
            "experts": []
        }), 500


# backfill_prediction_times route moved to routes/admin.py


@app.route("/api/event_handicappers/<event_id>", methods=["GET"])
def get_event_handicappers(event_id):
    """
    API endpoint to retrieve all handicappers who have made predictions for a specific event.

    Args:
        event_id (str): The event ID to get handicappers for.

    Returns:
        JSON response containing a list of handicapper names who have made predictions for this event.
    """
    try:
        conn = get_dict_connection()
        with conn:
            cursor = conn.cursor()
            cursor.execute(
                """
                SELECT DISTINCT expert_name
                FROM expert_predictions
                WHERE event_id = %s
            """, (event_id, ))
            rows = cursor.fetchall()

            handicappers = [row["expert_name"] for row in rows]

            return jsonify({
                "success": True,
                "handicappers": handicappers,
                "count": len(handicappers)
            })
    except Exception as e:
        print(f"❌ Error in /api/event_handicappers endpoint: {e}")
        return jsonify({
            "success": False,
            "message": f"Error retrieving handicappers: {str(e)}",
            "handicappers": []
        }), 500


@app.route("/api/cleanup_chart_generation", methods=["POST"])
def cleanup_chart_generation_endpoint():
    """Clean up phantom ChartGeneration expert predictions."""
    try:
        data = request.get_json() or {}
        if data.get("admin_password") != "ppadmin42":
            return jsonify({"success": False, "message": "Unauthorized"}), 401

        deleted_count = cleanup_chart_generation_predictions()

        return jsonify({
            "success": True,
            "message":
            f"Cleaned up {deleted_count} ChartGeneration predictions",
            "deleted_count": deleted_count
        })

    except Exception as e:
        return jsonify({"success": False, "message": f"Error: {str(e)}"}), 500


# get_main_model_prediction route moved to routes/model.py


@app.route("/api/player_chart/<event_id>", methods=["GET"])
def get_player_chart(event_id):
    """
    Comprehensive player chart endpoint with robust error handling and fallbacks.
    """
    import re
    import json
    import base64
    import io
    import matplotlib
    matplotlib.use('Agg')  # Use non-GUI backend for web server
    import matplotlib.pyplot as plt
    import numpy as np

    try:
        print(f"🔍 Generating chart for event_id: {event_id}")

        # Step 1: Robust event ID correction with double-letter fix
        corrected_event_id, corrections_made = correct_event_id_format_robust(
            event_id)
        print(f"🔧 After correction: {corrected_event_id}")
        print(f"📝 Corrections made: {corrections_made}")

        # Step 2: Try to get existing distribution data
        from data_science_modules.kde_distributions_port import fetch_distributions
        distribution_data = fetch_distributions(corrected_event_id)

        if distribution_data is not None:
            print(
                f"✅ Found existing distribution data for {corrected_event_id}")
            # Generate chart from existing data using PlotUtils
            plotter = PlotUtils()
            chart_base64 = plotter.unpack_distribution_data(corrected_event_id)
            return jsonify({
                "success": True,
                "event_id": corrected_event_id,
                "chart_data": chart_base64,
                "corrections": corrections_made,
                "data_source": "database"
            })

        print(f"⚠️ No distribution data found for {corrected_event_id}")

        # Step 3: Try to generate data via MainModel
        print(
            f"🔄 Attempting to generate distribution data via MainModel for {corrected_event_id}"
        )

        try:
            # Check if event exists in database before attempting MainModel
            import re
            pattern = r'^(\d{4}-\d{2}-\d{2})-([A-Z]+)-(.+?)(\d+(?:\.\d+)?)([A-Z]+)$'
            match = re.match(pattern, corrected_event_id)

            if match:
                date_part, league, player_name, threshold, stat_type = match.groups(
                )

                # Check if event exists (but don't create it)
                conn = get_connection()
                cursor = conn.cursor()
                cursor.execute("SELECT 1 FROM events WHERE event_id = %s",
                               (corrected_event_id, ))
                event_exists = cursor.fetchone() is not None
                conn.close()

                if event_exists:
                    print(f"✅ Event exists in database: {corrected_event_id}")
                    # Skip main_model to prevent phantom predictions during chart generation
                    model_result = None
                    if model_result is not None:
                        print(
                            f"✅ MainModel completed for {corrected_event_id}")

                        # Try to fetch distribution data again
                        distribution_data = fetch_distributions(
                            corrected_event_id)

                        if distribution_data is not None:
                            print(
                                f"✅ Distribution data saved for {corrected_event_id}"
                            )
                            chart_base64 = plotter.unpack_distribution_data(
                                corrected_event_id)
                            return jsonify({
                                "success":
                                True,
                                "event_id":
                                corrected_event_id,
                                "chart_data":
                                chart_base64,
                                "corrections":
                                corrections_made,
                                "data_source":
                                "generated_via_mainmodel"
                            })
                        else:
                            print(
                                f"⚠️ MainModel ran but no distribution data was saved for {corrected_event_id}"
                            )
                    else:
                        print(
                            f"❌ MainModel returned None for {corrected_event_id}"
                        )
                else:
                    print(
                        f"⚠️ Event does not exist in database: {corrected_event_id} - skipping MainModel"
                    )
            else:
                print(
                    f"❌ Could not parse event ID format: {corrected_event_id}")

        except Exception as model_error:
            print(
                f"❌ MainModel failed for {corrected_event_id}: {model_error}")
            import traceback
            traceback.print_exc()

        # Step 4: Try alternative chart generation from historical data
        print(
            f"📊 Attempting alternative chart generation from historical data for {corrected_event_id}"
        )
        from data_science_modules.planet_scale_port import generate_chart_from_historical_data

        historical_chart_result = generate_chart_from_historical_data(
            corrected_event_id)

        if historical_chart_result["success"] and historical_chart_result[
                "data_source"] != "placeholder":
            print(
                f"✅ Generated chart from historical data for {corrected_event_id}"
            )
            return jsonify({
                "success":
                True,
                "event_id":
                corrected_event_id,
                "chart_data":
                historical_chart_result["chart_data"],
                "corrections":
                corrections_made,
                "data_source":
                "historical_data",
                "stats":
                historical_chart_result.get("stats", {}),
                "message":
                "Chart generated from historical player data"
            })

        # Step 5: Final fallback to placeholder chart
        print(f"📊 Generating placeholder chart for {corrected_event_id}")
        placeholder_chart = generate_placeholder_chart_simple(
            corrected_event_id)

        return jsonify({
            "success":
            True,
            "event_id":
            corrected_event_id,
            "chart_data":
            placeholder_chart,
            "corrections":
            corrections_made,
            "data_source":
            "placeholder",
            "message":
            "Chart generated with placeholder data - no historical data available"
        })

    except Exception as e:
        print(f"❌ Error in player chart endpoint for {event_id}: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({
            "success": False,
            "event_id": event_id,
            "message": f"Error generating chart: {str(e)}",
            "chart_data": None
        }), 500


def correct_event_id_format_robust(event_id):
    """
    Robust event ID correction that handles double letter corruption and other issues.
    """
    import re

    corrections = []
    original_event_id = event_id

    print(f"🔍 Input event ID: {event_id}")

    # Step 1: Handle specific league corruption patterns only
    # Only fix known league corruptions, avoid touching player names
    league_fixes = {'NNBA': 'NBA', 'MMLB': 'MLB', 'NNFL': 'NFL', 'NNHL': 'NHL'}

    fixed_doubles = event_id
    for corrupt, correct in league_fixes.items():
        if f'-{corrupt}-' in event_id:
            fixed_doubles = event_id.replace(f'-{corrupt}-', f'-{correct}-')
            break

    if fixed_doubles != event_id:
        corrections.append(
            f"Fixed league corruption: '{event_id}' -> '{fixed_doubles}'")
        event_id = fixed_doubles

    # Step 2: Handle date corruption (20255 -> 2025)
    fixed_date = re.sub(r'(\d{4})\d+-(\d{2}-\d{2})', r'\1-\2', event_id)
    if fixed_date != event_id:
        corrections.append(
            f"Fixed date corruption: '{event_id}' -> '{fixed_date}'")
        event_id = fixed_date

    # Step 3: Standard event ID format correction
    pattern = r'^(\d{4}-\d{2}-\d{2})-([A-Z]+)-(.+?)(\d+(?:\.\d+)?)(.*?)$'
    match = re.match(pattern, event_id.upper())

    if not match:
        corrections.append(f"Invalid event ID format: {event_id}")
        return event_id, corrections

    date_part, league, player_part, threshold_part, stat_part = match.groups()

    # Fix player name: only remove spaces, keep periods for initials like "S.CURRY"
    original_player = player_part
    clean_player = player_part.replace(" ", "")
    if clean_player != original_player:
        corrections.append(
            f"Cleaned player name: '{original_player}' -> '{clean_player}'")

    # Fix threshold format: ensure decimal point
    original_threshold = threshold_part
    try:
        threshold_float = float(threshold_part)
        formatted_threshold = f"{threshold_float:.1f}"
        if formatted_threshold != original_threshold:
            corrections.append(
                f"Fixed threshold format: '{original_threshold}' -> '{formatted_threshold}'"
            )
        threshold_part = formatted_threshold
    except ValueError:
        corrections.append(f"Invalid threshold format: '{original_threshold}'")

    # Map stat types to standard abbreviations by league
    original_stat = stat_part
    stat_type_map = {
        'POINTS': 'PTS',
        'POINT': 'PTS',
        'PTS': 'PTS',
        'REBOUNDS': 'REB',
        'REBOUND': 'REB',
        'REB': 'REB',
        'ASSISTS': 'AST',
        'ASSIST': 'AST',
        'AST': 'AST',
        'HOMERUNS': 'HR',
        'HOMERUN': 'HR',
        'HR': 'HR',
        'HITS': 'H',
        'HIT': 'H',
        'H': 'H',
        'RBI': 'RBI',
        'RUNS': 'R',
        'RUN': 'R',
        'R': 'R',
        'STRIKEOUTS': 'K',
        'STRIKEOUT': 'K',
        'K': 'K',
    }

    # Handle special case: MLB events using "POINTS" should map to appropriate MLB stats
    if league == 'MLB' and original_stat == 'POINTS':
        corrected_stat = 'H'
        corrections.append(
            f"Fixed MLB stat type: 'POINTS' -> 'H' (MLB doesn't use points)")
    else:
        corrected_stat = stat_type_map.get(original_stat.upper(),
                                           original_stat.upper())
        if corrected_stat != original_stat.upper():
            corrections.append(
                f"Standardized stat type: '{original_stat}' -> '{corrected_stat}'"
            )

    # Reconstruct the corrected event ID
    corrected_event_id = f"{date_part}-{league}-{clean_player}{threshold_part}{corrected_stat}"

    if corrected_event_id != original_event_id:
        corrections.append(
            f"Final correction: '{original_event_id}' -> '{corrected_event_id}'"
        )

    return corrected_event_id, corrections


def generate_placeholder_chart_simple(event_id):
    """
    Generate a simple placeholder chart when no data is available.
    """
    import matplotlib
    matplotlib.use('Agg')  # Use non-GUI backend for web server
    import matplotlib.pyplot as plt
    import numpy as np
    import io
    import base64
    import re

    try:
        # Parse event ID to get player info
        pattern = r'^(\d{4}-\d{2}-\d{2})-([A-Z]+)-(.+?)(\d+(?:\.\d+)?)([A-Z]+)$'
        match = re.match(pattern, event_id)

        if match:
            date_part, league, player_name, threshold, stat_type = match.groups(
            )
            title = f"{player_name} - {stat_type} Distribution"
            subtitle = f"Threshold: {threshold} | League: {league} | Date: {date_part}"
            threshold_val = float(threshold)
        else:
            title = "Player Performance Distribution"
            subtitle = f"Event ID: {event_id}"
            threshold_val = 5.0

        # Create a simple placeholder chart
        fig, ax = plt.subplots(figsize=(10, 6))

        # Generate sample distribution for visualization
        x = np.linspace(0, threshold_val * 2, 100)
        y = np.exp(-(x - threshold_val)**2 / 2) / np.sqrt(2 * np.pi)

        ax.plot(x, y, color='gray', linewidth=2, alpha=0.7, linestyle='--')
        ax.fill_between(x, y, alpha=0.2, color='gray')

        # Add threshold line
        ax.axvline(threshold_val,
                   color='red',
                   linestyle='-',
                   linewidth=2,
                   label=f'Threshold: {threshold_val}')
        ax.legend()

        ax.set_xlabel(stat_type if match else 'Stat Value')
        ax.set_ylabel('Probability Density')
        ax.set_title(title)
        ax.text(0.5,
                0.95,
                subtitle,
                transform=ax.transAxes,
                ha='center',
                va='top',
                fontsize=10,
                alpha=0.7)
        ax.text(0.5,
                0.5,
                'No Historical Data Available\nPlaceholder Distribution Shown',
                transform=ax.transAxes,
                ha='center',
                va='center',
                fontsize=12,
                alpha=0.5,
                bbox=dict(boxstyle="round,pad=0.3",
                          facecolor="yellow",
                          alpha=0.3))

        plt.grid(True, alpha=0.3)
        plt.tight_layout()

        # Save to base64
        buf = io.BytesIO()
        plt.savefig(buf, format='png', dpi=150, bbox_inches='tight')
        plt.close(fig)
        buf.seek(0)

        encoded = base64.b64encode(buf.read()).decode('utf-8')
        return encoded

    except Exception as e:
        print(f"❌ Error generating placeholder chart: {e}")
        # Return a minimal base64 encoded 1x1 pixel image as fallback
        return "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="
    """
    API endpoint to get player statistical distribution chart for a specific event.

    Args:
        event_id (str): The event ID to get chart for.

    Returns:
        JSON response containing base64-encoded chart image.
    """
    try:
        print(f"🔍 Generating chart for event_id: {event_id}")

        # Basic event ID format correction
        corrected_event_id, corrections_made = correct_event_id_format(
            event_id)
        print(f"🔧 After correction: {corrected_event_id}")
        print(f"📝 Corrections made: {corrections_made}")

        # Check if distribution data exists in database
        from data_science_modules.kde_distributions_port import fetch_distributions
        distribution_data = fetch_distributions(corrected_event_id)

        if distribution_data is None:
            print(
                f"⚠️ No distribution data found for {corrected_event_id}, attempting to generate via MainModel"
            )

            # First, ensure the event exists in the database with basic data
            # MainModel requires event to exist with crowd probability
            try:
                # Parse event ID to extract components
                import re
                pattern = r'^(\d{4}-\d{2}-\d{2})-([A-Z]+)-(.+?)(\d+(?:\.\d+)?)([A-Z]+)$'
                match = re.match(pattern, corrected_event_id)

                if match:
                    date_part, league, player_name, threshold, stat_type = match.groups(
                    )

                    # Check if event exists (but don't create it)
                    conn = get_connection()
                    cursor = conn.cursor()
                    cursor.execute("SELECT 1 FROM events WHERE event_id = %s",
                                   (corrected_event_id, ))
                    event_exists = cursor.fetchone() is not None
                    conn.close()

                    if not event_exists:
                        print(
                            f"⚠️ Event does not exist in database: {corrected_event_id} - skipping historical chart generation"
                        )
                        # Don't create database entries - skip to placeholder chart
                        raise Exception(
                            f"Event {corrected_event_id} does not exist in database"
                        )

                else:
                    print(
                        f"❌ Could not parse event ID format: {corrected_event_id}"
                    )
                    return jsonify({
                        "success": False,
                        "event_id": corrected_event_id,
                        "message":
                        f"Invalid event ID format: {corrected_event_id}",
                        "corrections": corrections_made,
                        "chart_data": None
                    }), 400

            except Exception as db_error:
                print(f"❌ Failed to create event record: {db_error}")
                # Continue anyway - MainModel might still work

            # Skip main_model to prevent phantom predictions during chart generation
            try:
                model_result = None
                if model_result is not None:
                    print(
                        f"✅ MainModel generated data for {corrected_event_id}")
                    # After MainModel runs, it should have saved distribution data to the database
                    # Try fetching again
                    distribution_data = fetch_distributions(corrected_event_id)

                    if distribution_data is None:
                        print(
                            f"⚠️ MainModel ran but no distribution data was saved for {corrected_event_id}"
                        )
                        return jsonify({
                            "success": False,
                            "event_id": corrected_event_id,
                            "message":
                            "MainModel completed but no distribution data was generated. This may indicate the KDE step failed.",
                            "corrections": corrections_made,
                            "chart_data": None
                        }), 404
                else:
                    print(
                        f"❌ MainModel returned None for {corrected_event_id}")
                    return jsonify({
                        "success": False,
                        "event_id": corrected_event_id,
                        "message":
                        "Unable to generate distribution data - MainModel returned None",
                        "corrections": corrections_made,
                        "chart_data": None
                    }), 404

            except Exception as model_error:
                print(
                    f"❌ MainModel failed for {corrected_event_id}: {model_error}"
                )
                return jsonify({
                    "success": False,
                    "event_id": corrected_event_id,
                    "message":
                    f"Failed to generate distribution data: {str(model_error)}",
                    "corrections": corrections_made,
                    "chart_data": None
                }), 500

        # Generate chart using PlotUtils
        plotter = PlotUtils()
        chart_base64 = plotter.unpack_distribution_data(corrected_event_id)

        return jsonify({
            "success":
            True,
            "event_id":
            corrected_event_id,
            "chart_data":
            chart_base64,
            "corrections":
            corrections_made,
            "data_source":
            "database" if distribution_data else "generated"
        })

    except Exception as e:
        print(f"❌ Error in /api/player_chart endpoint for {event_id}: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({
            "success": False,
            "event_id": event_id,
            "message": f"Error generating chart: {str(e)}",
            "chart_data": None
        }), 500


def calculate_dynamic_confidence(event_id: str,
                                 expert_confidence: float) -> float:
    """
    Calculate dynamic confidence based on ML model prediction vs expert confidence.

    Args:
        event_id (str): The event ID to get ML prediction for
        expert_confidence (float): Expert confidence as decimal (0.0-1.0)

    Returns:
        float: Scaled confidence value (0-100)

    Steps:
    1. Call main_model(event_id) to get ML predictions
    2. Extract combined_probability or team_kde_prob from model output
    3. Calculate difference between model probability and expert confidence
    4. Scale difference to 0-100% where -25% = 0% and 25% = 100% (i.e., -0.25 to +0.25 in probability terms)
    """
    try:
        print(
            f"🔍 Running main_model for dynamic confidence calculation: {event_id}"
        )

        # Step 1: Call main_model to get ML predictions
        model_result = main_model(event_id)

        if not model_result:
            print(
                f"❌ Model returned None for {event_id}, using fallback confidence"
            )
            return expert_confidence * 100  # Return original expert confidence as percentage

        if isinstance(model_result, dict) and "error" in model_result:
            print(
                f"❌ Model returned error for {event_id}: {model_result.get('error', 'Unknown error')}"
            )
            return expert_confidence * 100  # Return original expert confidence as percentage

        # Step 2: Extract combined_probability or team_kde_prob from model output
        model_prob = model_result.get("combined_prob")
        if model_prob is None:
            model_prob = model_result.get("team_kde_prob")

        if model_prob is None:
            print(
                f"❌ No model probability found in result for {event_id}, using fallback confidence"
            )
            return expert_confidence * 100  # Return original expert confidence as percentage

        print(f"📊 Model probability: {model_prob:.4f}")
        print(f"👤 Expert confidence: {expert_confidence:.4f}")

        # Step 3: Calculate difference between model probability and expert confidence
        difference = model_prob - expert_confidence
        print(f"📈 Difference (Model - Expert): {difference:.4f}")

        # Step 4: Scale difference to 0-100% where -25% = 0% and 25% = 100%
        # Clamp difference to [-0.25, 0.25] range for mapping bounds
        clamped_difference = max(-0.25, min(0.25, difference))

        # Formula: scaled_confidence = ((clamped_difference + 0.25) / 0.5) * 100
        scaled_confidence = ((clamped_difference + 0.25) / 0.5) * 100
        # Guard rails
        scaled_confidence = max(0.0, min(100.0, scaled_confidence))

        print(
            f"🎯 Calculated dynamic confidence: {scaled_confidence:.1f}% (clamped difference: {clamped_difference:.4f})"
        )
        return scaled_confidence

    except Exception as e:
        print(f"❌ Error calculating dynamic confidence for {event_id}: {e}")
        import traceback
        traceback.print_exc()
        return expert_confidence * 100  # Return original expert confidence as percentage


def cleanup_chart_generation_predictions():
    """Clean up phantom ChartGeneration expert predictions."""
    try:
        from data_science_modules.planet_scale_port import get_connection
        conn = get_connection()
        cursor = conn.cursor()

        cursor.execute("DELETE FROM expert_predictions WHERE expert_name = %s",
                       ("ChartGeneration", ))
        deleted_count = cursor.rowcount

        conn.commit()
        conn.close()

        return deleted_count

    except Exception as e:
        print(f"Error cleaning up ChartGeneration predictions: {e}")
        return 0


# ------------------------
# Unknown teams cleanup helpers and endpoint
# ------------------------
def _placeholder_set() -> set[str]:
    return {"", "unknown", "null", "none", "other"}


def _is_placeholder(value: Optional[str]) -> bool:
    return (value or "").strip().lower() in _placeholder_set()


def cleanup_unknown_team_rows(limit: int = 100) -> dict:
    """
    Sweep recent team-based events where either side is Unknown/blank and
    attempt to fill using DB inference, then The Odds API. If the corrected
    teams imply a different canonical event_id, migrate dependent rows.

    Returns a summary dict with counts and per-row results.
    """
    try:
        from data_science_modules.planet_scale_port import get_connection
    except Exception as e:
        return {"success": False, "message": f"DB unavailable: {e}", "updated": 0, "migrated": 0, "checked": 0, "details": []}

    details: list[dict] = []
    updated = 0
    migrated = 0
    checked = 0

    with get_connection() as conn:
        cur = conn.cursor()
        cur.execute(
            """
            SELECT event_id, event_date, league, team_a, team_b, pick_type
            FROM events
            WHERE pick_type IN ('MoneyLine','Spread')
              AND (
                    team_a IS NULL OR team_b IS NULL OR
                    LOWER(team_a) IN ('', 'unknown', 'null', 'none', 'other') OR
                    LOWER(team_b) IN ('', 'unknown', 'null', 'none', 'other')
                  )
            ORDER BY event_date DESC
            LIMIT %s
            """,
            (int(limit),),
        )
        rows = cur.fetchall() or []

        for (event_id, event_date, league, team_a, team_b, pick_type) in rows:
            checked += 1
            old_a = team_a or ""
            old_b = team_b or ""

            new_a, new_b = fill_missing_teams(league, event_date, old_a, old_b)

            if _is_placeholder(new_a) or _is_placeholder(new_b):
                details.append({
                    "event_id": event_id,
                    "league": league,
                    "event_date": str(event_date),
                    "old_a": old_a,
                    "old_b": old_b,
                    "new_a": new_a,
                    "new_b": new_b,
                    "action": "skipped_unresolved"
                })
                continue

            corrected_id = generate_admin_event_id(
                event_date=str(event_date),
                league=league,
                pick_type=pick_type or "MoneyLine",
                team_a=new_a,
                team_b=new_b,
                player_name=None,
                stat_threshold=None,
                stat_type=None,
            )

            if corrected_id != event_id:
                _migrate_event_and_predictions(event_id, corrected_id, new_a, new_b)
                migrated += 1
                details.append({
                    "event_id": event_id,
                    "new_event_id": corrected_id,
                    "league": league,
                    "event_date": str(event_date),
                    "old_a": old_a,
                    "old_b": old_b,
                    "new_a": new_a,
                    "new_b": new_b,
                    "action": "migrated"
                })
            else:
                try:
                    cur2 = conn.cursor()
                    cur2.execute(
                        "UPDATE events SET team_a=%s, team_b=%s WHERE event_id=%s",
                        (new_a, new_b, event_id),
                    )
                    cur2.execute(
                        """
                        UPDATE expert_predictions
                        SET team=%s
                        WHERE event_id=%s AND (team IS NULL OR team='' OR LOWER(team) IN ('unknown','null','none','other'))
                        """,
                        (new_a, event_id),
                    )
                    conn.commit()
                    updated += 1
                    details.append({
                        "event_id": event_id,
                        "league": league,
                        "event_date": str(event_date),
                        "old_a": old_a,
                        "old_b": old_b,
                        "new_a": new_a,
                        "new_b": new_b,
                        "action": "updated"
                    })
                except Exception as _e:
                    conn.rollback()
                    details.append({
                        "event_id": event_id,
                        "league": league,
                        "event_date": str(event_date),
                        "old_a": old_a,
                        "old_b": old_b,
                        "new_a": new_a,
                        "new_b": new_b,
                        "action": "error",
                        "error": str(_e),
                    })

    return {
        "success": True,
        "message": "Cleanup completed",
        "checked": checked,
        "updated": updated,
        "migrated": migrated,
        "details": details,
    }


@app.route("/api/external/cleanup_unknown_teams", methods=["POST"])
@require_api_key
def external_cleanup_unknown_teams():
    try:
        limit_str = request.args.get("limit", "100").strip()
        try:
            limit = max(1, int(limit_str))
        except ValueError:
            limit = 100

        summary = cleanup_unknown_team_rows(limit=limit)
        status = 200 if summary.get("success") else 500
        return jsonify(summary), status
    except Exception as e:
        return jsonify({"success": False, "message": str(e)}), 500


@app.route("/api/external/format_projections_csv", methods=["POST"])
@require_api_key
def format_projections_csv():
    """
    External API endpoint for converting JSON projection data to formatted CSV strings.
    Takes JSON data containing hitter_projections, pitcher_projections, game_summary, and rfi data
    and returns them as clean CSV strings with minimal extra characters.

    Requires X-API-Key header for authentication.

    Expected input format (accepts either):
    Option 1 (direct JSON object):
    {
        "hitter_projections": {...},
        "pitcher_projections": {...},
        "game_summary": {...},
        "rfi": {...}
    }
    Option 2 (legacy stringified JSON):
    {
        "json_data": "stringified JSON containing projection data"
    }

    Returns:
    {
        "success": True,
        "hitter_projections_csv": "CSV string",
        "pitcher_projections_csv": "CSV string",
        "game_summary_csv": "CSV string",
        "rfi_csv": "CSV string"
    }
    """
    try:
        # Get the JSON data from request
        if not request.is_json:
            return jsonify({
                "success": False,
                "message": "Request must be JSON"
            }), 400

        data = request.get_json()
        # Handle cases where Make.com sends a JSON string (double-encoded JSON)
        if isinstance(data, str):
            try:
                # Try comprehensive repair first
                repaired = repair_json(data)
                data = json.loads(repaired)
            except Exception:
                # Fallback to Make.com specific repair
                try:
                    repaired = repair_makecom_projections(data)
                    data = json.loads(repaired)
                except Exception as e:
                    # Last resort: try parsing original string
                    try:
                        data = json.loads(data)
                    except Exception as final_e:
                        return jsonify({
                            "success": False,
                            "message": f"Invalid JSON body string: {str(final_e)}",
                            "debug": {
                                "original_error": str(e),
                                "repair_error": str(final_e),
                                "snippet": data[:500] if len(data) > 500 else data
                            }
                        }), 400
        if not data:
            return jsonify({
                "success": False,
                "message": "Missing request body"
            }), 400

        # Check if this is the legacy format with json_data field
        if "json_data" in data:
            json_string = data["json_data"]
            if not isinstance(json_string, str):
                return jsonify({
                    "success": False,
                    "message": "'json_data' must be a string"
                }), 400

            # Parse the nested JSON string
            try:
                # Try comprehensive repair first
                repaired_inner = repair_json(json_string)
                parsed_data = json.loads(repaired_inner)
            except Exception:
                # Fallback to Make.com specific repair
                try:
                    repaired_inner = repair_makecom_projections(json_string)
                    parsed_data = json.loads(repaired_inner)
                except Exception as e:
                    # Last resort: try parsing original string
                    try:
                        parsed_data = json.loads(json_string)
                    except json.JSONDecodeError as final_e:
                        return jsonify({
                            "success": False,
                            "message": f"Invalid JSON in json_data: {str(final_e)}",
                            "debug": {
                                "original_error": str(e),
                                "parse_error": str(final_e),
                                "snippet": json_string[:500] if len(json_string) > 500 else json_string
                            }
                        }), 400
        else:
            # Direct JSON object format
            parsed_data = data

        # Extract each section and convert to CSV
        csv_results = {}

        # Process hitter projections
        if isinstance(parsed_data, str):
            # If still a string here, it's malformed (string indices with str keys would fail)
            try:
                parsed_data = json.loads(parsed_data)
            except Exception:
                return jsonify({
                    "success": False,
                    "message": "Malformed payload: expected JSON object but received string"
                }), 400

        if "hitter_projections" in parsed_data:
            hitter_csv = _convert_dict_to_csv(parsed_data["hitter_projections"])
            csv_results["hitter_projections_csv"] = hitter_csv

        # Process pitcher projections
        if "pitcher_projections" in parsed_data:
            pitcher_csv = _convert_dict_to_csv(parsed_data["pitcher_projections"])
            csv_results["pitcher_projections_csv"] = pitcher_csv

        # Process game summary
        if "game_summary" in parsed_data:
            game_csv = _convert_dict_to_csv(parsed_data["game_summary"])
            csv_results["game_summary_csv"] = game_csv

        # Process RFI data
        if "rfi" in parsed_data:
            rfi_csv = _convert_dict_to_csv(parsed_data["rfi"])
            csv_results["rfi_csv"] = rfi_csv

        return jsonify({
            "success": True,
            **csv_results
        }), 200

    except Exception as e:
        print(f"[ERROR] Error in format_projections_csv: {e}")
        import traceback
        print(f"[ERROR] Traceback: {traceback.format_exc()}")
        return jsonify({
            "success": False,
            "message": f"Server error: {str(e)}",
            "debug": {
                "error_type": type(e).__name__,
                "traceback": traceback.format_exc()
            }
        }), 500


@app.route("/api/external/ingest_mlb_sheets", methods=["POST"])
@require_api_key
def ingest_mlb_sheets():
    """
    Deterministic ingestion of MLB projections from Google Sheets sections without LLMs.

    Accepts a JSON body containing any of the following keys (at least pitcher_projections and
    game_summary are recommended for reliable ingestion):

    {
      "hitter_projections": list[dict] | str,  # optional
      "pitcher_projections": list[dict] | str, # required for pitcher rows
      "game_summary": list[dict] | str,        # required for team/SP mapping
      "rfi": list[dict] | str                  # optional, enriches xRFI and xRFI_Chance
    }

    Notes:
    - This endpoint currently ingests PITCHER rows only, as hitter rows in the source sheet do not
      contain explicit team or game mapping. If hitter-team mapping is added to the Sheet, this
      endpoint can be extended to ingest hitters deterministically.
    - game_date is set to the current California date.
    """
    try:
        if not request.is_json:
            return jsonify({"success": False, "message": "Request must be JSON"}), 400

        payload = request.get_json(silent=True)
        if isinstance(payload, str):
            try:
                repaired = repair_json(payload)
                payload = json.loads(repaired)
            except Exception:
                try:
                    repaired = repair_makecom_projections(payload)
                    payload = json.loads(repaired)
                except Exception:
                    try:
                        payload = json.loads(payload)
                    except Exception as e:
                        return jsonify({
                            "success": False,
                            "message": f"Invalid JSON body string: {str(e)}"
                        }), 400
        # Handle Make.com or client wrappers that send stringified JSON under a 'data' field
        if isinstance(payload, dict) and isinstance(payload.get("data"), str):
            body_str = payload.get("data")
            try:
                repaired = repair_json(body_str)
                payload = json.loads(repaired)
            except Exception:
                try:
                    repaired = repair_makecom_projections(body_str)
                    payload = json.loads(repaired)
                except Exception:
                    try:
                        payload = json.loads(body_str)
                    except Exception as e:
                        return jsonify({
                            "success": False,
                            "message": f"Invalid JSON body in 'data': {str(e)}"
                        }), 400
        if not payload:
            payload = {}

        def _normalize_section(section):
            # Accept None
            if section is None:
                return []
            # If already list of dict rows
            if isinstance(section, list):
                return [r for r in section if isinstance(r, dict)]
            # If dict with numeric keys -> convert to list ordered by key
            if isinstance(section, dict):
                numeric_keys = []
                for k in section.keys():
                    try:
                        numeric_keys.append(int(str(k)))
                    except Exception:
                        # not a numeric index; treat as a single row dict
                        return [section]
                numeric_keys.sort()
                rows = []
                for nk in numeric_keys:
                    row = section.get(str(nk))
                    if isinstance(row, dict):
                        rows.append(row)
                return rows
            # If string, try JSON parse first
            if isinstance(section, str):
                try:
                    parsed = json.loads(section)
                    return _normalize_section(parsed)
                except Exception:
                    # Fallback: treat as CSV lines
                    rows = []
                    for line in section.splitlines():
                        if not line.strip():
                            continue
                        cols = line.split(",")
                        row = {str(i): c.strip() for i, c in enumerate(cols)}
                        rows.append(row)
                    return rows
            # Unknown type
            return []

        hitters_rows = _normalize_section(payload.get("hitter_projections"))
        pitchers_rows = _normalize_section(payload.get("pitcher_projections"))
        summary_rows = _normalize_section(payload.get("game_summary"))
        rfi_rows = _normalize_section(payload.get("rfi"))

        if not pitchers_rows or not summary_rows:
            return jsonify({
                "success": False,
                "message": "Missing required sections: pitcher_projections and game_summary are required"
            }), 400

        # Drop header rows if present: detect by typical header titles
        def _drop_header(rows, header_probe_keys):
            if not rows:
                return rows
            first = rows[0]
            for k in header_probe_keys:
                if any(isinstance(first.get(col), str) and k in first.get(col, "") for col in first.keys()):
                    return rows[1:]
            return rows

        pitchers_rows = _drop_header(pitchers_rows, ["Pitcher Name", "xPitches", "Pitches", "xStrikeouts"])
        summary_rows = _drop_header(summary_rows, ["Game Number", "Away Team", "Home Team", "Away SP", "Home SP"])
        rfi_rows = _drop_header(rfi_rows, ["xRFI", "Chance of RFI"]) if rfi_rows else []

        # Build game metadata keyed by game number (column "0")
        game_meta_by_no = {}
        for row in summary_rows:
            try:
                game_no = str(row.get("0", "")).strip()
                if not game_no:
                    continue
                game_meta_by_no[game_no] = {
                    "away_team": row.get("1"),
                    "home_team": row.get("2"),
                    "away_sp": row.get("3"),
                    "home_sp": row.get("4"),
                    "away_score": row.get("5"),
                    "home_score": row.get("6"),
                    "total_runs_scored": row.get("7"),
                    "winning_team": row.get("8"),
                    "game_xHRs": row.get("9"),
                    "home_win_pct": row.get("10"),
                    # OU at col 13 if needed: row.get("13")
                }
            except Exception:
                continue

        # Merge in RFI by same game number
        rfi_by_no = {}
        for row in rfi_rows or []:
            try:
                game_no = str(row.get("0", "")).strip()
                if not game_no:
                    continue
                rfi_by_no[game_no] = {
                    "xRFI": row.get("3"),
                    "xRFI_Chance": row.get("4"),
                }
            except Exception:
                continue

        # Helper to coerce numeric strings to float
        def _num(v):
            if v in (None, ""):
                return None
            if isinstance(v, (int, float)):
                return float(v)
            s = str(v).strip().rstrip("%")
            try:
                return float(s)
            except Exception:
                return None

        today_ca = get_current_california_date()

        # Cell access supporting string and int keys
        def _cell(row: dict, idx: int):
            if row is None:
                return None
            if str(idx) in row:
                return row.get(str(idx))
            return row.get(idx)

        # --- Build helper to match teams reliably ---
        def _nickname(s: str) -> str:
            s = (s or "").strip().lower()
            if not s:
                return ""
            # special multi-word nicknames
            multi = [
                "red sox", "white sox", "blue jays"
            ]
            for m in multi:
                if m in s:
                    return m
            parts = s.split()
            return parts[-1] if parts else s

        def _teams_match_relaxed(a: str, b: str) -> bool:
            if not a or not b:
                return False
            la, lb = a.lower(), b.lower()
            if la == lb or la in lb or lb in la:
                return True
            na, nb = _nickname(la), _nickname(lb)
            return bool(na and nb and (na == nb))

        # Build pitcher projections
        def _clean_name_for_match(name):
            if not name:
                return ""
            s = str(name)
            s = s.replace('*', '').replace('#', '').strip()
            return s.lower()

        def _norm_name(n: str) -> str:
            n = (n or "").strip()
            try:
                import unicodedata
                n = unicodedata.normalize('NFKD', n)
            except Exception:
                pass
            n = n.replace('*', '').replace('#', '').replace('"', '').replace("'", "").strip()
            return n

        projections = []
        for row in pitchers_rows:
            raw_pitcher_name = (_cell(row, 0) or "").strip()
            pitcher_name = raw_pitcher_name
            if not pitcher_name:
                continue

            # Find matching game by SP name
            matched_meta = None
            matched_game_no = None
            for gno, meta in game_meta_by_no.items():
                p = _clean_name_for_match(pitcher_name)
                a = _clean_name_for_match(meta.get("away_sp"))
                h = _clean_name_for_match(meta.get("home_sp"))
                if p and (p == a or p == h):
                    matched_meta = meta
                    matched_game_no = gno
                    break

            if not matched_meta:
                # If we cannot confidently map to a game, skip to avoid bad keys
                continue

            rfi_meta = rfi_by_no.get(matched_game_no, {})

            away_team = matched_meta.get("away_team")
            home_team = matched_meta.get("home_team")
            away_sp = matched_meta.get("away_sp")
            home_sp = matched_meta.get("home_sp")

            # Determine which side this pitcher is on
            is_away_sp = _clean_name_for_match(pitcher_name) == _clean_name_for_match(away_sp)
            is_home_sp = _clean_name_for_match(pitcher_name) == _clean_name_for_match(home_sp)

            # Calculate home_team_run_diff if scores are available
            away_score = _num(matched_meta.get("away_score"))
            home_score = _num(matched_meta.get("home_score"))
            home_team_run_diff = None
            if home_score is not None and away_score is not None:
                home_team_run_diff = home_score - away_score

            # Determine winning team: prefer sheet value unless it's missing/placeholder
            winning_raw = (matched_meta.get("winning_team") or "").strip()
            computed_winner = None
            if home_score is not None and away_score is not None:
                if home_score > away_score:
                    computed_winner = home_team
                elif away_score > home_score:
                    computed_winner = away_team
                else:
                    computed_winner = None  # tie/unknown
            if not winning_raw or "no ml" in winning_raw.lower():
                winning_team_val = team_to_nickname(computed_winner)
            else:
                winning_team_val = team_to_nickname(winning_raw)

            projection = {
                "game_date": today_ca,
                "away_team": team_to_nickname(away_team),
                "home_team": team_to_nickname(home_team),
                "away_sp": _norm_name(away_sp),
                "home_sp": _norm_name(home_sp),
                "away_score": away_score,
                "home_score": home_score,
                "total_runs_scored": _num(matched_meta.get("total_runs_scored")),
                "home_team_run_diff": home_team_run_diff,
                "winning_team": winning_team_val,
                "game_xHRs": _num(matched_meta.get("game_xHRs")),
                "home_win_pct": _num(matched_meta.get("home_win_pct")),
                "xRFI": _num(rfi_meta.get("xRFI")),
                "player_name": _norm_name(pitcher_name),
                "is_pitcher": 1,
                "AB": None,
                "Hits": None,
                "Singles": None,
                "Doubles": None,
                "Triples": None,
                "Homeruns": None,
                "TB": None,
                "Walks": None,
                "SB": None,
                "Runs": None,
                "RBIs": None,
                "HRR": None,
                "OBP": None,
                "HitterStrikeouts": None,
                "Pitches": _num(_cell(row, 1)),
                "Outs": _num(_cell(row, 2)),
                "BF": _num(_cell(row, 3)),
                "Strikeouts": _num(_cell(row, 4)),
                "PitcherWalks": _num(_cell(row, 5)),
                "ER": _num(_cell(row, 6)),
                "HA": _num(_cell(row, 7)),
                "xRFI_Chance": _num(rfi_meta.get("xRFI_Chance")),
            }

            projections.append(projection)

        if not projections:
            return jsonify({
                "success": False,
                "message": "No pitcher projections could be mapped to games (check SP names match Game Summary)"
            }), 400

        # ---------------- HITTER INGESTION (no sheet change required) ----------------
        # Attempt to map hitter names to teams using MLB Stats API active rosters and then to games
        hitter_projections = []
        try:
            import unicodedata
            def _norm_name(n: str) -> str:
                n = (n or "").strip()
                try:
                    n = unicodedata.normalize('NFKD', n)
                except Exception:
                    pass
                # strip markers and quotes
                n = n.replace('*', '').replace('#', '').replace('"', '').replace("'", "").strip()
                return n

            # Fetch team list
            season_year = datetime.now(ZoneInfo("America/Los_Angeles")).year
            teams_resp = requests.get(
                f"https://statsapi.mlb.com/api/v1/teams?sportId=1&season={season_year}", timeout=10
            )
            teams_resp.raise_for_status()
            teams_data = teams_resp.json().get("teams", [])

            team_id_to_name = {t["id"]: t.get("name") for t in teams_data if isinstance(t, dict) and t.get("id")}
            player_to_team = {}
            # Pull active roster per team (best-effort)
            for tid, tname in list(team_id_to_name.items()):
                try:
                    r = requests.get(
                        f"https://statsapi.mlb.com/api/v1/teams/{tid}/roster?season={season_year}", timeout=8
                    )
                    r.raise_for_status()
                    roster = r.json().get("roster", [])
                    for p in roster:
                        person = (p or {}).get("person", {})
                        full = _norm_name(person.get("fullName"))
                        if full:
                            player_to_team[full.lower()] = tname
                except Exception:
                    continue

            # Build quick lookup for game rows using relaxed matching
            games = []
            for gno, meta in game_meta_by_no.items():
                games.append((gno, meta.get("away_team"), meta.get("home_team"), meta))

            # Iterate hitters
            for row in hitters_rows:
                raw_name = _cell(row, 0)
                player_name = _norm_name(raw_name)
                if not player_name or player_name.lower() in ("hitter name",):
                    continue

                # Find player's team (try roster lookup first, then fallback to all games)
                team_name = player_to_team.get(player_name.lower())
                matched_meta = None

                if team_name:
                    # Find matching game by team presence
                    for (gno, away_t, home_t, meta) in games:
                        if _teams_match_relaxed(team_name, away_t) or _teams_match_relaxed(team_name, home_t):
                            matched_meta = (gno, meta)
                            break
                else:
                    # Fallback: if we can't find the player's team, try to match them to any game
                    # This handles cases where roster lookup fails or player names don't match exactly
                    if games:
                        # For now, assign to the first game as a fallback
                        # In a production system, you might want more sophisticated logic here
                        matched_meta = (games[0][0], games[0][3])
                        print(f"[WARNING] Could not find team for player '{player_name}', assigning to first available game")

                if not matched_meta:
                    continue

                gno, meta = matched_meta
                rfi_meta = rfi_by_no.get(gno, {})

                # Calculate home_team_run_diff if scores are available
                away_score = _num(meta.get("away_score"))
                home_score = _num(meta.get("home_score"))
                home_team_run_diff = None
                if home_score is not None and away_score is not None:
                    home_team_run_diff = home_score - away_score

                projection = {
                    "game_date": today_ca,
                    "away_team": team_to_nickname(meta.get("away_team")),
                    "home_team": team_to_nickname(meta.get("home_team")),
                    "away_sp": _norm_name(meta.get("away_sp")),
                    "home_sp": _norm_name(meta.get("home_sp")),
                    "away_score": away_score,
                    "home_score": home_score,
                    "total_runs_scored": _num(meta.get("total_runs_scored")),
                    "home_team_run_diff": home_team_run_diff,
                    # Determine winning team: prefer sheet value unless it's missing/placeholder
                    "winning_team": (
                        team_to_nickname(meta.get("winning_team"))
                        if (meta.get("winning_team") and "no ml" not in str(meta.get("winning_team")).lower())
                        else (
                            team_to_nickname(meta.get("home_team"))
                            if (home_score is not None and away_score is not None and home_score > away_score)
                            else (
                                team_to_nickname(meta.get("away_team"))
                                if (home_score is not None and away_score is not None and away_score > home_score)
                                else None
                            )
                        )
                    ),
                    "game_xHRs": _num(meta.get("game_xHRs")),
                    "home_win_pct": _num(meta.get("home_win_pct")),
                    "xRFI": _num(rfi_meta.get("xRFI")),
                    "player_name": _norm_name(player_name),
                    "is_pitcher": 0,
                    "AB": _num(_cell(row, 1)),
                    "Hits": _num(_cell(row, 2)),
                    "Singles": _num(_cell(row, 3)),
                    "Doubles": _num(_cell(row, 4)),
                    "Triples": _num(_cell(row, 5)),
                    "Homeruns": _num(_cell(row, 6)),
                    "TB": _num(_cell(row, 7)),
                    "Walks": _num(_cell(row, 8)),
                    "SB": _num(_cell(row, 9)),
                    "Runs": _num(_cell(row, 10)),
                    "RBIs": _num(_cell(row, 11)),
                    "HRR": _num(_cell(row, 12)),
                    "OBP": _num(_cell(row, 13)),
                    "HitterStrikeouts": None,
                    "Pitches": None,
                    "Outs": None,
                    "BF": None,
                    "Strikeouts": None,
                    "PitcherWalks": None,
                    "ER": None,
                    "HA": None,
                    "xRFI_Chance": _num(rfi_meta.get("xRFI_Chance")),
                }
                hitter_projections.append(projection)
        except Exception:
            # Best effort; if roster fetch fails, we still return pitcher rows
            hitter_projections = []

        # Merge hitter projections if any
        if hitter_projections:
            projections.extend(hitter_projections)

        # Reuse the insertion logic from insert_mlb_projections
        results = {
            "total_projections": len(projections),
            "successful_inserts": 0,
            "successful_updates": 0,
            "failed_operations": 0,
        }
        errors = []

        conn = get_connection()
        cursor = conn.cursor()
        try:
            for idx, p in enumerate(projections):
                try:
                    player = p.get("player_name")

                    # Debug logging for problematic data
                    if not player:
                        print(f"[WARNING] Empty player_name in projection {idx}: {p}")
                    if p.get("winning_team") and len(str(p.get("winning_team"))) <= 2:
                        print(f"[WARNING] Suspicious winning_team '{p.get('winning_team')}' for player '{player}'")

                    cursor.execute(
                        "SELECT 1 FROM mlb_projection_sheet WHERE game_date=%s AND away_team=%s AND home_team=%s AND player_name=%s",
                        (p.get("game_date"), p.get("away_team"), p.get("home_team"), player),
                    )
                    exists = cursor.fetchone()

                    fields = (
                        "away_sp,home_sp,away_score,home_score,total_runs_scored,home_team_run_diff,winning_team,"
                        "game_xHRs,home_win_pct,xRFI,is_pitcher,AB,Hits,Singles,Doubles,Triples,Homeruns,TB,Walks,SB,"
                        "Runs,RBIs,HRR,OBP,HitterStrikeouts,Pitches,Outs,BF,Strikeouts,PitcherWalks,ER,HA,xRFI_Chance"
                    ).split(",")

                    values = tuple(p.get(f) for f in fields) + (
                        p.get("game_date"), p.get("away_team"), p.get("home_team"), player
                    )

                    if exists:
                        cursor.execute(
                            f"UPDATE mlb_projection_sheet SET {','.join(f'{f}=%s' for f in fields)} WHERE game_date=%s AND away_team=%s AND home_team=%s AND player_name=%s",
                            values,
                        )
                        results["successful_updates"] += 1
                    else:
                        cursor.execute(
                            f"INSERT INTO mlb_projection_sheet (game_date,away_team,home_team,player_name,{','.join(fields)}) VALUES (%s,%s,%s,%s,{','.join(['%s']*len(fields))})",
                            (p.get("game_date"), p.get("away_team"), p.get("home_team"), player, *[p.get(f) for f in fields]),
                        )
                        results["successful_inserts"] += 1
                except Exception as e:
                    results["failed_operations"] += 1
                    errors.append({"projection_index": idx, "player_name": p.get("player_name"), "error": str(e)})

            try:
                conn.commit()
            except Exception:
                conn.rollback()
        finally:
            try:
                cursor.close()
            except Exception:
                pass
            conn.close()

        return jsonify({
            "success": results["failed_operations"] == 0,
            "message": (
                f"Successfully processed all {results['total_projections']} pitcher projections"
                if results["failed_operations"] == 0
                else f"Processed {results['successful_inserts'] + results['successful_updates']}/{results['total_projections']} pitcher projections"
            ),
            "results": results,
            "errors": errors,
        })

    except Exception as e:
        return jsonify({"success": False, "message": str(e)}), 500


def _convert_dict_to_csv(data_dict):
    """
    Convert a dictionary with numeric keys to CSV format.
    Handles cases where data_dict contains either:
    1. Direct string values for each row (simple case)
    2. Nested dictionaries representing columns (complex case like projections)

    Args:
        data_dict (dict): Dictionary where keys are string numbers ("0", "1", etc.)

    Returns:
        str: CSV formatted string
    """
    if data_dict is None:
        return ""

    # If already a CSV string, return as-is
    if isinstance(data_dict, str):
        return data_dict

    # If it's a list (array) coming from Make.com aggregators
    # We support two variants:
    #  - list[str]
    #  - list[dict] with numeric string keys or natural keys
    if isinstance(data_dict, list):
        if not data_dict:
            return ""
        # list of strings
        if all(isinstance(row, str) or row is None for row in data_dict):
            return "\n".join([str(r) for r in data_dict if r is not None])
        # list of dicts
        if all(isinstance(row, dict) or row is None for row in data_dict):
            # Determine column order by collecting numeric-like keys first; fallback to sorted keys
            col_keys_set = set()
            for row in data_dict:
                if not isinstance(row, dict):
                    continue
                for k in row.keys():
                    col_keys_set.add(str(k))
            # Prefer numeric column keys order ("0","1",...) if present
            numeric_cols = []
            other_cols = []
            for k in col_keys_set:
                try:
                    numeric_cols.append(int(k))
                except Exception:
                    other_cols.append(k)
            numeric_cols.sort()
            ordered_cols = [str(n) for n in numeric_cols] + sorted(other_cols)
            # Build CSV rows
            csv_rows = []
            for row in data_dict:
                if not isinstance(row, dict):
                    continue
                values = []
                for col in ordered_cols:
                    v = row.get(col, "")
                    values.append("" if v is None else str(v))
                csv_rows.append(",".join(values))
            return "\n".join(csv_rows)
        # Fallback: stringify each row
        return "\n".join([str(r) for r in data_dict])

    # Get all numeric keys and sort them
    numeric_keys = []
    for key in data_dict.keys():
        try:
            numeric_keys.append(int(key))
        except (ValueError, TypeError):
            continue

    numeric_keys.sort()

    # Check the structure of the first few items to determine format
    first_row_key = str(numeric_keys[0]) if numeric_keys else None
    if not first_row_key or first_row_key not in data_dict:
        return ""

    first_row_data = data_dict[first_row_key]

    # Case 1: Each row is already a string (simple case)
    if isinstance(first_row_data, str):
        csv_rows = []
        for key_num in numeric_keys:
            key_str = str(key_num)
            if key_str in data_dict:
                row_data = data_dict[key_str]
                if row_data is not None:
                    csv_rows.append(str(row_data))
        return "\n".join(csv_rows)

    # Case 2: Each row is a dictionary with column mappings (complex case)
    elif isinstance(first_row_data, dict):
        # Get all column keys from the first row and sort them
        if not first_row_data:
            return ""

        column_keys = []
        for col_key in first_row_data.keys():
            try:
                column_keys.append(int(col_key))
            except (ValueError, TypeError):
                continue
        column_keys.sort()

        csv_rows = []
        for key_num in numeric_keys:
            key_str = str(key_num)
            if key_str in data_dict and data_dict[key_str] is not None:
                row_dict = data_dict[key_str]
                if isinstance(row_dict, dict):
                    # Build row by extracting values in column order
                    row_values = []
                    for col_num in column_keys:
                        col_str = str(col_num)
                        if col_str in row_dict:
                            value = row_dict[col_str]
                            if value is not None:
                                row_values.append(str(value))
                            else:
                                row_values.append("")
                        else:
                            row_values.append("")

                    # Join with commas for CSV format
                    csv_rows.append(",".join(row_values))

        return "\n".join(csv_rows)

    # Case 3: Fallback - convert whatever we have to string
    else:
        csv_rows = []
        for key_num in numeric_keys:
            key_str = str(key_num)
            if key_str in data_dict:
                row_data = data_dict[key_str]
                if row_data is not None:
                    csv_rows.append(str(row_data))
        return "\n".join(csv_rows)


def calculate_dynamic_confidence_vs_expert(
        event_id: str, expert_confidence_percentage: float) -> float:
    """
    Calculate confidence based on model prediction vs expert's original confidence.

    Steps:
    1. Get model prediction (team_kde_prob) by running main_model
    2. Calculate difference: model_percentage - expert_confidence_percentage
    3. Map difference from [-25%, +25%] scale to [0%, 100%] confidence
    """
    try:
        # Step 1: Get model prediction
        print(f"🔍 Running main_model for event_id: {event_id}")
        try:
            model_result = main_model(event_id)

            if not model_result:
                print(
                    f"❌ Model returned None for {event_id}, using default confidence"
                )
                return 50.0

            if isinstance(model_result, dict) and "error" in model_result:
                print(
                    f"❌ Model returned error for {event_id}: {model_result.get('error', 'Unknown error')}"
                )
                return 50.0

        except Exception as model_error:
            print(f"❌ Model threw exception for {event_id}: {model_error}")
            return 50.0

        # Get team_kde_prob first, fall back to combined_prob if KDE failed
        team_kde_prob = model_result.get("team_kde_prob")
        if team_kde_prob is not None:
            print(f"✅ Using team_kde_prob: {team_kde_prob:.4f}")
            model_percentage = float(team_kde_prob) * 100
        else:
            combined_prob = model_result.get("combined_prob")
            if combined_prob is not None:
                print(
                    f"🔄 Using combined_prob (team_kde_prob unavailable): {combined_prob:.4f}"
                )
                model_percentage = float(combined_prob) * 100
            else:
                print(
                    f"❌ No model probability returned for {event_id}, using default confidence"
                )
                return 50.0
        print(f"📊 Model prediction: {model_percentage:.1f}%")
        print(f"👤 Expert confidence: {expert_confidence_percentage:.1f}%")

        # Step 2: Calculate difference: model - expert
        difference = model_percentage - expert_confidence_percentage
        print(f"📈 Difference (Model - Expert): {difference:.1f}%")

        # Step 3: Map difference to confidence scale
        # Clamp difference to [-25%, +25%] range
        clamped_difference = max(-25, min(25, difference))

        # Map [-25%, +25%] range to [0%, 100%] confidence scale:
        # -25% difference → 0% confidence (expert much better than model)
        # 0% difference → 50% confidence (model matches expert exactly)
        # +25% difference → 100% confidence (model much better than expert)
        # Formula: confidence = ((clamped_difference + 25) / 50) * 100
        confidence = ((clamped_difference + 25) / 50) * 100
        # Guard rails
        confidence = max(0.0, min(100.0, confidence))

        print(
            f"🎯 Calculated confidence: {confidence:.1f}% (clamped difference: {clamped_difference:.1f}% mapped from range [-25%, +25%] to [0%, 100%])"
        )
        return confidence

    except Exception as e:
        print(
            f"❌ Error calculating dynamic confidence vs expert for {event_id}: {e}"
        )
        return 50.0  # Default to 50% if calculation fails


# Note: Removed DDL constraint creation - PlanetScale doesn't allow schema modifications
# Application-level duplicate prevention is handled in the insert logic


def repair_makecom_projections(json_str):
    """
    Repair Make.com specific malformed projections JSON where projections
    is sent as an object instead of an array with multiple player records concatenated.
    Also handles BasicAggregator outputs where multiple objects are concatenated.
    """
    try:
        # Check for Make.com BasicAggregator format: "key": {obj1}{obj2}{obj3}
        # This is the most common issue from Make.com workflows
        for key_name in ['hitter_projections', 'pitcher_projections', 'game_summary', 'rfi']:
            # Look for the pattern: "key": {obj1}{obj2}{obj3}
            pattern = f'("{key_name}"\\s*:\\s*)((?:\\{{[^{{}}]*\\}})+)'
            match = re.search(pattern, json_str, re.DOTALL)
            if match:
                key_part = match.group(1)
                objects_str = match.group(2)

                print(f"[DEBUG] Detected Make.com aggregator format for {key_name}")
                print(f"[DEBUG] Objects string: {objects_str[:100]}...")

                # Add commas between objects: }{  becomes },{
                fixed_objects = re.sub(r'\}\s*\{', '},{', objects_str)

                # Wrap in array brackets
                replacement = f'{key_part}[{fixed_objects}]'
                json_str = json_str.replace(match.group(0), replacement)

                print(f"[DEBUG] Fixed {key_name} section")

        # Check if this is the legacy Make.com malformed format
        if '"projections": {' in json_str and json_str.count(
                '"player_name"') > 1:
            print("[DEBUG] Repairing Make.com malformed projections format")

            # Extract the projections object content
            proj_start = json_str.find('"projections": {')
            if proj_start == -1:
                return json_str

            # Find the end of the projections object
            brace_count = 0
            content_start = proj_start + len('"projections": ')
            i = content_start
            while i < len(json_str):
                if json_str[i] == '{':
                    brace_count += 1
                elif json_str[i] == '}':
                    brace_count -= 1
                    if brace_count == 0:
                        break
                i += 1

            if i >= len(json_str):
                return json_str

            # Extract the malformed projections content
            proj_content = json_str[content_start +
                                    1:i]  # +1 to skip opening brace

            # Split on "game_date" to separate individual player records
            # Each complete record starts with game metadata
            parts = proj_content.split('"game_date"')

            if len(parts) <= 1:
                return json_str

            objects = []
            for j, part in enumerate(parts[1:]):  # Skip first empty part
                # Reconstruct each object
                obj_content = '"game_date"' + part

                # Clean up the object - remove trailing commas and extra content
                # Find the end of this object (before next object starts)
                if j < len(parts) - 2:  # Not the last object
                    # Look for the end pattern that indicates next object
                    next_obj_patterns = [', "game_date"', ',"game_date"']
                    for pattern in next_obj_patterns:
                        if pattern in obj_content:
                            obj_content = obj_content[:obj_content.find(pattern
                                                                        )]
                            break

                # Clean up trailing content
                obj_content = obj_content.strip().rstrip(',')

                # Wrap in braces to make valid JSON object
                objects.append('{' + obj_content + '}')

            if objects:
                # Create proper array format
                projections_array = '[' + ', '.join(objects) + ']'

                # Replace in original string
                before_proj = json_str[:proj_start]
                after_proj = json_str[i + 1:]  # +1 to skip closing brace
                repaired = before_proj + '"projections": ' + projections_array + after_proj

                print(
                    f"[DEBUG] Successfully repaired Make.com format: {len(objects)} objects"
                )
                return repaired

        return json_str
    except Exception as e:
        print(f"[DEBUG] Make.com repair failed: {e}, returning original")
        return json_str


def repair_json(json_str):
    """
    Comprehensive JSON repair function to handle common Make.com and malformed JSON issues
    """
    if not isinstance(json_str, str):
        return json_str

    original_str = json_str

    try:
        # First attempt: try parsing as-is
        json.loads(json_str)
        return json_str
    except json.JSONDecodeError:
        pass

    # Step 1: Remove potential trailing commas before closing brackets/braces
    json_str = re.sub(r',(\s*[}\]])', r'\1', json_str)

    # Step 2: Fix unquoted property names (common Make.com issue)
    # Pattern: find unquoted keys followed by colon
    json_str = re.sub(r'(\s*[{,]\s*)([a-zA-Z_][a-zA-Z0-9_]*)\s*:', r'\1"\2":', json_str)

    # Step 3: Fix single quotes to double quotes
    json_str = re.sub(r"'([^']*)'", r'"\1"', json_str)

    # Step 4: Handle unquoted string values
    json_str = re.sub(r':\s*([a-zA-Z_][a-zA-Z0-9_]*)\s*([,}\]])', r': "\1"\2', json_str)

    # Step 5: Handle scientific notation or numbers that might be malformed
    json_str = re.sub(r':\s*([0-9]+\.?[0-9]*[eE][+-]?[0-9]+)\s*', r': \1 ', json_str)

    # Step 6: Handle aggregated data from Make.com (multiple objects without array wrapper)
    # Look for patterns like: "key": {obj1}{obj2}{obj3} or "key": {obj1},{obj2},{obj3}
    # This handles Make.com BasicAggregator output
    for key in ['hitter_projections', 'pitcher_projections', 'game_summary', 'rfi', 'projections']:
        if f'"{key}"' in json_str:
            # Match the key followed by object(s)
            pattern = rf'("{key}"\s*:\s*)(\{{[^{{}}]*\}}(?:\s*,?\s*\{{[^{{}}]*\}})*)'
            match = re.search(pattern, json_str, re.DOTALL)
            if match:
                key_part = match.group(1)
                objects_part = match.group(2)

                # Add missing commas between objects if needed
                objects_part = re.sub(r'\}\s*\{', '},{', objects_part)

                # Wrap in array brackets
                fixed_section = f'{key_part}[{objects_part}]'
                json_str = re.sub(pattern, fixed_section, json_str, flags=re.DOTALL)

    # Step 7: Handle array-like structures that aren't properly wrapped
    # Look for patterns like: "key": {obj1}, {obj2} and fix to "key": [{obj1}, {obj2}]
    array_pattern = r'("[\w_]+"\s*:\s*)(\{[^}]*\}(?:\s*,\s*\{[^}]*\})+)'
    def fix_array_structure(match):
        key_part = match.group(1)
        objects_part = match.group(2)
        return f'{key_part}[{objects_part}]'

    json_str = re.sub(array_pattern, fix_array_structure, json_str, flags=re.DOTALL)

    # Step 7.5: Handle missing commas between objects (Make.com aggregator issue)
    # Pattern: }{  should be },{
    json_str = re.sub(r'\}\s*\{', '},{', json_str)

    # Step 7.6: Fix unescaped inner quotes inside word tokens (e.g., Ke"Bryan -> Ke'Bryan)
    # This commonly occurs with names that include apostrophes but arrive as raw double-quotes
    try:
        json_str = re.sub(r'(?<=\w)"(?=\w)', "'", json_str)
    except Exception:
        # Fallback for environments that might not support lookbehind
        json_str = re.sub(r'([A-Za-z0-9])"([A-Za-z0-9])', r"\1'\2", json_str)

    # Step 8: Final cleanup - remove any double commas
    json_str = re.sub(r',,+', ',', json_str)

    # Step 9: Try to parse again
    try:
        json.loads(json_str)
        return json_str
    except json.JSONDecodeError as e:
        print(f"[WARNING] JSON repair failed: {e}")
        print(f"[DEBUG] Original: {original_str[:200]}...")
        print(f"[DEBUG] Repaired: {json_str[:200]}...")

        # Try to identify the problematic section
        if hasattr(e, 'pos') and e.pos:
            error_pos = e.pos
            start = max(0, error_pos - 100)
            end = min(len(json_str), error_pos + 100)
            problematic_section = json_str[start:end]
            print(f"[DEBUG] Error around position {error_pos}: ...{problematic_section}...")

        return original_str  # Return original if repair didn't work


if __name__ == "__main__":
    # Ensure app is created when running as a script (redundant-safe)
    app = create_app()

    # Optionally start scheduler via env flag
    try:
        from fullstack.backend.background.scheduler import start_scheduler
        if os.getenv("START_SCHEDULER") == "1":
            start_scheduler(app)
    except Exception:
        pass

    # Start background healing jobs according to schedule
    def _start_schedulers():
        try:
            if BackgroundScheduler is None:
                return
            sched = BackgroundScheduler(timezone="America/Los_Angeles")

            def heal_unknown_for_dates(dates: list[str]):
                try:
                    with get_connection() as conn:
                        cur = conn.cursor()
                        # Fetch candidates for provided dates
                        cur.execute(
                            f"""
                            SELECT event_id, league, team_a, team_b, event_date, pick_type
                            FROM events
                            WHERE event_date IN ({','.join(['%s']*len(dates))})
                            """,
                            tuple(dates),
                        )
                        rows = cur.fetchall() or []
                        for (ev_id, league, ta, tb, ev_date, pk) in rows:
                            if (pk or "").strip() in ("Prop", "Total"):
                                continue
                            if _is_unknown_team(ta) or _is_unknown_team(tb):
                                na, nb = fill_missing_teams(league or "", ev_date, ta, tb)
                                if (na and na != ta) or (nb and nb != tb):
                                    try:
                                        cur.execute(
                                            "UPDATE events SET team_a=%s, team_b=%s WHERE event_id=%s",
                                            (na, nb, ev_id),
                                        )
                                        corrected_id = generate_admin_event_id(
                                            ev_date,
                                            league or (ev_id or "").split("-")[3],
                                            pk or "MoneyLine",
                                            na,
                                            nb,
                                            None,
                                            None,
                                            None,
                                        )
                                        if corrected_id != ev_id:
                                            _migrate_event_and_predictions(ev_id, corrected_id, na, nb)
                                    except Exception:
                                        pass
                        try:
                            conn.commit()
                        except Exception:
                            pass
                except Exception:
                    pass

            # Every 30 minutes from 09:00 to 12:00 PT (i.e., 9,10,11,12)
            def job_heal_today():
                today = get_current_california_date()
                heal_unknown_for_dates([today])

            # Nightly sweep for last 3 days at 00:30 PT
            def job_heal_recent():
                from datetime import timedelta
                base = datetime.now(ZoneInfo("America/Los_Angeles"))
                dates = [(base - timedelta(days=i)).strftime("%Y-%m-%d") for i in range(0, 3)]
                heal_unknown_for_dates(dates)

            try:
                sched.add_job(job_heal_today, CronTrigger(day_of_week="mon-sun", hour="9-12", minute="*/30"))
                sched.add_job(job_heal_recent, CronTrigger(day_of_week="mon-sun", hour=0, minute=30))
                sched.start()
            except Exception:
                pass
        except Exception:
            pass

    _start_schedulers()
    app.run(host='0.0.0.0', port=5000, debug=True)
