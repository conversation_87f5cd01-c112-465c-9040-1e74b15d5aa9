# Project Parlay AI Assistant Configuration Guide

## 🚀 Quick Start

This project is optimized for AI-powered development with <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>surf. Each assistant has been configured with deep context about our architecture, coding standards, and workflows.

### Instant Context Loading

**Claude Code**: Run `/prime` command or `bash .claude/hooks/prime.sh`
**Cursor**: Rules auto-load from `.cursorrules` 
**Windsurf**: Rules auto-load from `.windsurf/rules.md`

## 📁 Configuration Structure

```
Project-Parlay/
├── .claude/                 # Claude Code configuration
│   ├── agents/             # Specialized AI agents
│   ├── commands/           # Custom commands
│   ├── config/             # Settings and policies
│   ├── docs/               # Project documentation
│   ├── hooks/              # Automation scripts
│   ├── patterns/           # Code patterns
│   └── settings.local.json # Local settings
├── .windsurf/              # Windsurf configuration
│   ├── rules.md           # Project rules
│   ├── memories/          # Persistent context
│   └── workflows/         # Automated workflows
├── .cursor/                # Cursor configuration
│   └── rules.md           # Project rules
└── CLAUDE.md              # This file
```

## 🎯 Project Context

### Architecture Overview
- **Frontend**: React + TypeScript + Vite + TailwindCSS (`fullstack/frontend/`)
- **Backend**: Python Flask + FastAPI hybrid (`fullstack/backend/`)
- **Data Science**: MySQL + Analytics modules (`fullstack/backend/data_science_modules/`)
- **Automation**: Discord scrapers + Make.com workflows (`data_collection_automation/`)

### Key API Endpoints
- `/api/external/mlb_projections` - MLB projection data (upsert)
- `/api/external/insert_picks` - Sports picks with confidence mapping
- Various internal APIs for events, predictions, and analytics

### Database
- **Provider**: PlanetScale MySQL
- **Connection**: `planet_scale_port.py`
- **Key Tables**: `mlb_projection_sheet`, `events`, `expert_predictions`, `crowd_predictions`

## 🔧 Development Standards

### Code Style
- **Indentation**: 4 spaces (never tabs)
- **Naming**: `snake_case` for functions/variables, `PascalCase` for classes
- **Imports**: Always at file top, organized by standard → third-party → local
- **Comments**: Explain "why" not "what", include docstrings

### Best Practices
1. **Minimal Solutions**: No unnecessary logging, print statements, or fluff code
2. **File Management**: Edit existing files, avoid creating new ones unless essential
3. **Security**: No hardcoded secrets, validate all inputs, use environment variables
4. **Testing**: Run `npm run lint` (frontend) and `pytest` (backend) before commits

### Git Workflow
- **Branch naming**: `feature-description-yourname`
- **PR titles**: Clear and concise
- **Commits**: Meaningful messages
- **Review**: Tag @hartel or team lead

## 🤖 AI Assistant Commands

### Claude Code
```bash
/prime                    # Load full project context
/prime [specific_focus]   # Load context with focus area
```

### Cursor
- Rules auto-load from `.cursorrules`
- Use Cmd+K for inline AI assistance
- Cmd+Shift+K for context-aware prompts

### Windsurf (Cascade)
- Rules auto-load from `.windsurf/rules.md`
- `/deploy` - Deploy web application
- `/test` - Run test suite
- `/review` - Code review workflow
- Use @-mentions for context (@file, @folder, @web)

## 🛠️ Common Tasks

### Start Development
```bash
# Frontend
cd fullstack/frontend && npm run dev

# Backend
cd fullstack/backend && python app.py

# Handicapper Manager
cd handicapper_manager_webapp
npm run dev:frontend
python handicapper_manager_backend/app.py
```

### Database Operations
```python
# Use inline commands, not new files
python -c "from fullstack.backend.modules.planet_scale_port import get_connection; ..."
```

### Deployment
```bash
# Frontend build
npm run build

# Deploy via Windsurf
# Cascade: /deploy
```

## 💡 AI Assistant Tips

### For Maximum Effectiveness
1. **Start with context**: Always run `/prime` (Claude) or ensure rules are loaded
2. **Be specific**: Reference exact files and functions
3. **Use memories**: Let AI assistants create memories for repeated patterns
4. **Leverage workflows**: Use predefined workflows for common tasks
5. **Batch operations**: Use parallel tool calls for independent tasks

### Model Selection
- **Complex Architecture**: Claude 3.5 Sonnet/Opus
- **Quick Edits**: GPT-4o or Windsurf Base Model
- **Code Generation**: Windsurf Premier or Claude Sonnet
- **Refactoring**: Claude Opus for system-wide changes

## 🔐 Security & Compliance

### API Keys
- Store in `.env` files (never commit)
- Use environment variables in code
- Rotate keys regularly

### Data Protection
- No PII in logs or debug output
- Sanitize all user inputs
- Follow principle of least privilege

## 📚 Resources

### Documentation
- [Project README](README.md)
- [Style Guide](Project_Parlay_Style_Guide.md)
- [API Documentation](.claude/docs/project-overview.md)

### External
- [Windsurf Docs](https://docs.windsurf.com)
- [Cursor Docs](https://docs.cursor.com)
- [Claude Code Guide](https://claude.ai/docs)

## 🚨 Important Notes

1. **Never create unnecessary files** - Use inline commands for one-off tasks
2. **Proactive fixes** - AI should fix issues without asking permission
3. **Clean code only** - No debug prints or unnecessary logging
4. **Follow patterns** - Maintain consistency with existing code

