# fullstack/backend/routes/admin.py
from flask import Blueprint, request, jsonify
from fullstack.backend.services.odds_api import get_event_start_from_odds_api
from data_science_modules.planet_scale_port import get_connection, get_dict_connection

admin_bp = Blueprint('admin', __name__)


@admin_bp.route("/admin/backfill_prediction_times", methods=["POST"])
def backfill_prediction_times():
    """Admin endpoint to backfill prediction times using Odds API."""
    data = request.get_json() or {}
    admin_password = data.get("admin_password")
    if admin_password != "ppadmin42":
        return jsonify({"success": False, "message": "Unauthorized"}), 401

    try:
        from data_science_modules.planet_scale_port import get_connection
    except Exception as e:
        return jsonify({"success": False, "message": f"DB unavailable: {e}"}), 500

    event_ids = data.get("event_ids") or []
    target_date = data.get("date")
    target_league = data.get("league")

    updated = 0
    errors: list[str] = []

    try:
        with get_connection() as conn:
            cur = conn.cursor()
            rows = []
            if event_ids:
                placeholders = ",".join(["%s"] * len(event_ids))
                cur.execute(
                    f"SELECT e.event_id, e.league, e.team_a, e.team_b, e.event_date FROM events e WHERE e.event_id IN ({placeholders})",
                    tuple(event_ids),
                )
                rows = cur.fetchall() or []
            else:
                where = []
                params: list = []
                if target_date:
                    where.append("e.event_date = %s")
                    params.append(target_date)
                if target_league:
                    where.append("e.league = %s")
                    params.append(target_league)
                where_sql = (" WHERE " + " AND ".join(where)) if where else ""
                cur.execute(
                    f"SELECT e.event_id, e.league, e.team_a, e.team_b, e.event_date FROM events e{where_sql}",
                    tuple(params),
                )
                rows = cur.fetchall() or []

            for (ev_id, league, team_a, team_b, ev_date) in rows:
                agd, apt = get_event_start_from_odds_api(league, team_a, team_b, (ev_date or target_date))
                if not agd and not apt:
                    agd2, apt2 = get_event_start_from_odds_api(league, team_a, team_b, (target_date or ev_date))
                    agd = agd2 or agd
                    apt = apt2 or apt
                try:
                    if agd:
                        cur.execute(
                            "UPDATE events SET event_date=%s WHERE event_id=%s",
                            (agd, ev_id),
                        )
                    if apt:
                        cur.execute(
                            "UPDATE expert_predictions SET prediction_time=%s WHERE event_id=%s",
                            (apt, ev_id),
                        )
                    if cur.rowcount >= 0:
                        updated += 1
                except Exception as ue:
                    errors.append(f"{ev_id}: {ue}")

            conn.commit()

    except Exception as e:
        return jsonify({"success": False, "message": str(e)}), 500

    return jsonify({"success": True, "updated": updated, "errors": errors})
