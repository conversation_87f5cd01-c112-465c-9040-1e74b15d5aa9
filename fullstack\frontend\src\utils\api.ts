import { ManualPick } from "../types/manualPick";

export interface EventData {
  event_id: string;
  event_date: string;
  player_name: string;
  event_team: string;
  handicappers: string[];
  stat_threshold?: number;
  pick_type?: string;
  team_a?: string;
  team_b?: string;
  stat_type?: string;
  predictions?: number[];
  confidence_values?: number[];
  stat_thresholds?: number[];
  prediction_time?: string;
  // Map of expert_name -> prediction (1 for Over/Higher, 0 for Under/Lower)
  expert_predictions_map?: Record<string, number>;
}

export interface TodaysEventsResponse {
  success: boolean;
  message: string;
  events: EventData[];
  date: string;
}

export const fetchTodaysEvents = async (
  customDate?: string,
  useBackendConfiguredDate: boolean = false
): Promise<TodaysEventsResponse> => {
  try {
    // If useBackendConfiguredDate is true, don't pass any date parameter
    // This allows the backend to use get_default_date() (admin-configured date)
    const url = useBackendConfiguredDate
      ? "/api/todays_events"
      : customDate
      ? `/api/todays_events?date=${customDate}`
      : "/api/todays_events";

    console.log("🌐 API Request URL:", url);
    console.log(
      "📅 Custom date parameter:",
      useBackendConfiguredDate
        ? "none (using backend configured date)"
        : customDate || "none (using current date)"
    );

    const response = await fetch(url);
    console.log(
      "📡 HTTP Response status:",
      response.status,
      response.statusText
    );

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    console.log("🔄 Raw API Response:", JSON.stringify(data, null, 2));

    return data;
  } catch (error) {
    console.error("💥 Error fetching today's events:", error);
    throw error;
  }
};

export interface FavoritesResponse {
  success: boolean;
  message: string;
  favorites: number[];
}

export const fetchFavorites = async (): Promise<FavoritesResponse> => {
  try {
    const response = await fetch("/api/favorites");

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("💥 Error fetching favorites:", error);
    throw error;
  }
};

export const saveFavorites = async (
  favorites: number[]
): Promise<FavoritesResponse> => {
  try {
    const response = await fetch("/api/favorites", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ favorites }),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("💥 Error saving favorites:", error);
    throw error;
  }
};

export interface HandicapperProfileResponse {
  success: boolean;
  message: string;
  handicapper: {
    id: number;
    name: string;
    accuracy: string;
    sports: string;
    picks: Record<string, unknown>[];
  };
}

export const fetchHandicapperProfile = async (
  id: number
): Promise<HandicapperProfileResponse> => {
  try {
    const response = await fetch(`/api/handicappers/${id}`);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    const data = await response.json();
    return data;
  } catch (error) {
    console.error("💥 Error fetching handicapper profile:", error);
    throw error;
  }
};

export interface PickObject {
  id: number;
  name: string;
  odds: number;
  confidence: number;
  league: string;
  event_id: string;
  [key: string]: unknown;
}

export interface ManualPickResponse {
  success: boolean;
  response: string;
  objects?: PickObject[];
}

export const submitManualPick = async (
  pickData: ManualPick
): Promise<ManualPickResponse> => {
  try {
    const response = await fetch("/api/process", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(pickData),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("💥 Error submitting manual pick:", error);
    throw error;
  }
};

export interface ExpertsResponse {
  success: boolean;
  experts: string[];
  message?: string;
}

export const fetchExperts = async (): Promise<ExpertsResponse> => {
  try {
    const response = await fetch("/api/experts");

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("💥 Error fetching experts:", error);
    throw error;
  }
};

export interface InsertEventResponse {
  success: boolean;
  message?: string;
  event_id?: string;
  events_inserted?: number;
  expert_predictions_inserted?: number;
}

export const insertEvent = async (
  eventData: ManualPick
): Promise<InsertEventResponse> => {
  try {
    const response = await fetch("/api/add_event", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(eventData),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("💥 Error inserting event:", error);
    throw error;
  }
};
