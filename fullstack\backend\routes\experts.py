# fullstack/backend/routes/experts.py
from flask import Blueprint
from flask import jsonify, request
from datetime import datetime
from data_science_modules.planet_scale_port import get_connection

experts_bp = Blueprint('experts', __name__)


@experts_bp.route('/experts', methods=['GET'])
def get_all_experts():
    try:
        with get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT DISTINCT expert_name
                FROM expert_predictions
                ORDER BY expert_name ASC
            """)
            rows = cursor.fetchall() or []
        experts = [r[0] for r in rows]
        return jsonify({"success": True, "experts": experts})
    except Exception as e:
        return jsonify({"success": False, "message": str(e)}), 500


@experts_bp.route('/experts_by_date', methods=['GET'])
def experts_by_date():
    target_date = request.args.get("date") or datetime.today().strftime("%Y-%m-%d")
    try:
        with get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT DISTINCT expert_name
                FROM expert_predictions
                WHERE DATE(prediction_time) = %s
                ORDER BY expert_name ASC
            """, (target_date,))
            rows = cursor.fetchall() or []
        experts = [r[0] for r in rows]
        return jsonify({"success": True, "experts": experts, "date": target_date})
    except Exception as e:
        return jsonify({"success": False, "message": str(e)}), 500


@experts_bp.route('/handicappers', methods=['GET'])
def get_handicappers():
    """
    Placeholder endpoint to prevent 404 errors.
    Frontend handles handicapper data through local caching.
    """
    return jsonify({
        "success": True,
        "message": "Handicappers endpoint placeholder",
        "handicappers": []
    })


@experts_bp.route('/handicappers/<int:handicapper_id>', methods=['GET'])
def get_handicapper_profile(handicapper_id):
    """
    Placeholder endpoint to prevent 404 errors.
    Frontend handles handicapper profiles through local caching.
    """
    return jsonify({
        "success": True,
        "message": "Handicapper profile endpoint placeholder",
        "handicapper": {
            "id": handicapper_id,
            "name": f"Handicapper {handicapper_id}",
            "accuracy": "N/A",
            "sports": "Multiple Sports",
            "picks": []
        }
    })


@experts_bp.route('/favorites', methods=['GET'])
def get_favorites():
    """
    Placeholder endpoint to prevent 404 errors.
    Frontend handles favorites through local storage.
    """
    return jsonify({
        "success": True,
        "message": "Favorites endpoint placeholder",
        "favorites": []
    })


@experts_bp.route('/favorites', methods=['POST'])
def save_favorites():
    """
    Placeholder endpoint to prevent 404 errors.
    Frontend handles favorites through local storage.
    """
    return jsonify({
        "success": True,
        "message": "Favorites saved (placeholder)",
        "favorites": []
    })

