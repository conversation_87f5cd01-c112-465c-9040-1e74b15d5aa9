import discord
import requests
import asyncio
from datetime import datetime
import argparse
from io import BytesIO
from PIL import Image
from typing import List, Optional
import os
from google.cloud import vision
from google.api_core.client_options import ClientOptions

parser = argparse.ArgumentParser(description="Coles Algo Historical Discord Image Scraper")
parser.add_argument('--max', '-m', type=int, default=5, help="Max number of messages per channel")
parser.add_argument('--all', '-a', action='store_true', help="Scrape the entire channel history")
parser.add_argument('--verbose', '-v', action='store_true', help="Enable verbose logging")
args = parser.parse_args()
MAX_MESSAGES = args.max
SCRAPE_ALL = args.all
VERBOSE = args.verbose

TOKEN = "MTM5NzI4ODg3NzM5OTYwNTI1OQ.GbjChY.WQAyO6uGlmctv_m5g8YH8c7KXoI1ZCRs07J6pU"
WEBHOOK_URL = "https://hook.us2.make.com/d7a6ulpg26gw4zbwsmykqx44v1yahilp"


# TARGET_CHANNEL_IDS = [
#     "1358106129611100309",  # 🚨official-picks🚨
#     "1358106091761700956",  # 🏀nba-sheets🏀
#     "1358105984224198818",  # ⚾mlb-sheets⚾
#     "1364336675546726520",  # 🏒nhl-sheets🏒
#     "1380264720082276523",  # 💬premium-chat💬
#     "1358105866204872906",  # winnings-screenshots
#     "1358106853485314299",  # testimonial
#     "1358106364139802714",  # general
#     "1358118720278368598",  # community_slips-and-live_bets
#     "1358106251065819367",  # mlb-chat
#     "1358106269814358276",  # nba-chat
#     "1358106330929561610",  # nhl-chat
#     "1358110319687241840",  # random-chat
#     "1359355196395880544",  # bad-beats
#     "1365127288877875282",  # nfl-chat
#     "1361160617029013627",  # soccer-chat
#     "1358106306401013887",  # college-chat
#     "1380045834376450099",  # tennis-chat
#     "1358868108772638781",  # props-cash-request
#     "1359618861439123566",  # gambly-bot-requests
#     "1390510994501009429"   # 🔒community-pod🔒
# ]


TARGET_CHANNEL_IDS = [
    "1358105984224198818",  # ⚾mlb-sheets⚾
]

intents = discord.Intents.default()
intents.message_content = True
intents.messages = True
client = discord.Client(intents=intents)

def log(message: str, *, verbose_only: bool = False) -> None:
    if verbose_only and not VERBOSE:
        return
    print(f"[{datetime.now().isoformat(sep=' ', timespec='seconds')}] {message}", flush=True)

@client.event
async def on_ready():
    log(f"Logged in as {client.user} - Ready for historical scraping")
    await fetch_historical_messages(None if SCRAPE_ALL else MAX_MESSAGES, SCRAPE_ALL)
    await client.close()

async def fetch_historical_messages(limit: int | None = 1, scrape_all: bool = False):
    total_images = 0
    log(f"Starting fetch scrape_all={scrape_all} limit={limit}")

    vision_client: Optional[vision.ImageAnnotatorClient] = None
    try:
        api_key = os.getenv("GOOGLE_CLOUD_VISION_API_KEY")
        if api_key:
            client_options = ClientOptions(api_key=api_key)
            vision_client = vision.ImageAnnotatorClient(client_options=client_options)
            log("Initialized Google Cloud Vision client with API key")
        else:
            vision_client = vision.ImageAnnotatorClient()
            log("Initialized Google Cloud Vision client with default credentials", verbose_only=True)
    except Exception as ex:
        log(f"Failed to initialize Google Cloud Vision: {ex}")
        vision_client = None

    def ocr_extract_text_if_contains(url: str, phrase: str) -> Optional[str]:
        try:
            if vision_client is None:
                return None
            r = requests.get(url, timeout=12)
            r.raise_for_status()
            img_bytes = r.content
            gcv_image = vision.Image(content=img_bytes)
            response = vision_client.text_detection(image=gcv_image, timeout=20)
            if response.error and response.error.message:
                log(f"Vision API error: {response.error.message}")
                return None
            annotations = response.text_annotations or []
            if not annotations:
                return None
            full_text = annotations[0].description or ""
            return full_text if phrase.lower() in full_text.lower() else None
        except Exception as ex:
            log(f"OCR/download failed: {ex}")
            return None

    for channel_id in TARGET_CHANNEL_IDS:
        try:
            log(f"Resolving channel {channel_id}")
            channel = client.get_channel(int(channel_id))
            if not channel:
                log("Channel not cached, fetching from API", verbose_only=True)
                channel = await client.fetch_channel(int(channel_id))
                if not channel:
                    continue

            if scrape_all:
                log(f"Fetching entire history from #{channel.name}")
            else:
                log(f"Fetching {limit} messages from #{channel.name}")

            async for message in channel.history(limit=limit, oldest_first=False):
                log(f"Message id={message.id} ts={message.created_at.isoformat()} author={message.author}", verbose_only=True)
                if message.author.bot:
                    log("Skipping bot message", verbose_only=True)
                    continue
                if message.attachments:
                    sem = asyncio.Semaphore(5)
                    async def check_attachment(attachment):
                        if not (attachment.content_type and attachment.content_type.startswith('image/')):
                            return None
                        async with sem:
                            log(f"OCR check {attachment.filename}", verbose_only=True)
                            try:
                                extracted_text = await asyncio.wait_for(
                                    asyncio.to_thread(ocr_extract_text_if_contains, attachment.url, "Total Runs Scored:"),
                                    timeout=20
                                )
                            except Exception as ex:
                                log(f"OCR failed: {ex}", verbose_only=True)
                                extracted_text = None
                            if extracted_text:
                                log(f"MATCH FOUND: 'Total Runs Scored:' in {attachment.filename} (message {message.id})")
                                return {
                                    "channel": message.channel.name,
                                    "content": message.content,
                                    "timestamp": message.created_at.isoformat(),
                                    "image_url": attachment.url,
                                    "image_filename": attachment.filename,
                                    "ocr_text": extracted_text,
                                    "is_historical": True,
                                    "message_id": str(message.id),
                                    "author": str(message.author)
                                }
                            else:
                                log("OCR did not match; skipping image", verbose_only=True)
                                return None
                    tasks = [check_attachment(att) for att in message.attachments]
                    results = await asyncio.gather(*tasks)
                    per_message_images = [r for r in results if r]
                    if per_message_images:
                        log(f"Sending batch with {len(per_message_images)} image(s) for message {message.id}")
                        await send_batch_to_webhook(per_message_images)
                        total_images += len(per_message_images)
                else:
                    log("Message has no attachments", verbose_only=True)

        except Exception as e:
            log(f"Error processing channel {channel_id}: {e}")

    if total_images == 0:
        log("No images found to send")
    else:
        log(f"Done. Sent {total_images} images in batches.")

async def send_batch_to_webhook(images):
    data = {
        "batch_type": "historical_images",
        "total_images": len(images),
        "images": images,
        "scrape_timestamp": datetime.now().isoformat()
    }
    
    try:
        response = requests.post(WEBHOOK_URL, json=data, timeout=30)
        response.raise_for_status()
        log(f"Successfully sent {len(images)} images in batch")
    except Exception as e:
        log(f"Failed to send batch: {e}")

if __name__ == "__main__":
    log("Coles Algo Historical Discord Image Scraper")
    log("This script will fetch historical messages and forward images to your webhook")
    log(f"Target channels: {TARGET_CHANNEL_IDS}")
    
    client.run(TOKEN)
