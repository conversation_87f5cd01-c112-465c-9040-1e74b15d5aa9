# fullstack/backend/config.py
import os
from typing import Dict


def get_config() -> Dict[str, str]:
    """
    Return runtime configuration values read from environment.
    Keep this function lightweight and safe to import at module load.
    """
    return {
        "ENV": os.environ.get("ENV", "development"),
        "DATABASE_URL": os.environ.get("DATABASE_URL", ""),
        "MODEL_PATH": os.environ.get("MODEL_PATH", ""),
    }


def load_env(path: str | None = None) -> None:
    """
    Explicitly load a .env file if caller wants to; do not call on import.
    """
    try:
        from dotenv import load_dotenv as _load_dotenv
    except Exception:
        return
    if path:
        _load_dotenv(path)
    else:
        _load_dotenv()