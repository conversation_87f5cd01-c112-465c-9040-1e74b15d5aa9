# fullstack/backend/routes/events.py
from flask import Blueprint
from flask import jsonify, request
from fullstack.backend.services.date_utils import get_current_california_date
from data_science_modules.planet_scale_port import get_todays_events_with_handicappers
from fullstack.backend.services.settings_service import get_default_date

events_bp = Blueprint('events', __name__)


@events_bp.route('/todays_events', methods=['GET'])
def get_todays_events():
    try:
        custom_date = request.args.get('date')
        if custom_date:
            from datetime import datetime
            try:
                datetime.strptime(custom_date, "%Y-%m-%d")
                target_date = custom_date
            except ValueError:
                return jsonify({"success": False, "message": "Invalid date format. Please use YYYY-MM-DD format.", "events": []}), 400
        else:
            target_date = get_default_date()
        events = get_todays_events_with_handicappers(target_date)
        events_data = [e.to_dict() for e in events]
        return jsonify({"success": True, "message": f"Retrieved {len(events_data)} events for {target_date}", "events": events_data, "date": target_date})
    except Exception as e:
        return jsonify({"success": False, "message": f"Error retrieving events: {str(e)}", "events": []}), 500

