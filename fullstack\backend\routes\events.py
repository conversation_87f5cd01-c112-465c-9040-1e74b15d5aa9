# fullstack/backend/routes/events.py
from flask import Blueprint, jsonify, request
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from services.date_utils import get_current_california_date
from data_science_modules.planet_scale_port import get_todays_events_with_handicappers
from services.settings_service import get_default_date

events_bp = Blueprint('events', __name__)


@events_bp.route('/todays_events', methods=['GET'])
def get_todays_events():
    try:
        custom_date = request.args.get('date')
        if custom_date:
            from datetime import datetime
            try:
                datetime.strptime(custom_date, "%Y-%m-%d")
                target_date = custom_date
            except ValueError:
                return jsonify({"success": False, "message": "Invalid date format. Please use YYYY-MM-DD format.", "events": []}), 400
        else:
            target_date = get_default_date()
        events = get_todays_events_with_handicappers(target_date)
        events_data = [e.to_dict() for e in events]
        return jsonify({"success": True, "message": f"Retrieved {len(events_data)} events for {target_date}", "events": events_data, "date": target_date})
    except Exception as e:
        return jsonify({"success": False, "message": f"Error retrieving events: {str(e)}", "events": []}), 500


@events_bp.route('/add_event', methods=['POST'])
def add_event():
    """Insert a single event (or one per league) into the events table. Minimal logic, password-protected."""
    from services.events_service import fill_missing_teams, _is_unknown_team, _format_nickname
    from services.odds_api import get_event_start_from_odds_api
    from data_science_modules.planet_scale_port import generate_admin_event_id, normalize_stat_type_for_storage
    from datetime import datetime
    import re

    data = request.get_json()
    print("[DEBUG] /api/add_event payload:", data)
    print(f"[DEBUG] crowd_probability received: {data.get('crowd_probability')}")
    source_name = data.get("source")
    print(f"[DEBUG] source_name received: {source_name}")

    if data.get("admin_password") != "ppadmin42":
        return jsonify({"success": False, "message": "Unauthorized"}), 401

    name = data.get("name", "").strip()
    leagues = data.get("league", [])
    event_date = data.get("eventDate") or data.get("event_date") or datetime.today().strftime("%Y-%m-%d")
    pick_type = data.get("pick_type", "MoneyLine")
    player_team = data.get("player_team", "None")
    stat_type_raw = data.get("stat_type", "MoneyLine")
    # Normalize stat type for consistent database storage
    stat_type = normalize_stat_type_for_storage(stat_type_raw)
    player_name = data.get("player_name")
    stat_threshold = data.get("stat_threshold")
    team_a = data.get("team_a", name)  # Default to name if not provided
    team_b = data.get("team_b", "Other")  # Default to "Other" if not provided
    pick_origin = data.get('pick_origin', [])

    if not name or not leagues:
        return jsonify({
            "success": False,
            "message": "Missing required fields"
        }), 400

    # Import PlanetScale connection function
    try:
        from data_science_modules.planet_scale_port import get_connection
    except ImportError:
        return jsonify({
            "success": False,
            "message": "Database connection module not available"
        }), 500

    inserted = 0

    try:
        with get_connection() as conn:
            cursor = conn.cursor()
            event_id = None  # Ensure event_id is defined outside the loop
            expert_predictions_inserted = 0

            # Test database connection
            cursor.execute("SELECT 1")
            test_result = cursor.fetchone()
            print(f"[INFO] Database connection test: {test_result}")

            # Check if tables exist
            cursor.execute("SHOW TABLES LIKE 'events'")
            events_table = cursor.fetchone()
            cursor.execute("SHOW TABLES LIKE 'expert_predictions'")
            predictions_table = cursor.fetchone()
            print(f"[INFO] Tables exist - events: {bool(events_table)}, expert_predictions: {bool(predictions_table)}")

            print(f"[INFO] Processing {len(leagues)} leagues: {leagues}")
            print(f"[INFO] Expert predictions to insert: {len(pick_origin) if pick_origin else 0}")

            for league in leagues:
                # Use original stat_type for event_id generation (for consistency)
                # but store normalized version in database
                # Fill missing teams for MoneyLine/Spread when Unknown/null provided
                if pick_type in ["MoneyLine", "Spread"]:
                    prev_a, prev_b = team_a, team_b
                    team_a, team_b = fill_missing_teams(league, event_date, team_a, team_b)
                    # Last-resort normalization to avoid Unknown/null leaking into ID
                    if _is_unknown_team(team_a) and not _is_unknown_team(prev_a):
                        team_a = _format_nickname(prev_a)
                    if _is_unknown_team(team_b) and not _is_unknown_team(prev_b):
                        team_b = _format_nickname(prev_b)

                event_id = generate_admin_event_id(event_date, league, pick_type, team_a, team_b, player_name, stat_threshold, stat_type_raw)
                print(f"[INFO] Generated event_id for {league}: {event_id}")
                print(f"[INFO] Event data: date={event_date}, teams={team_a} vs {team_b}, type={pick_type}")

                # Resolve accurate start times via Odds API per league
                api_game_date, api_prediction_time = get_event_start_from_odds_api(league, team_a, team_b, event_date)
                event_date_for_insert = api_game_date or event_date

                try:
                    insert_query = """INSERT INTO events (
                            event_id, event_date, league,
                            team_a, team_b, pick_type,
                            player_team, stat_type, player_name, stat_threshold)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)"""
                    insert_values = (event_id, event_date_for_insert, league, team_a, team_b, pick_type, player_team, stat_type, player_name, stat_threshold)
                    print(f"[INFO] Executing SQL: {insert_query}")
                    print(f"[INFO] With values: {insert_values}")

                    cursor.execute(insert_query, insert_values)
                    rows_affected = cursor.rowcount
                    print(f"[INFO] Rows affected by INSERT: {rows_affected}")

                    if rows_affected > 0:
                        inserted += 1
                        print(f"[SUCCESS] Inserted event: {event_id}")
                    else:
                        print(f"[WARNING] No rows inserted for event: {event_id}")

                except Exception as insert_error:
                    if "Duplicate entry" in str(insert_error):
                        print(f"[WARNING] Event already exists: {event_id}")
                    else:
                        lower_err = str(insert_error).lower()
                        if "unknown column" in lower_err or "column count" in lower_err:
                            try:
                                fallback_query = """INSERT INTO events (
                                        event_id, event_date, league,
                                        team_a, team_b, pick_type,
                                        player_team, stat_type)
                                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s)"""
                                cursor.execute(fallback_query, (event_id, event_date_for_insert, league, team_a, team_b, pick_type, player_team, stat_type))
                                if cursor.rowcount > 0:
                                    inserted += 1
                                    print(f"[SUCCESS] Fallback inserted event: {event_id}")
                            except Exception as fb_err:
                                print(f"[WARNING] Fallback failed, attempting minimal insert: {fb_err}")
                                try:
                                    minimal_query = """INSERT INTO events (
                                            event_id, event_date, league, team_a, team_b)
                                        VALUES (%s, %s, %s, %s, %s)"""
                                    cursor.execute(minimal_query, (event_id, event_date_for_insert, league, team_a, team_b))
                                    if cursor.rowcount > 0:
                                        inserted += 1
                                        print(f"[SUCCESS] Minimal fallback inserted event: {event_id}")
                                except Exception as min_err:
                                    print(f"[ERROR] Minimal fallback insert failed for {event_id}: {min_err}")
                        else:
                            print(f"[ERROR] Failed to insert event {event_id}: {insert_error}")
                            import traceback
                            traceback.print_exc()
                            raise

                # Insert crowd predictions for this event
                crowd_prob = data.get("crowd_probability")
                try:
                    print(f"[DEBUG] /api/add_event inserting crowd_prediction: event_id={event_id}, crowd_probability={crowd_prob}")
                    cursor.execute(
                        "INSERT INTO crowd_predictions (event_id, crowd_probability) VALUES (%s, %s) ON DUPLICATE KEY UPDATE crowd_probability = VALUES(crowd_probability)",
                        (event_id, crowd_prob))
                except Exception as crowd_err:
                    print(f"[ERROR] Failed to insert crowd_prediction for {event_id}: {crowd_err}")

                # After inserting the event, insert expert predictions referencing this specific event_id
                if pick_origin:
                    for expert_pick in pick_origin:
                        expert_name = expert_pick.get("name")
                        if expert_name and expert_name.strip():
                            # Validate and convert data types to match database constraints
                            try:
                                # Convert prediction to integer (0 or 1)
                                prediction = int(data.get("prediction", 1))
                                if prediction not in [0, 1]:
                                    prediction = 1  # Default to 1 if invalid

                                # Store original confidence for potential fallback
                                confidence_raw = expert_pick.get("confidence")
                                if confidence_raw is not None:
                                    # Extract numeric value from confidence
                                    match = re.search(r'(\d+\.?\d*)', str(confidence_raw))
                                    if match:
                                        confidence_str = match.group(1)
                                        original_confidence = float(confidence_str)
                                        # If confidence is > 1, assume it's a percentage and convert
                                        if original_confidence > 1.0:
                                            original_confidence = original_confidence / 100.0
                                        # Ensure it's within valid range
                                        confidence = max(0.0, min(1.0, original_confidence))
                                    else:
                                        confidence = 0.5  # Default if invalid format
                                else:
                                    confidence = 0.5  # Default if no confidence provided

                                print(f"🔄 Using original confidence: {confidence*100:.1f}% for {expert_name} (will attempt dynamic calculation after insertion)")

                                # Convert stat_threshold to float if provided
                                stat_threshold_raw = expert_pick.get("stat_threshold")
                                stat_threshold = float(stat_threshold_raw) if stat_threshold_raw is not None else None

                                # Handle prediction_time field
                                prediction_time = expert_pick.get("prediction_time")
                                if prediction_time:
                                    # If only time is provided, combine with event_date
                                    if len(prediction_time) <= 8:  # Format like "14:30:00" or "14:30"
                                        prediction_time = f"{event_date_for_insert} {prediction_time}"
                                    # If full datetime is provided, use as-is
                                else:
                                    # Prefer Odds API commence time; otherwise leave NULL to avoid misleading defaults
                                    prediction_time = api_prediction_time or None

                                print(f"🔄 Inserting prediction: expert={expert_name}, prediction={prediction}, confidence={confidence}, time={prediction_time}")

                                # Prevent phantom ChartGeneration predictions
                                if expert_name.lower() == "chartgeneration":
                                    continue

                                # Check if this expert prediction already exists to prevent duplicates
                                cursor.execute(
                                    "SELECT event_id FROM expert_predictions WHERE event_id = %s AND expert_name = %s",
                                    (event_id, expert_name))
                                existing_record = cursor.fetchone()

                                if existing_record:
                                    print(f"⚠️ Expert prediction already exists for {expert_name} on {event_id}, updating...")
                                    cursor.execute(
                                        """UPDATE expert_predictions SET
                                            prediction = %s, confidence = %s, stat_threshold = %s,
                                            team = %s, prediction_time = %s
                                        WHERE event_id = %s AND expert_name = %s""",
                                        (prediction, confidence, stat_threshold, expert_pick.get("team"), prediction_time, event_id, expert_name))
                                else:
                                    cursor.execute(
                                        """INSERT INTO expert_predictions (
                                            event_id, expert_name, prediction, confidence, team, league, game_date, stat_threshold, prediction_time
                                        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)""",
                                        (event_id, expert_name, prediction, confidence, expert_pick.get("team"), league, event_date_for_insert, stat_threshold, prediction_time))

                                expert_predictions_inserted += 1
                                print(f"✅ Inserted expert prediction for {expert_name}")

                            except Exception as expert_error:
                                print(f"❌ Failed to insert expert prediction for {expert_name}: {expert_error}")
                                import traceback
                                traceback.print_exc()

            conn.commit()
            print(f"[SUCCESS] Transaction committed. Inserted {inserted} events and {expert_predictions_inserted} expert predictions.")

            return jsonify({
                "success": True,
                "message": f"Successfully inserted {inserted} events with {expert_predictions_inserted} expert predictions",
                "events_inserted": inserted,
                "expert_predictions_inserted": expert_predictions_inserted
            })

    except Exception as e:
        print(f"[ERROR] Database operation failed: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({
            "success": False,
            "message": f"Database error: {str(e)}"
        }), 500

