import React from 'react';

interface ExpertPredictionListProps {
  experts: string[];
  selectedExpert: string;
  onSelect: (expert: string) => void;
}

const ExpertPredictionList: React.FC<ExpertPredictionListProps> = ({
  experts,
  selectedExpert,
  onSelect,
}) => (
  <div className="mb-4">
    <label className="block text-sm font-medium text-gray-300 mb-1">
      Expert Prediction
    </label>
    <select
      value={selectedExpert}
      onChange={(e) => onSelect(e.target.value)}
      className="w-full bg-[#233e6c] rounded-md p-2.5 focus:ring-2 focus:ring-[#58C612] outline-none text-white cursor-pointer"
    >
      <option value="">Select expert</option>
      {experts.map((expert) => (
        <option key={expert} value={expert}>
          {expert}
        </option>
      ))}
    </select>
  </div>
);

export default ExpertPredictionList;