# fullstack/backend/routes/optimizer.py
from flask import Blueprint

from flask import jsonify, request
from modules.RROptimizer import analyze_all_splits
from modules.ppObjects import Pick, BoostPromo, ProtectedPromo
from fullstack.backend import state
from fullstack.backend.services.parlay import generate_round_robin_subparlays
from fullstack.backend.services.confidence import confidence_score, calculate_dynamic_confidence
from modules.HandiCapperAccuracyModel import main_model
from data_science_modules.planet_scale_port import generate_event_id, submit_event
import sqlite3
from datetime import datetime
import random


@optimizer_bp.route('/optimize_split', methods=['GET'])
def optimize_split():
    sorted_picks = sorted(state.pick_objects, key=lambda x: getattr(x, "confidence_score", 0), reverse=True)
    if len(sorted_picks) < 2:
        return jsonify({
            "best_score": 0.0,
            "best_config": "Not enough picks to optimize (Need at least 2).",
            "sorted_picks": [p.to_dict() for p in sorted_picks],
            "subparlays": []
        }), 200
    best_score, best_label = analyze_all_splits(sorted_picks)
    picks_to_use = []
    subgroup_size = 2
    if isinstance(best_label, str):
        if "Full List" in best_label:
            picks_to_use = sorted_picks
        elif "Left" in best_label:
            try:
                split_index = int(best_label.split("index")[1].strip())
                picks_to_use = sorted_picks[:split_index]
            except Exception:
                picks_to_use = []
        elif "Right" in best_label:
            try:
                split_index = int(best_label.split("index")[1].strip())
                picks_to_use = sorted_picks[split_index:]
            except Exception:
                picks_to_use = []
        import re
        m = re.search(r"Size (\d+)", best_label)
        if m:
            subgroup_size = int(m.group(1))
    else:
        picks_to_use = []
        subgroup_size = 2
        best_label = "Error: Invalid optimization label received."
        best_score = 0.0
    subparlays = generate_round_robin_subparlays(picks_to_use, subgroup_size)
    final_best_score = best_score if isinstance(best_score, (int, float)) else 0.0
    return jsonify({
        "best_score": final_best_score,
        "best_config": best_label,
        "sorted_picks": [p.to_dict() for p in sorted_picks],
        "subparlays": [[p.to_dict() for p in sub] for sub in subparlays]
    })


@optimizer_bp.route('/process', methods=['POST'])
def process():
    data = request.get_json()
    try:
        name = data.get("name", "").strip()
        pick_origins = data.get("pick_origin", [])
        odds = float(data.get("odds", 0))
        leagues = data.get("league", [])
        reusable = data.get("reusable", True)
        capital_limit = int(data.get("capital_limit", 0))
        mutual_exclusion = int(data.get("mutual_exclusion", -1))
        pick_type = data.get("pick_type", "MoneyLine")
        player_team = data.get("player_team", "None")
        stat_type = data.get("stat_type", "MoneyLine")
        if not name or not odds or not pick_origins or not leagues:
            return jsonify({"response": "Missing required fields", "success": False}), 400
        implied_prob = round(1 / odds, 4)
        event_date = data.get("eventDate") or datetime.today().strftime("%Y-%m-%d")
        expert_predictions = []
        total_score = 0
        for origin_obj in pick_origins:
            origin = origin_obj.get("name")
            origin_conf = origin_obj.get("confidence")
            if not origin:
                continue
            try:
                used_conf = float(origin_conf)
            except Exception:
                used_conf = 75.0
            origin_key = origin.replace(" ", "")
            origin_profiles = state.origin_profiles if hasattr(state, 'origin_profiles') else {
                "ChalkBoardPI": 0.80,
                "HarryLock": 0.71,
                "DanGamblePOD": 0.78,
                "DanGambleAIEdge": 90.0,
                "GameScript": 0.80,
                "Winible": 0.83,
                "DoberMan": 0.76,
                "JoshMiller": 0.60,
                "Me": lambda: getattr(state, 'user_accuracy', 0.0)
            }
            if origin_key not in origin_profiles:
                raise KeyError(f"Origin key '{origin_key}' not found in origin_profiles")
            origin_accuracy = origin_profiles[origin_key]() if callable(origin_profiles[origin_key]) else origin_profiles[origin_key]
            prediction = int(data.get("prediction", 1))
            expert_predictions.append((origin, prediction, used_conf/100.0))
            score = confidence_score(odds, used_conf, origin_accuracy)
            total_score += score
        for league in leagues:
            event_id = generate_event_id(name, league)
            conn = sqlite3.connect(state.DB_PATH)
            cursor = conn.cursor()
            cursor.execute("SELECT 1 FROM events WHERE event_id = ?", (event_id,))
            exists = cursor.fetchone() is not None
            conn.close()
            if not exists:
                success, message = submit_event(
                    event_id=event_id,
                    event_date=event_date,
                    league=league,
                    team_a=(name if pick_type=="MoneyLine" else "Over"),
                    team_b=("Other" if pick_type=="MoneyLine" else "Under"),
                    crowd_probability=implied_prob,
                    expert_predictions=expert_predictions,
                    actual_result=None,
                    pick_type=pick_type,
                    player_team=player_team,
                    stat_type=stat_type,
                    player_name=None,
                    stat_threshold=None
                )
            else:
                try:
                    conn = sqlite3.connect(state.DB_PATH)
                    cursor = conn.cursor()
                    cursor.execute("""
                        INSERT OR REPLACE INTO crowd_predictions (event_id, crowd_probability)
                        VALUES (?, ?)
                    """, (event_id, implied_prob))
                    for expert_name, prediction, confidence in expert_predictions:
                        if (expert_name or '').lower() == 'chartgeneration':
                            continue
                        cursor.execute("""
                            INSERT OR REPLACE INTO expert_predictions (event_id, expert_name, prediction, confidence)
                            VALUES (?, ?, ?, ?)
                        """, (event_id, expert_name, prediction, confidence))
                        cursor.execute("SELECT 1 FROM expert_reliability WHERE expert_name = ?", (expert_name,))
                        if not cursor.fetchone():
                            cursor.execute("INSERT INTO expert_reliability (expert_name) VALUES (?)", (expert_name,))
                    conn.commit()
                    conn.close()
                    success = True
                    message = "Existing event updated with crowd prediction and new expert predictions."
                except Exception as e:
                    success = False
                    message = f"Error updating existing event: {e}"
        # Build Pick and compute dynamic confidence
        avg_expert_confidence = sum(conf for _, _, conf in expert_predictions)/len(expert_predictions) if expert_predictions else 0.75
        try:
            final_score = calculate_dynamic_confidence(event_id, avg_expert_confidence)
        except Exception:
            final_score = avg_expert_confidence * 100
        try:
            prediction_result = main_model(event_id)
            ml_prob = prediction_result.get("combined_prob", 0.5)
            logistic_prob = prediction_result.get("logistic_prob", 0.5)
            bayesian_prob = prediction_result.get("bayesian_prob", 0.5)
            bayesian_conf = prediction_result.get("quality_score", 0.5)
        except Exception:
            ml_prob = logistic_prob = bayesian_prob = bayesian_conf = 0.5
        if success:
            new_pick = Pick(name=name, odds=odds, confidence=final_score, mutual_exclusion_group=mutual_exclusion, league=league, event_id=event_id, bayesian_prob=bayesian_prob, logistic_prob=logistic_prob, bayesian_conf=bayesian_conf, stat_type=stat_type, reusable=reusable, capital_limit=capital_limit)
            state.pick_objects.append(new_pick)
        return jsonify({
            "response": message,
            "objects": [p.__dict__ for p in state.pick_objects],
            "success": success
        })
    except Exception as e:
        return jsonify({"response": f"Server error: {str(e)}", "success": False}), 500


@optimizer_bp.route('/create_boost_promo', methods=['POST'])
def create_boost_promo():
    data = request.get_json()
    boost_percentage = int(data.get("boost_percentage", 0))
    required_picks = int(data.get("required_picks", 0))
    same_sport = data.get("same_sport", False)
    boost = BoostPromo(boost_percentage, required_picks, same_sport)
    if not hasattr(state, 'boost_promo_objects'):
        state.boost_promo_objects = []
    state.boost_promo_objects.append(boost.__dict__)
    return jsonify({"response": f"Created Boost Promo: {boost.name}", "boost_promos": state.boost_promo_objects})


@optimizer_bp.route('/create_protected_promo', methods=['POST'])
def create_protected_promo():
    data = request.get_json()
    protected_amount = int(data.get("protected_amount", 0))
    eligible_leagues = data.get("eligible_leagues", [])
    protected = ProtectedPromo(protected_amount, eligible_leagues)
    if not hasattr(state, 'protected_promo_objects'):
        state.protected_promo_objects = []
    state.protected_promo_objects.append(protected.__dict__)
    return jsonify({"response": f"Created Protected Play Promo: {protected.name}", "protected_promos": state.protected_promo_objects})


@optimizer_bp.route('/submit_verified', methods=['POST'])
def submit_verified():
    data = request.get_json()
    verified = data.get("verified", [])
    conn = sqlite3.connect(state.DB_PATH)
    cursor = conn.cursor()
    updated_ids = []
    localID = 0
    for item in verified:
        localID += 1
        pick_id = localID
        user_verification = item["actual_result"]
        pick = next((p for p in state.pick_objects if hasattr(p, "pID") and p.pID == pick_id), None)
        if not pick:
            continue
        event_id = getattr(pick, "event_id", None)
        if not event_id:
            continue
        cursor.execute("SELECT prediction FROM expert_predictions WHERE event_id = ? LIMIT 1", (event_id,))
        row = cursor.fetchone()
        if row:
            expert_prediction = row[0]
            if expert_prediction == 1:
                actual_result = 1 if user_verification == 1 else 0
            else:
                actual_result = 0 if user_verification == 1 else 1
        else:
            actual_result = user_verification
        try:
            cursor.execute("UPDATE events SET actual_result = ? WHERE event_id = ?", (actual_result, event_id))
            if cursor.rowcount > 0:
                updated_ids.append(event_id)
        except Exception:
            pass
    conn.commit()
    conn.close()
    return jsonify({"message": f"Updated {len(updated_ids)} events with actual results."})


@optimizer_bp.route('/load_sample_picks', methods=['POST'])
def load_sample_picks():
    state.pick_objects = []
    num_picks = 8
    example_names = ["Lakers ML", "Yankees -1.5", "Chiefs +3", "Over 8.5", "Under 220", "Dodgers ML", "Ravens -2.5", "Heat +6", "Bills ML", "Nets Over 230"]
    leagues = ["NBA", "NFL", "MLB", "NHL"]
    for i in range(num_picks):
        name = random.choice(example_names) + f" #{i+1}"
        odds = round(random.uniform(1.05, 2.5), 2)
        mutual_exclusion_group = random.randint(0, 5)
        league = random.choice(leagues)
        reusable = random.choice([True, False])
        capital_limit = random.randint(10, 100)
        stat_type = "MoneyLine"
        event_id = f"SAMPLE-{i+1}"
        bayesian_prob = round(random.uniform(0.4, 0.9), 2)
        logistic_prob = round(random.uniform(0.4, 0.9), 2)
        bayesian_conf = round(random.uniform(0.5, 0.9), 2)
        combined_prob = round(bayesian_conf * bayesian_prob + (1 - bayesian_conf) * logistic_prob, 4)
        implied_prob = 1 / odds
        raw_score = combined_prob - implied_prob
        clamped = max(min(raw_score, 0.2), -0.2)
        scaled_score = round((clamped + 0.2) / 0.4 * 100, 2)
        new_pick = Pick(name=name, odds=odds, confidence=scaled_score, mutual_exclusion_group=mutual_exclusion_group, league=league, event_id=event_id, bayesian_prob=bayesian_prob, logistic_prob=logistic_prob, bayesian_conf=bayesian_conf, stat_type=stat_type, reusable=reusable, capital_limit=capital_limit)
        state.pick_objects.append(new_pick)
    return jsonify({"message": f"{num_picks} sample picks loaded.", "objects": [p.to_dict() for p in state.pick_objects]})

optimizer_bp = Blueprint('optimizer', __name__)

