# fullstack/backend/services/teams.py
from typing import Optional


def _normalize_team_name(name: str) -> str:
    s = (name or "").lower()
    for ch in [".", ",", "'", "-", "_", "&"]:
        s = s.replace(ch, " ")
    s = " ".join(s.split())
    s = s.replace("saint ", "st ")
    s = s.replace("d backs", "diamondbacks")
    s = s.replace("dbacks", "diamondbacks")
    s = s.replace("diamondiamondbacks", "diamondbacks")
    s = s.replace("redsox", "red sox")
    s = s.replace("whitesox", "white sox")
    s = s.replace("ny ", "new york ")
    s = s.replace("la ", "los angeles ")
    return s


def _teams_match(a: str, b: str) -> bool:
    na, nb = _normalize_team_name(a), _normalize_team_name(b)
    if not na or not nb:
        return False
    if na == nb:
        return True
    if na in nb or nb in na:
        return True
    return False


MLB_NICKNAME_MAP: dict[str, str] = {
    "arizona diamondbacks": "Diamondbacks",
    "diamondbacks": "Diamondbacks",
    "atlanta braves": "Braves",
    "braves": "Braves",
    "baltimore orioles": "Orioles",
    "orioles": "Orioles",
    "boston red sox": "Red Sox",
    "red sox": "Red Sox",
    "chicago cubs": "Cubs",
    "cubs": "Cubs",
    "chicago white sox": "White Sox",
    "white sox": "White Sox",
    "cincinnati reds": "Reds",
    "reds": "Reds",
    "cleveland guardians": "Guardians",
    "guardians": "Guardians",
    "colorado rockies": "Rockies",
    "rockies": "Rockies",
    "detroit tigers": "Tigers",
    "tigers": "Tigers",
    "houston astros": "Astros",
    "astros": "Astros",
    "kansas city royals": "Royals",
    "royals": "Royals",
    "los angeles angels": "Angels",
    "la angels": "Angels",
    "angels": "Angels",
    "los angeles dodgers": "Dodgers",
    "la dodgers": "Dodgers",
    "dodgers": "Dodgers",
    "miami marlins": "Marlins",
    "marlins": "Marlins",
    "milwaukee brewers": "Brewers",
    "brewers": "Brewers",
    "minnesota twins": "Twins",
    "twins": "Twins",
    "new york mets": "Mets",
    "ny mets": "Mets",
    "mets": "Mets",
    "new york yankees": "Yankees",
    "ny yankees": "Yankees",
    "yankees": "Yankees",
    "oakland athletics": "Athletics",
    "the athletics": "Athletics",
    "athletics": "Athletics",
    "philadelphia phillies": "Phillies",
    "phillies": "Phillies",
    "pittsburgh pirates": "Pirates",
    "pirates": "Pirates",
    "san diego padres": "Padres",
    "padres": "Padres",
    "san francisco giants": "Giants",
    "giants": "Giants",
    "seattle mariners": "Mariners",
    "mariners": "Mariners",
    "st louis cardinals": "Cardinals",
    "saint louis cardinals": "Cardinals",
    "cardinals": "Cardinals",
    "tampa bay rays": "Rays",
    "rays": "Rays",
    "texas rangers": "Rangers",
    "rangers": "Rangers",
    "toronto blue jays": "Blue Jays",
    "blue jays": "Blue Jays",
    "washington nationals": "Nationals",
    "nationals": "Nationals",
}


def team_to_nickname(name: Optional[str]) -> Optional[str]:
    try:
        s_original = (name or "")
        s = _normalize_team_name(s_original)
        if not s:
            return name
        mapped = MLB_NICKNAME_MAP.get(s) or MLB_NICKNAME_MAP.get(s.replace(" ", ""))
        if mapped:
            return mapped
        last = s.split(" ")[-1]
        if last and len(last) >= 3:
            return last.capitalize()
        elif s:
            if len(s) <= 2:
                print(f"[WARNING] team_to_nickname: Very short team name '{s_original}' -> '{s}', returning title-cased")
            return s.title()
        return s_original
    except Exception as e:
        print(f"[ERROR] team_to_nickname failed for '{name}': {e}")
        return name


def _is_unknown_team(value: Optional[str]) -> bool:
    """Check if team value represents an unknown/placeholder team."""
    v = (value or "").strip().lower()
    return v in ("", "unknown", "null", "none", "other")


def _format_nickname(name: Optional[str]) -> str:
    """Format team name as a proper nickname."""
    try:
        nm = (name or "").strip()
        from fullstack.backend.services.data_validation import clean_team_name
        cleaned = clean_team_name(nm)
        return cleaned if cleaned else "Unknown"
    except Exception:
        return (name or "Unknown").strip() or "Unknown"

