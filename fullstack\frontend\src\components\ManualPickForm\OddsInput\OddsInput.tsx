import React, { useState, useEffect } from "react";
import ToggleSwitch from "../../ToggleSwitch/ToggleSwitch";
import { OddsInputProps, OddsFormat, OddsSign } from "../../../types/manualPick";

const SUPPORTED_SPORTSBOOKS = [
  '888sport',
  'BET99',
  'BetDSI',
  'BetMGM',
  'BetOnline',
  'BetRivers',
  'BetUS',
  'BetVictor',
  '<PERSON>no',
  '<PERSON><PERSON><PERSON>',
  'Betfair',
  'Betfred',
  'Betsafe',
  'Betway',
  'Bodog',
  'BookMaker',
  'Caesars',
  'DraftKings',
  'ESPN BET',
  'FanDuel',
  'Golden Nugget',
  'Hard Rock',
  'Jazz Sports',
  'LeoVegas',
  'Pinnacle',
  'PowerPlay',
  'PrizePicks',
  'TwinSpires',
  'Underdog Fantasy',
  'Unibet',
  'William Hill',
  'Wind Creek',
  'Xbet',
  'YouWager',
  'bet365',
  'betPARX'
];

/**
 * OddsInput component for handling odds input with format toggles and sportsbook selection
 */
const OddsInput: React.FC<OddsInputProps> = ({
  odds,
  format = "Decimal",
  sign = "positive",
  source_name = "",
  onChange,
}) => {
  // Local state
  const [oddsValue, setOddsValue] = useState<string>("");
  const [oddsFormat, setOddsFormat] = useState<OddsFormat>(format);
  const [oddsSign, setOddsSign] = useState<OddsSign>(sign);
  const [selectedSportsbook, setSelectedSportsbook] = useState<string>(source_name);
  const [isDropdownOpen, setIsDropdownOpen] = useState<boolean>(false);

  // Initialize local state from props
  useEffect(() => {
    setOddsValue(odds.toString());
    setOddsFormat(format);
    setOddsSign(sign);
    setSelectedSportsbook(source_name);
  }, [odds, format, sign, source_name]);

  // Handle odds value input change
  const handleOddsChange = (value: string) => {
    // Allow empty string, digits, decimal point, and decimal numbers
    if (value === "" || /^(\d*\.?\d*|\.\d*)$/.test(value)) {
      setOddsValue(value);
      
      const numericValue = parseFloat(value);
      if (!isNaN(numericValue)) {
        onChange({
          odds: numericValue,
          format: oddsFormat,
          sign: oddsSign,
          source_name: selectedSportsbook
        });
      }
    }
  };

  // Handle format toggle (American vs Decimal)
  const handleFormatToggle = (isAmerican: boolean) => {
    const newFormat: OddsFormat = isAmerican ? "American" : "Decimal";
    setOddsFormat(newFormat);
    
    onChange({
      odds: parseFloat(oddsValue) || 0,
      format: newFormat,
      sign: oddsSign,
      source_name: selectedSportsbook
    });
  };

  // Handle sign toggle (positive vs negative for American odds)
  const handleSignToggle = (isPositive: boolean) => {
    const newSign: OddsSign = isPositive ? "positive" : "negative";
    setOddsSign(newSign);
    
    onChange({
      odds: parseFloat(oddsValue) || 0,
      format: oddsFormat,
      sign: newSign,
      source_name: selectedSportsbook
    });
  };

  // Handle sportsbook selection
  const handleSportsbookSelect = (sourceName: string) => {
    setSelectedSportsbook(sourceName);
    setIsDropdownOpen(false);

    onChange({
      odds: parseFloat(oddsValue) || 0,
      format: oddsFormat,
      sign: oddsSign,
      source_name: sourceName,
    });
  };

  // Format the displayed odds value with sign prefix for American odds
  const getDisplayValue = () => {
    if (oddsFormat === "American" && oddsSign === "negative" && oddsValue) {
      return oddsValue.startsWith("–") ? oddsValue : `–${oddsValue}`;
    }
    return oddsValue;
  };

  return (
    <div className="space-y-4">
      {/* Odds Format Toggle */}
      <div className="flex items-center justify-between">
        <label className="block text-sm font-medium text-gray-300">
          Odds Format
        </label>
        <ToggleSwitch
          label={oddsFormat === "American" ? "American" : "Decimal"}
          checked={oddsFormat === "American"}
          onChange={handleFormatToggle}
        />
      </div>

      {/* American Odds Sign Toggle */}
      {oddsFormat === "American" && (
        <div className="flex items-center justify-between">
          <label className="block text-sm font-medium text-gray-300">
            Odds Sign
          </label>
          <ToggleSwitch
            label={oddsSign === "positive" ? "Positive (+)" : "Negative (–)"}
            checked={oddsSign === "positive"}
            onChange={handleSignToggle}
          />
        </div>
      )}

      {/* Odds Input */}
      <div>
        <label className="block text-sm font-medium text-gray-300 mb-1">
          Odds Value *
        </label>
        <input
          type="text"
          value={getDisplayValue()}
          onChange={(e) => {
            let value = e.target.value;
            // Remove the negative sign prefix if it exists for American odds
            if (oddsFormat === "American" && value.startsWith("–")) {
              value = value.substring(1);
            }
            handleOddsChange(value);
          }}
          className="w-full bg-[#233e6c] rounded-md p-2.5 focus:ring-2 focus:ring-[#58C612] outline-none text-white"
          placeholder={oddsFormat === "American" ? "110" : "1.91"}
          required
        />
      </div>

      {/* Sportsbook Selection */}
      <div>
        <label className="block text-sm font-medium text-gray-300 mb-1">
          Source
        </label>
        <div className="relative">
          <button
            type="button"
            onClick={() => setIsDropdownOpen(!isDropdownOpen)}
            className="w-full bg-[#233e6c] rounded-md p-2.5 focus:ring-2 focus:ring-[#58C612] outline-none text-white text-left flex items-center justify-between cursor-pointer"
          >
            <span>{selectedSportsbook || "Select Source"}</span>
            <svg
              className={`w-4 h-4 transition-transform ${isDropdownOpen ? 'rotate-180' : ''}`}
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </svg>
          </button>

          {isDropdownOpen && (
            <div className="absolute z-10 w-full mt-1 bg-[#233e6c] border border-gray-600 rounded-md shadow-lg max-h-60 overflow-y-auto">
              <div className="py-1">
                {/* Clear selection option */}
                <button
                  type="button"
                  onClick={() => handleSportsbookSelect("")}
                  className="w-full text-left px-3 py-2 text-sm text-gray-400 hover:bg-[#1a2d54] hover:text-white transition-colors"
                >
                  -- None Selected --
                </button>
                
                {/* Sportsbook options */}
                {SUPPORTED_SPORTSBOOKS.map((sourceName) => (
                  <button
                    key={sourceName}
                    type="button"
                    onClick={() => handleSportsbookSelect(sourceName)}
                    className={`w-full text-left px-3 py-2 text-sm transition-colors hover:bg-[#1a2d54] ${
                      selectedSportsbook === sourceName
                        ? 'bg-[#58C612] text-black'
                        : 'text-white hover:text-white'
                    }`}
                  >
                    {sourceName}
                  </button>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default OddsInput;