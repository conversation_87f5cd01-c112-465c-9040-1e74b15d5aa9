# fullstack/backend/routes/frontend.py
from flask import Blueprint, send_from_directory
import os

frontend_bp = Blueprint('frontend', __name__)

# This blueprint will be registered without a prefix and used for catch-all
@frontend_bp.route('/', defaults={'path': ''})
@frontend_bp.route('/<path:path>')
def serve_react_app(path):
    from flask import current_app as app
    FilePath = os.path.join(app.static_folder, path)
    if path != "" and os.path.exists(FilePath):
        return send_from_directory(app.static_folder, path)
    return send_from_directory(app.static_folder, 'index.html')

