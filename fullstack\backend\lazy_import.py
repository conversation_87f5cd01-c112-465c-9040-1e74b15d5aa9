# fullstack/backend/lazy_import.py
import importlib
from types import ModuleType

_lazy_cache: dict[str, ModuleType] = {}


def lazy_import(module_name: str) -> ModuleType:
    """
    Import module on first use and cache it to avoid import-time cost
    when this module is imported.
    """
    if module_name in _lazy_cache:
        return _lazy_cache[module_name]
    mod = importlib.import_module(module_name)
    _lazy_cache[module_name] = mod
    return mod