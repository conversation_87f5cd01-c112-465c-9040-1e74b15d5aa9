{"model": "openai/gpt-5-nano", "stream": false, "messages": [{"role": "system", "content": "Return ONE SINGLE-LINE JSON: {\"picks\":[{\"league\":\"\",\"team_a\":\"\",\"team_b\":\"\",\"pick_type\":\"\",\"player_name\":\"\",\"stat_type\":\"\",\"stat_threshold\":\"\",\"odds\":\"\",\"prediction\":\"\",\"game_date\":\"\",\"prediction_time\":\"\"}]}. HARD RULES: Always put the correct name of the league (such as NBA, NFL, NHL, UFC, MMA, WNBA, etc). Always output every supported play as one item per bullet/number. Use the nearest preceding matchup header to fill opponents. Required non-empty fields: league, team_a, team_b, pick_type, stat_type, game_date; props also require player_name. Teams are nicknames only; expand WS/CWS→White Sox, LAA→Angels, LAD→Dodgers, DAL→Cowboys, PHI→Eagles, NYG→Giants, WAS→Commanders. MoneyLine: team_a=selected team, team_b=opponent, stat_type=MoneyLine, stat_threshold=\"\". Spread: team_a=side played, team_b=opponent, stat_type=Spread, stat_threshold=absolute number. Totals: team_a=Over, team_b=Under, stat_type=Total, stat_threshold=number. Props: player_name \"F. LastName\"; team_a=Over, team_b=Under; stat_type short code without direction (K,H,HR,PTS,AST,REB,Total Bases); stat_threshold=number. CRUCIAL PREDICTION RULES: Always set prediction=1 (this means pick team_a which is the winning side). For Totals: team_a=Over means Over pick, so prediction=1. For Props: team_a=Over means Over pick, so prediction=1. For MoneyLine/Spread: team_a=selected team, so prediction=1. All of the picks provided will be FOR TODAY {{ 313.data.date }}. Otherwise, if the date is for today, I will provide the unique games and times for today, to fill in any gaps (if available): {{ 313.data.games_flattened_str }}. Dates: game_date=YYYY-MM-DD; prediction_time=YYYY-MM-DD HH:MM:SS America/Los_Angeles converted from ET/CT/MT/PT. Final sanity pass: if any team fields empty then for Totals/Props set Over/Under, else set \"null\"; ensure MoneyLine stat_threshold is blank and others numeric. Output exactly one FULLY valid JSON line with no extra text, and the necessary data for the pick is CRUCIAL. There should be at least a player_name or player_team (winning player or team) or both if possible. But never leave both blank."}, {"role": "user", "content": "Parse picks from: If you see a confidence score, use that for the odds value (e.g., 72, 75, etc){{ 213.task.capturedTexts.`Sports Picks` }} "}]}