import React from "react";

interface InsertTestDataButtonProps {
  onClick: () => void;
}

/**
 * Small utility button used to auto-populate the Manual Pick Form with sample data.
 */
const InsertTestDataButton: React.FC<InsertTestDataButtonProps> = ({
  onClick,
}) => {
  return (
    <button
      type="button"
      onClick={onClick}
      className="px-3 py-1 bg-[#58C612] text-black font-semibold rounded-md hover:bg-[#449e10] transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-900 focus:ring-[#58C612]"
    >
      Insert Test Data
    </button>
  );
};

export default InsertTestDataButton;
