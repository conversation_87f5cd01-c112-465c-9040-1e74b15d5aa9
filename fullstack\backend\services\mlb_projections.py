# fullstack/backend/services/mlb_projections.py
import json
import re
from typing import Dict, List, Any, Optional
from data_science_modules.planet_scale_port import get_connection

try:
    from data_science_modules.OddsFetcher import get_mlb_events, find_event_commence_time
except Exception:
    get_mlb_events = None
    find_event_commence_time = None

try:
    from zoneinfo import ZoneInfo
except ImportError:
    from datetime import timezone, timedelta
    class ZoneInfo:
        def __init__(self, name):
            if name == "America/Los_Angeles":
                self.offset = timedelta(hours=-8)  # Simplified as PST
            else:
                self.offset = timedelta(0)
        def utcoffset(self, dt):
            return self.offset


def clean_projection_data(projection: Dict[str, Any], events_cache: Optional[List] = None) -> Dict[str, Any]:
    """Clean and convert projection data for database insertion."""
    cleaned = {}
    
    for key, value in projection.items():
        # Convert empty strings to None
        if value == "" or value is None:
            cleaned[key] = None
        # Handle percentage values
        elif isinstance(value, str) and value.endswith('%'):
            try:
                # Convert "97.00%" to 97.00 (keep as percentage for database)
                cleaned[key] = float(value.rstrip('%'))
            except ValueError:
                cleaned[key] = None
        # Handle numeric strings
        elif isinstance(value, str) and key not in [
                'game_date', 'away_team', 'home_team', 'away_sp',
                'home_sp', 'winning_team', 'player_name'
        ]:
            try:
                # Try to convert to float if it's a numeric field
                if '.' in value or value.isdigit() or (
                        value.startswith('-')
                        and value[1:].replace('.', '').isdigit()):
                    numeric_value = float(value)
                    # Check for reasonable ranges to catch potential data issues
                    if abs(numeric_value) > 1000000:  # Sanity check for extremely large values
                        print(f"[WARNING] Unusually large value for {key}: {numeric_value}")
                    cleaned[key] = numeric_value
                else:
                    cleaned[key] = value
            except (ValueError, OverflowError):
                print(f"[WARNING] Could not convert {key} value '{value}' to number, keeping as string")
                cleaned[key] = value
        else:
            cleaned[key] = value

        # Normalize game_date to YYYY-MM-DD in Los Angeles time using TheOdds API commence_time when possible
        if key == 'game_date':
            try:
                away_team = projection.get('away_team')
                home_team = projection.get('home_team')
                prefer = None
                if isinstance(cleaned.get('game_date'), str) and cleaned['game_date']:
                    prefer = cleaned['game_date'].split('T')[0].split(' ')[0]
                odds_api_date, odds_api_dt = (None, None)
                if find_event_commence_time and away_team and home_team:
                    try:
                        events = events_cache
                        commence = None
                        if events:
                            # quick match from preloaded events
                            commence = find_event_commence_time(away_team, home_team, events)
                        if not commence:
                            commence = find_event_commence_time(away_team, home_team, None)
                        if commence and 'T' in commence:
                            date_part, time_part = commence.split('T', 1)
                            time_part = time_part.replace('Z', '')
                            from datetime import datetime as _dt
                            dt_utc = _dt.strptime(f"{date_part} {time_part[:8]}", "%Y-%m-%d %H:%M:%S").replace(tzinfo=ZoneInfo("UTC"))
                            dt_pt = dt_utc.astimezone(ZoneInfo("America/Los_Angeles"))
                            odds_api_date = dt_pt.strftime('%Y-%m-%d')
                            odds_api_dt = dt_pt.strftime('%Y-%m-%d %H:%M:%S')
                    except Exception:
                        pass
                if odds_api_date:
                    cleaned['game_date'] = odds_api_date
                    cleaned['game_datetime_pt'] = odds_api_dt
                else:
                    # Fallback to parsing incoming date string
                    if isinstance(cleaned.get('game_date'), str):
                        s = cleaned['game_date']
                        if 'T' in s:
                            s = s.split('T')[0]
                        if ' ' in s:
                            s = s.split(' ')[0]
                        try:
                            from datetime import datetime as _dt
                            _dt.strptime(s, '%Y-%m-%d')
                            cleaned['game_date'] = s
                        except ValueError:
                            try:
                                _dt.strptime(s, '%m/%d/%Y')
                                dt_obj = _dt.strptime(s, '%m/%d/%Y')
                                cleaned['game_date'] = dt_obj.strftime('%Y-%m-%d')
                            except ValueError:
                                print(f"[WARNING] Could not parse game_date: {s}")
                                cleaned['game_date'] = prefer or "2025-01-01"
            except Exception as e:
                print(f"[WARNING] Error processing game_date: {e}")
                cleaned['game_date'] = "2025-01-01"

    return cleaned


def repair_json_data(raw_data: str) -> str:
    """Repair common JSON issues from Make.com and other sources."""
    # Step 1: Fix the critical Make.com issues
    # Fix empty stat_threshold values
    raw_data = re.sub(r'"stat_threshold":\s*,', '"stat_threshold": null,', raw_data)
    raw_data = re.sub(r'"stat_threshold":\s*}', '"stat_threshold": null}', raw_data)
    
    # Step 2: Fix escaped quotes in string values
    raw_data = raw_data.replace('\\"', '"')
    raw_data = raw_data.replace('"\\""', '""')
    
    # Step 3: Fix any remaining structural issues
    raw_data = re.sub(r',\s*}', '}', raw_data)
    raw_data = re.sub(r',\s*]', ']', raw_data)
    
    return raw_data


def propagate_game_metadata(projections: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """Propagate common game metadata (game_date, teams, SPs, totals) across rows."""
    if len(projections) == 0:
        return projections
        
    game_metadata = {}
    metadata_fields = [
        'game_date', 'away_team', 'home_team', 'away_sp', 'home_sp',
        'away_score', 'home_score', 'total_runs_scored',
        'home_team_run_diff', 'winning_team', 'game_xHRs',
        'home_win_pct', 'xRFI'
    ]
    
    for projection in projections:
        for field in metadata_fields:
            if field in projection and projection[field] not in (None, ""):
                game_metadata.setdefault(field, projection[field])
                
    for i, projection in enumerate(projections):
        for field, value in game_metadata.items():
            if field not in projection or projection[field] in (None, ""):
                projections[i][field] = value
                
    return projections


def process_mlb_projections(projections: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Process MLB projections and insert/update them in the database."""
    results = {
        "total_projections": len(projections),
        "successful_inserts": 0,
        "successful_updates": 0,
        "failed_operations": 0
    }
    errors = []
    processing_errors = []
    
    conn = get_connection()
    cursor = conn.cursor()
    
    events_cache = None
    try:
        if get_mlb_events:
            events_cache = get_mlb_events()
    except Exception:
        events_cache = None
    
    try:
        # Propagate metadata across projections
        projections = propagate_game_metadata(projections)
        
        for idx, projection in enumerate(projections):
            try:
                p = clean_projection_data(projection, events_cache)
                
                game_date = p.get('game_date')
                away = p.get('away_team')
                home = p.get('home_team')
                player = p.get('player_name')
                
                if not all([game_date, away, home, player]):
                    processing_errors.append({
                        "projection_index": idx,
                        "error": f"Missing required fields: game_date={game_date}, away_team={away}, home_team={home}, player_name={player}"
                    })
                    results["failed_operations"] += 1
                    continue
                
                # Check if record exists
                cursor.execute(
                    "SELECT 1 FROM mlb_projection_sheet WHERE game_date=%s AND away_team=%s AND home_team=%s AND player_name=%s",
                    (game_date, away, home, player)
                )
                exists = cursor.fetchone() is not None
                
                # Prepare fields and values for insert/update
                fields = [k for k in p.keys() if k not in ('game_date', 'away_team', 'home_team', 'player_name') and p[k] is not None]
                if not fields:
                    processing_errors.append({
                        "projection_index": idx,
                        "error": "No valid fields to insert/update"
                    })
                    results["failed_operations"] += 1
                    continue
                
                # Remove fields that don't exist in the table schema
                excluded_fields = {'game_datetime_pt'}  # Add any fields that shouldn't be in DB
                fields = [f for f in fields if f not in excluded_fields]
                
                if not fields:
                    processing_errors.append({
                        "projection_index": idx,
                        "error": "No valid database fields after filtering"
                    })
                    results["failed_operations"] += 1
                    continue
                
                # Build field list string for SQL
                fields_str = ",".join(fields).split(",")
                
                values = tuple(p.get(f) for f in fields) + (game_date, away, home, player)
                
                if exists:
                    cursor.execute(
                        f"UPDATE mlb_projection_sheet SET {','.join(f'{f}=%s' for f in fields)} WHERE game_date=%s AND away_team=%s AND home_team=%s AND player_name=%s",
                        values,
                    )
                    results["successful_updates"] += 1
                else:
                    cursor.execute(
                        f"INSERT INTO mlb_projection_sheet (game_date,away_team,home_team,player_name,{','.join(fields)}) VALUES (%s,%s,%s,%s,{','.join(['%s']*len(fields))})",
                        (game_date, away, home, player, *values[:-4]),
                    )
                    results["successful_inserts"] += 1
                    
            except Exception as e:
                results["failed_operations"] += 1
                errors.append({"projection_index": idx, "error": str(e)})
        
        conn.commit()
        
    finally:
        if cursor:
            cursor.close()
        conn.close()
    
    return {
        "results": results,
        "errors": errors,
        "processing_errors": processing_errors
    }
