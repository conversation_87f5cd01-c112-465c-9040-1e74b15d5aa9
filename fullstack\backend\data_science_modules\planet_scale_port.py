import pymysql
pymysql.install_as_MySQLdb()
import os
from dotenv import load_dotenv
from datetime import datetime
from typing import Optional, List, Tuple
from pymysql.cursors import DictCursor
from data_science_modules.goalserve_test import fetch_MLB_team_stats, get_todays_events_with_handicappers as _gtewh
import json
# Load environment variables
load_dotenv()

def get_player_logs_table_name(league: str) -> str:
    """
    Get the correct table name for player logs based on league.
    MLB uses 'MLB_Logs' while other leagues use '{LEAGUE}_Player_Logs'.
    """
    if league.upper() == "MLB":
        return "MLB_Logs"
    else:
        return f"{league}_Player_Logs"

def get_connection():
    return pymysql.connect(
        host=os.getenv("DATABASE_HOST"),
        user=os.getenv("DATABASE_USERNAME"),
        password=os.getenv("DATABASE_PASSWORD"),
        database=os.getenv("DATABASE"),
        ssl={"ssl_ca": "/etc/ssl/cert.pem"}  # Path to trusted CA cert on macOS
    )

def get_dict_connection():
    return pymysql.connect(
        host=os.getenv("DATABASE_HOST"),
        user=os.getenv("DATABASE_USERNAME"),
        password=os.getenv("DATABASE_PASSWORD"),
        database=os.getenv("DATABASE"),
        ssl={"ssl_ca": "/etc/ssl/cert.pem"},  # Path to trusted CA cert on macOS
        cursorclass=DictCursor
    )

def add_event(event_id: str, event_date: str, league: str, team_a: str, team_b: str, actual_result: Optional[int] = None):
    with get_connection() as conn:
        cursor = conn.cursor()
        cursor.execute("""
            INSERT IGNORE INTO events (event_id, event_date, league, team_a, team_b, actual_result)
            VALUES (%s, %s, %s, %s, %s, %s)
        """, (event_id, event_date, league, team_a, team_b, actual_result))




def insert_nba_player_log(
    player_name: str,
    game_date: str,
    team: Optional[str] = None,
    opponent: Optional[str] = None,
    points: Optional[float] = None,
    rebounds: Optional[float] = None,
    assists: Optional[float] = None,
    personal_fouls: Optional[float] = None,
    threes_made: Optional[float] = None,
    blocks: Optional[float] = None,
    steals: Optional[float] = None,
    turnovers: Optional[float] = None,
    minutes: Optional[float] = None
):
    print("🏀 Inserting NBA player log:", player_name)
    with get_connection() as conn:
        cursor = conn.cursor()
        table_name = get_player_logs_table_name("NBA")
        cursor.execute(f"""
            INSERT INTO {table_name} (
                player_name, game_date, team, opponent,
                points, rebounds, assists, personal_fouls, threes_made,
                blocks, steals, turnovers, minutes
            )
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """, (
            player_name, game_date, team, opponent,
            points, rebounds, assists, personal_fouls, threes_made,
            blocks, steals, turnovers, minutes
        ))

def bulk_import_mlb_logs(api_key, start_date, end_date):
    current = start_date

    while current <= end_date:
        date_api = current.strftime("%d.%m.%Y")
        game_date = current.strftime("%Y-%m-%d")
        print(f"\n Processing {game_date}...")

        all_stats = []

        for team in [
            "Braves", "Marlins", "Mets", "Phillies", "Nationals",
            "Cubs", "Pirates", "Reds", "Brewers", "Cardinals",
            "Yankees", "Red Sox", "Rays", "Blue Jays", "Orioles",
            "Astros", "Rangers", "Angels", "Athletics", "Mariners",
            "Rockies", "Diamondbacks", "Dodgers", "Giants",
            "Padres", "Royals", "Twins", "White Sox", "Tigers"
        ]:
            try:
                stats = fetch_MLB_team_stats(api_key, date_api, team)

                if not stats:
                    continue  # Skip teams with no data

                for stat in stats:
                    stat["game_date"] = game_date  # SQL format
                all_stats.extend(stats)

            except Exception as e:
                print(f"❌ Failed to fetch stats for {team} on {date_api}: {e}")

        try:
            inserted = bulk_insert_mlb_logs(all_stats)
            print(f"✅ Inserted {inserted} logs for {game_date}")
        except Exception as insert_err:
            print(f"❌ Bulk insert error on {game_date}: {insert_err}")

        current += timedelta(days=1)

    print("\n Done importing MLB logs.")

def bulk_insert_mlb_logs(logs: List[dict]):
    if not logs:
        print(" No logs insert.")
        return 0

    rows = []
    for stat in logs:
        #print(stat)
        row = (
            stat.get("player_name"),
            stat.get("game_date"),
            stat.get("isHitter"),
            stat.get("isPitcher"),
            stat.get("team"),
            stat.get("opponent"),
            stat.get("hits"),
            stat.get("singles"),
            stat.get("doubles"),
            stat.get("triples"),
            stat.get("runs"),
            stat.get("rbi"),
            stat.get("home_runs"),
            stat.get("walks_hitter"),
            stat.get("hit_by_pitch_hitter"),
            stat.get("at_bats_hitter"),
            stat.get("hit_by_pitch_pitcher"),
            stat.get("fantasy_points_hitter"),
            stat.get("strikeouts_hitter"),
            stat.get("total_bases"),
            stat.get("stolen_bases"),
            stat.get("earned_runs"),
            stat.get("total_pitches"),
            stat.get("strikeouts_pitcher"),
            stat.get("outs_pitched"),
            stat.get("hits_allowed"),
            stat.get("win_credited"),
            stat.get("quality_start")
        )
        # if not enough or too many stats 
        if len(row) != 28: 
            print(f"❗ Skipping row for {stat.get('player_name')} on {stat.get('game_date')}: expected 28 fields, got {len(row)}")
            #print(row)
            return
            continue
        # if not type string, int, float, null
        if any(type(val) not in [str, int, float, type(None)] for val in row):
            print(f" Invalid value types in row: {row}")
            print(f"❗ Skipping row for {stat.get('player_name')} on {stat.get('game_date')}: contains unsupported data types")
            continue

        rows.append(row)

    if not rows:
        print("no valid rows to insert")
        return 0

    try:
        with get_connection() as conn:
            cursor = conn.cursor()
            table_name = get_player_logs_table_name("MLB")
            for row in rows:
                print(f" Inserting row: {row}")
                cursor.execute(f"""
                    INSERT INTO {table_name} (
                        player_name, game_date, isHitter, isPitcher,
                        team, opponent,
                        hits, singles, doubles, triples, runs, rbi, home_runs,
                        walks_hitter, hit_by_pitch_hitter, at_bats_hitter,
                        hit_by_pitch_pitcher, fantasy_points_hitter, strikeouts_hitter, total_bases,
                        stolen_bases, earned_runs, total_pitches, strikeouts_pitcher,
                        outs_pitched, hits_allowed, win_credited, quality_start
                    ) VALUES (
                        %s, %s, %s, %s, 
                        %s, %s,
                        %s, %s, %s, %s, %s, %s, %s,
                        %s, %s, %s, 
                        %s, %s, %s, %s,
                        %s, %s, %s, %s, 
                        %s, %s, %s, %s
                    )
                """, row)
            conn.commit()
        return len(rows)

    except Exception as e:
        print(f"❌ Bulk insert failed: {e}")
        return 0


def update_nba_derived_fields():
    with get_connection() as conn:
        cursor = conn.cursor()
        table_name = get_player_logs_table_name("NBA")
        cursor.execute(f"SELECT entry_id, points, rebounds, assists, blocks, steals, turnovers FROM {table_name}")
        rows = cursor.fetchall()

        for row in rows:
            entry_id, points, rebounds, assists, blocks, steals, turnovers = row
            points = points or 0
            rebounds = rebounds or 0
            assists = assists or 0
            blocks = blocks or 0
            steals = steals or 0
            turnovers = turnovers or 0

            pa = points + assists
            pr = points + rebounds
            ra = rebounds + assists
            pra = pa + rebounds
            fantasy_points = (
                points * 1 +
                rebounds * 1.2 +
                assists * 1.5 +
                blocks * 3 +
                steals * 3 +
                turnovers * (-1)
            )

            cursor.execute(f"""
                UPDATE {table_name}
                SET pa = %s, pr = %s, ra = %s, pra = %s, fantasy_points = %s
                WHERE entry_id = %s
            """, (pa, pr, ra, pra, fantasy_points, entry_id))

def add_crowd_prediction(event_id: str, crowd_probability: float, source_name: str = "CrowdConsensus"):
    with get_connection() as conn:
        cursor = conn.cursor()
        cursor.execute("""
            INSERT INTO crowd_predictions (event_id, crowd_probability, source_name)
            VALUES (%s, %s, %s)
            ON DUPLICATE KEY UPDATE crowd_probability = VALUES(crowd_probability)
        """, (event_id, crowd_probability, source_name))

def add_expert_prediction(event_id: str, expert_name: str, prediction: int, confidence: Optional[float] = None):
    with get_connection() as conn:
        cursor = conn.cursor()
        cursor.execute("""
            INSERT INTO expert_predictions (event_id, expert_name, prediction, confidence)
            VALUES (%s, %s, %s, %s)
            ON DUPLICATE KEY UPDATE 
                prediction = VALUES(prediction),
                confidence = VALUES(confidence)
        """, (event_id, expert_name, prediction, confidence))

def add_multiple_expert_predictions(predictions: List[Tuple[str, str, int, Optional[float]]]):
    with get_connection() as conn:
        cursor = conn.cursor()
        for prediction in predictions:
            cursor.execute("""
                INSERT INTO expert_predictions (event_id, expert_name, prediction, confidence)
                VALUES (%s, %s, %s, %s)
                ON DUPLICATE KEY UPDATE 
                    prediction = VALUES(prediction),
                    confidence = VALUES(confidence)
            """, prediction)

def normalize_stat_type_for_storage(stat_type: str) -> str:
    """
    Normalize stat type to Title Case for consistent database storage.
    """
    if not stat_type:
        return "Points"  # Default fallback, title-cased
    # Strip whitespace, convert to lowercase, then title case multi-word strings
    return stat_type.strip().lower().title()

def generate_event_id(pick_name: str, league: str) -> str:
    clean = pick_name.upper().replace(" ", "").replace("/", "-")
    return f"{datetime.today().strftime('%Y-%m-%d')}-{league}-{clean}"

def validate_and_correct_event_id(event_id: str) -> tuple[str, list[str]]:
    """
    Validates and corrects common event ID formatting issues.
    
    Handles issues like:
    - Player names with periods/spaces (Z.GELOF -> ZGELOF)
    - Incorrect stat types for leagues (POINTS for MLB -> H, HR, etc.)
    - Missing decimal points in thresholds
    - Case sensitivity issues
    
    Args:
        event_id: The event ID to validate and correct
        
    Returns:
        tuple: (corrected_event_id, list_of_corrections_made)
    """
    import re
    
    corrections = []
    original_event_id = event_id
    
    # Expected format: YYYY-MM-DD-LEAGUE-PLAYERNAME##.#STATTYPE
    pattern = r'^(\d{4}-\d{2}-\d{2})-([A-Z]+)-(.+?)(\d+(?:\.\d+)?)(.*?)$'
    match = re.match(pattern, event_id.upper())
    
    if not match:
        corrections.append(f"Invalid event ID format: {event_id}")
        return event_id, corrections
    
    date_part, league, player_part, threshold_part, stat_part = match.groups()
    
    # Fix player name: remove periods and spaces
    original_player = player_part
    clean_player = player_part.replace(".", "").replace(" ", "")
    if clean_player != original_player:
        corrections.append(f"Cleaned player name: '{original_player}' -> '{clean_player}'")
    
    # Fix threshold format: ensure decimal point
    original_threshold = threshold_part
    try:
        threshold_float = float(threshold_part)
        formatted_threshold = f"{threshold_float:.1f}"
        if formatted_threshold != original_threshold:
            corrections.append(f"Fixed threshold format: '{original_threshold}' -> '{formatted_threshold}'")
        threshold_part = formatted_threshold
    except ValueError:
        corrections.append(f"Invalid threshold format: '{original_threshold}'")
    
    # Map stat types to standard abbreviations by league
    original_stat = stat_part
    stat_type_map = {
        # NBA/NFL stats
        'POINTS': 'PTS',
        'POINT': 'PTS',
        'PTS': 'PTS',
        'REBOUNDS': 'REB',
        'REBOUND': 'REB',
        'REB': 'REB',
        'ASSISTS': 'AST',
        'ASSIST': 'AST',
        'AST': 'AST',
        'STEALS': 'STL',
        'STEAL': 'STL',
        'STL': 'STL',
        'BLOCKS': 'BLK',
        'BLOCK': 'BLK',
        'BLK': 'BLK',
        'TURNOVERS': 'TO',
        'TURNOVER': 'TO',
        'TO': 'TO',
        'THREESMADE': '3PM',
        'THREEPOINTSMADE': '3PM',
        '3PM': '3PM',
        'THREES': '3PM',
        # MLB stats
        'HOMERUNS': 'HR',
        'HOMERUN': 'HR',
        'HR': 'HR',
        'HITS': 'H',
        'HIT': 'H',
        'H': 'H',
        'RBI': 'RBI',
        'RUNS': 'R',
        'RUN': 'R',
        'R': 'R',
        'STRIKEOUTS': 'K',
        'STRIKEOUT': 'K',
        'K': 'K',
        'WALKS': 'BB',
        'WALK': 'BB',
        'BB': 'BB',
        # NFL stats
        'PASSINGTOUCHDOWNS': 'PASSTD',
        'PASSTD': 'PASSTD',
        'PASSINGTD': 'PASSTD',
        'RUSHINGYARD': 'RUSHYD',
        'RUSHINGYARD': 'RUSHYD',
        'RUSHYD': 'RUSHYD'
    }
    
    # Handle special case: MLB events using "POINTS" should map to appropriate MLB stats
    if league == 'MLB' and original_stat == 'POINTS':
        # Default to hits for MLB when POINTS is used incorrectly
        corrected_stat = 'H'
        corrections.append(f"Fixed MLB stat type: 'POINTS' -> 'H' (MLB doesn't use points)")
    else:
        corrected_stat = stat_type_map.get(original_stat.upper(), original_stat.upper())
        if corrected_stat != original_stat.upper():
            corrections.append(f"Standardized stat type: '{original_stat}' -> '{corrected_stat}'")
    
    # Reconstruct the corrected event ID
    corrected_event_id = f"{date_part}-{league}-{clean_player}{threshold_part}{corrected_stat}"
    
    # Final validation check
    final_pattern = r'^\d{4}-\d{2}-\d{2}-[A-Z]+-[A-Z]+\d+\.\d+[A-Z]+$'
    if not re.match(final_pattern, corrected_event_id):
        corrections.append(f"Warning: Corrected event ID may still have format issues: {corrected_event_id}")
    
    if corrected_event_id != original_event_id:
        corrections.append(f"Final correction: '{original_event_id}' -> '{corrected_event_id}'")
    
    return corrected_event_id, corrections


def generate_admin_event_id(event_date, league, pick_type, team_a, team_b, player_name=None, stat_threshold=None, stat_type=None):
    """
    Generate consistent event IDs for admin form in the format: YYYY-MM-DD-LEAGUE-IDENTIFIER

    For player stat picks: YYYY-MM-DD-NBA-PLAYERNAME##.#STATTYPE
    For team matchups: YYYY-MM-DD-NBA-TEAMA-TEAMB
    """
    # Ensure all components are uppercase
    league = league.upper()

    # Check if we have player stat information (regardless of pick_type)
    if player_name and stat_threshold is not None and stat_type:
        # Player stat pick format: YYYY-MM-DD-NBA-PLAYERNAME##.#STATTYPE
        # Clean player name: remove spaces and periods, convert to uppercase (for event_id consistency)
        clean_player_name = player_name.strip().replace(" ", "").replace(".", "").upper()
        
        # Map stat types to standard abbreviations
        stat_type_map = {
            'points': 'PTS',
            'rebounds': 'REB', 
            'assists': 'AST',
            'steals': 'STL',
            'blocks': 'BLK',
            'turnovers': 'TO',
            'threes made': '3PM',
            'three pointers made': '3PM',
            'home runs': 'HR',
            'hits': 'H',
            'rbi': 'RBI',
            'runs': 'R',
            'strikeouts': 'K',
            'walks': 'BB'
        }
        
        # Get abbreviated stat type
        stat_key = stat_type.strip().lower()
        clean_stat_type_for_id = stat_type_map.get(stat_key, stat_type.strip().replace(" ", "").upper())

        # Format stat threshold consistently with decimal separator
        # Always use . as separator by formatting as float for all cases
        # This ensures consistent format: player_name.threshold.stat_type
        try:
            # Convert to float and format with exactly one decimal place
            # This ensures integer thresholds display as X.0 (e.g., 2 becomes 2.0)
            threshold_str = f"{float(stat_threshold):.1f}"
        except (ValueError, TypeError):
            # Fallback for non-numeric values
            threshold_str = str(stat_threshold)

        event_id = f"{event_date}-{league}-{clean_player_name}{threshold_str}{clean_stat_type_for_id}"
    else:
        clean_team_a = team_a.strip().replace(" ", "").upper()
        clean_team_b = team_b.strip().replace(" ", "").upper()
        # Enhanced handling for team totals and spreads so they don't collide with MoneyLine
        if pick_type in ["Total", "Spread"] and stat_threshold is not None:
            try:
                thresh_str = f"{abs(float(stat_threshold)):.1f}".rstrip('0').rstrip('.')
            except (ValueError, TypeError):
                thresh_str = str(stat_threshold)
            if pick_type == "Total":
                # Prefix with O or U based on stat_type if available
                over_under_prefix = "O" if (stat_type and str(stat_type).lower().startswith("over")) else "U"
                event_id = f"{event_date}-{league}-{clean_team_a}-{clean_team_b}-{over_under_prefix}{thresh_str}"
            else:  # Spread
                sign_prefix = "M" if float(stat_threshold) < 0 else "P"  # Minus / Plus
                event_id = f"{event_date}-{league}-{clean_team_a}-{clean_team_b}-{sign_prefix}{thresh_str}"
        else:
            # Default team matchup format (MoneyLine)
            event_id = f"{event_date}-{league}-{clean_team_a}-{clean_team_b}"

    return event_id

def delete_event(event_id: str):
    with get_connection() as conn:
        cursor = conn.cursor()
        cursor.execute("DELETE FROM expert_predictions WHERE event_id = %s", (event_id,))
        cursor.execute("DELETE FROM crowd_predictions WHERE event_id = %s", (event_id,))
        cursor.execute("DELETE FROM events WHERE event_id = %s", (event_id,))
    print(f"Deleted event {event_id} and all associated predictions.")

def fetch_events_by_date(event_date: str):
    with get_connection() as conn:
        cursor = conn.cursor()
        cursor.execute("""
            SELECT event_id, league, team_a, team_b, actual_result
            FROM events
            WHERE event_date = %s
        """, (event_date,))
        rows = cursor.fetchall()

    print(f"Events on {event_date}:")
    for row in rows:
        event_id, league, team_a, team_b, result = row
        result_text = "Occurred" if result == 1 else "Did Not Occur" if result == 0 else "Unknown"
        print(f" - [{league}] {team_a} vs {team_b} (Event ID: {event_id}) → {result_text}")

def fetch_predictions_by_expert(expert_name: str):
    with get_connection() as conn:
        cursor = conn.cursor()
        cursor.execute("""
            SELECT event_id, prediction, confidence, prediction_time
            FROM expert_predictions
            WHERE expert_name = %s
            ORDER BY prediction_time DESC
        """, (expert_name,))
        rows = cursor.fetchall()

    print(f"Predictions by {expert_name}:")
    for event_id, prediction, confidence, time in rows:
        pred_str = "Too Low" if prediction == 1 else "Too High"
        conf_str = f"{confidence:.2f}" if confidence is not None else "N/A"
        print(f" - [{event_id}] {pred_str} (Confidence: {conf_str}) at {time}")

def add_handicapper(
        capper_id: str, 
        capper_name: str, 
        twitter_handle: str, 
        insta_handle: str,
        tiktok_handle: str, 
        image_url: str,
        accuracy_score: float,
        units_per_month: float,
        capper_bio: str,
        capper_pricing: str,
        group_memberships: str,
        estimated_customers: int,
        followers: int,
        last_updated: str

        ):
    with get_connection() as conn:
        cursor = conn.cursor()
        cursor.execute("""
            INSERT IGNORE INTO handicappers (capper_id, capper_name, twitter_handle, instagram_handle, 
                       tiktok_handle, image_url, accuracy_score, units_per_month, capper_bio, 
                       capper_pricing, group_memberships, estimated_customers, followers, last_updated)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """, (capper_id, capper_name, twitter_handle, insta_handle, 
                tiktok_handle, image_url, accuracy_score, units_per_month, capper_bio, 
                capper_pricing, group_memberships, estimated_customers, followers, last_updated))
        
def search_by_name(capper_name: str):
    with get_connection() as conn:
        cursor = conn.cursor()
        cursor.execute("""
            SELECT capper_id, capper_name, twitter_handle, insta_handle, 
                tiktok_handle, image_url, accuracy_score, units_per_month, capper_bio, 
                capper_pricing, group_memberships, estimated_customers, followers, last_updated
            FROM handicappers
            WHERE capper_name = %s
            ORDER BY capper_name DESC
        """, (capper_name,))
        rows = cursor.fetchall()

def search_by_social_handle(insta_handle: str = None, twitter_handle: str = None, tiktok_handle: str = None):
    query = """
        SELECT capper_id, capper_name, twitter_handle, insta_handle, 
               tiktok_handle, image_url, accuracy_score, units_per_month, 
               capper_bio, capper_pricing, group_memberships, 
               estimated_customers, followers, last_updated
        FROM handicappers
        WHERE
    """
    params = []
    conditions = []

    if insta_handle:
        conditions.append("insta_handle = %s")
        params.append(insta_handle)

    if twitter_handle:
        conditions.append("twitter_handle = %s")
        params.append(twitter_handle)

    if tiktok_handle:
        conditions.append("tiktok_handle = %s")
        params.append(tiktok_handle)

    if not conditions:
        raise ValueError("At least one social media handle must be provided.")

    
    query += " OR ".join(conditions)

    with get_connection() as conn:
        cursor = conn.cursor()
        cursor.execute(query, tuple(params))
        return cursor.fetchall()


def submit_event(
    event_id: str,
    event_date: str,
    league: str,
    team_a: str,
    team_b: str,
    crowd_probability: float,
    expert_predictions: List[Tuple[str, int, Optional[float]]],
    actual_result: Optional[int] = None,
    pick_type: str = "MoneyLine",
    player_team: str = "None",
    stat_type: str = "MoneyLine",
    player_name: Optional[str] = None,
    stat_threshold: Optional[float] = None
) -> Tuple[bool, str]:
    try:
        with get_connection() as conn:
            cursor = conn.cursor()

            # Prevent duplicates
            cursor.execute("SELECT 1 FROM events WHERE event_id = %s", (event_id,))
            if cursor.fetchone():
                return False, f"Event '{event_id}' already exists."

            cursor.execute("""
                INSERT INTO events (event_id, event_date, league, team_a, team_b, actual_result, pick_type, player_team, stat_type, player_name, stat_threshold)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """, (event_id, event_date, league, team_a, team_b, actual_result, pick_type, player_team, stat_type, player_name, stat_threshold))

            cursor.execute("""
                INSERT INTO crowd_predictions (event_id, crowd_probability)
                VALUES (%s, %s)
                ON DUPLICATE KEY UPDATE crowd_probability = VALUES(crowd_probability)
            """, (event_id, crowd_probability))

            for expert_name, prediction, confidence in expert_predictions:
                # Prevent phantom ChartGeneration predictions
                if expert_name.lower() == "chartgeneration":
                    continue
                    
                cursor.execute("""
                    INSERT INTO expert_predictions (event_id, expert_name, prediction, confidence)
                    VALUES (%s, %s, %s, %s)
                    ON DUPLICATE KEY UPDATE 
                        prediction = VALUES(prediction),
                        confidence = VALUES(confidence)
                """, (event_id, expert_name, prediction, confidence))

                cursor.execute("SELECT 1 FROM expert_reliability WHERE expert_name = %s", (expert_name,))
                if not cursor.fetchone():
                    cursor.execute("INSERT INTO expert_reliability (expert_name) VALUES (%s)", (expert_name,))

        return True, "Event, crowd prediction, and expert predictions submitted successfully."
    except Exception as e:
        return False, f"Database error: {e}"


def fetch_nba_player_log(player_name: str, game_date: Optional[str] = None):
    with get_connection() as conn:
        cursor = conn.cursor()
        table_name = get_player_logs_table_name("NBA")

        if game_date:
            cursor.execute(f"""
                SELECT * FROM {table_name}
                WHERE player_name = %s AND game_date = %s
            """, (player_name, game_date))
        else:
            cursor.execute(f"""
                SELECT * FROM {table_name}
                WHERE player_name = %s
                ORDER BY game_date DESC
                LIMIT 1
            """, (player_name,))
        
        row = cursor.fetchone()

    if row:
        columns = [desc[0] for desc in cursor.description]
        print(f"\n🧾 Statline for {player_name}:")
        for col, val in zip(columns, row):
            print(f"{col}: {val}")
    else:
        print(f"⚠️ No statline found for {player_name}.")


def bulk_insert_sample_data(conn, sample_events, sample_predictions):
    cursor = conn.cursor()

    # Insert sample events
    event_query = """
        INSERT INTO events (event_id, player_name, league, game_date, stat_type, stat_threshold)
        VALUES (%s, %s, %s, %s, %s, %s)
        ON DUPLICATE KEY UPDATE player_name=VALUES(player_name)
    """
    for event in sample_events:
        cursor.execute(event_query, (
            event["event_id"],
            event["player_name"],
            event["league"],
            event["game_date"],
            event["stat_type"],
            event["stat_threshold"]
        ))

    # Insert sample expert predictions
    prediction_query = """
        INSERT INTO expert_predictions (event_id, expert_name, prediction, confidence)
        VALUES (%s, %s, %s, %s)
        ON DUPLICATE KEY UPDATE confidence=VALUES(confidence)
    """
    for pred in sample_predictions:
        cursor.execute(prediction_query, (
            pred["event_id"],
            pred["expert_name"],
            pred["prediction"],
            pred["confidence"]
        ))

    conn.commit()
    print("✅ Sample events and predictions inserted.")

import random
from datetime import datetime, timedelta
import pymysql
import os

def generate_and_insert_sample_data():
    print("Accessing Database")
    conn = get_connection()

    sample_events = []
    sample_predictions = []

    player_pool = ["Tyler Herro", "Bam Adebayo", "Jayson Tatum", "Jaylen Brown", "Davion Mitchell"]
    leagues = ["NBA"]
    stat_types = ["Points", "Assists", "Rebounds", "Steals", "Blocks"]
    expert_pool = ["ExpertA", "ExpertB", "ExpertC", "ExpertD", "ExpertE"]

    base_date = datetime.today() - timedelta(days=30)

    for i in range(500):
        player_name = random.choice(player_pool)
        league = random.choice(leagues)
        stat_type = random.choice(stat_types)
        stat_threshold = round(random.uniform(5, 30), 1)
        event_date = (base_date + timedelta(days=i % 15)).date()
        event_date_str = event_date.isoformat()

        team_a = "MIA"
        team_b = "BOS"

        event_id = f"{event_date_str}-{league}-{player_name.replace(' ', '').upper()}{stat_threshold:.1f}{stat_type.upper()}"

        event = {
            "event_id": event_id,
            "player_name": player_name,
            "league": league,
            "event_date": event_date_str,
            "stat_type": stat_type,
            "stat_threshold": stat_threshold,
            "team_a": team_a,
            "team_b": team_b
        }

        sample_events.append(event)

        for expert in expert_pool:
            prediction = random.choice([0, 1])
            confidence = round(random.uniform(0.5, 1.0), 2)


            sample_predictions.append({
                "event_id": event_id,
                "expert_name": expert,
                "prediction": prediction,
                "confidence": confidence
            })

    bulk_insert_sample_data(conn, sample_events, sample_predictions)
    conn.close()

def bulk_insert_sample_data(conn, sample_events, sample_predictions):
    cursor = conn.cursor()

    event_query = """
        INSERT INTO events (
            event_id, player_name, league, event_date,
            stat_type, stat_threshold, team_a, team_b
        )
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
        ON DUPLICATE KEY UPDATE
            player_name = VALUES(player_name)
    """

    prediction_query = """
        INSERT INTO expert_predictions (
            event_id, expert_name, prediction, confidence
        )
        VALUES (%s, %s, %s, %s)
        ON DUPLICATE KEY UPDATE 
            prediction = VALUES(prediction),
            confidence = VALUES(confidence)
    """

    for event in sample_events:
        cursor.execute(event_query, (
            event["event_id"],
            event["player_name"],
            event["league"],
            event["event_date"],
            event["stat_type"],
            event["stat_threshold"],
            event["team_a"],
            event["team_b"]
        ))

    for pred in sample_predictions:
        cursor.execute(prediction_query, (
            pred["event_id"],
            pred["expert_name"],
            pred["prediction"],
            pred["confidence"]
        ))

    conn.commit()
    print(f"✅ Inserted {len(sample_events)} events and {len(sample_predictions)} predictions.")

def get_player_team(event_id):
    # Connect to the MySQL database
    conn = get_connection()
    
    cursor = conn.cursor()
    
    # Query to get the team based on event_id
    query = """
        SELECT player_team
        FROM events
        WHERE event_id = %s
    """
    
    # Execute the query with the provided event_id
    cursor.execute(query, (event_id,))
    
    # Fetch the result
    result = cursor.fetchone()

    # Close the database connection
    cursor.close()
    conn.close()
    
    # If result is found, return the team
    if result:
        return result[0]  # team is in the first column
    else:
        return None  # If no result found for the event_id



def update_game_date_league_team_for_predictions():
    # Connect to the MySQL database
    conn = get_connection()
    cursor = conn.cursor()
    
    # Query to fetch all event_ids
    cursor.execute("SELECT event_id FROM expert_predictions")
    event_ids = cursor.fetchall()
    
    # Loop through each event_id and update game_date, league, and team
    for event_id in event_ids:
        # Extract game_date from event_id (first part before the first hyphen)
        event_parts = event_id[0].split('-')
        game_date = '-'.join(event_parts[:3])  # Extract 'YYYY-MM-DD' part
        league = event_parts[3]  # Extract league (4th part)
        team = get_player_team(event_id)
        
        # Update query to set game_date, league, and team based on event_id
        update_query = """
            UPDATE expert_predictions
            SET game_date = %s, league = %s, team = %s
            WHERE event_id = %s
        """
        cursor.execute(update_query, (game_date, league, team, event_id[0]))
    
    # Commit the changes to the database
    conn.commit()
    
    # Close the database connection
    cursor.close()
    conn.close()
    
    print("Game dates, leagues, and teams updated successfully!")


#fetch_nba_player_log("K. Towns","2024-10-22")
#print("Accessing Database")
# update_game_date_league_team_for_predictions()


def insert_lebron_event_prediction():
    conn = get_connection()
    cursor = conn.cursor()

    event_id = "2024-10-22-NBA-KTOWNS14.0PTS"
    event_date = "2024-10-22"
    league = "NBA"
    team_a = "Over"
    team_b = "Under"
    player_name = "K. Towns"
    stat_type = "points"
    stat_threshold = 14.0
    pick_type = "Prop"
    player_team = "New York Knicks"

    # === Insert into events ===
    cursor.execute("""
        INSERT INTO events (
            event_id, event_date, league, team_a, team_b,
            actual_result, context_features, pick_type,
            player_team, stat_type, player_name, stat_threshold
        ) VALUES (
            %s, %s, %s, %s, %s,
            NULL, NULL, %s,
            %s, %s, %s, %s
        )
        ON DUPLICATE KEY UPDATE player_name = VALUES(player_name)
    """, (
        event_id, event_date, league, team_a, team_b,
        pick_type, player_team, stat_type, player_name, stat_threshold
    ))

    # === Insert into crowd_predictions ===
    crowd_prob = round(random.uniform(0.45, 0.7), 4)
    cursor.execute("""
        INSERT INTO crowd_predictions (event_id, crowd_probability)
        VALUES (%s, %s)
        ON DUPLICATE KEY UPDATE crowd_probability = VALUES(crowd_probability)
    """, (event_id, crowd_prob))

    # === Insert expert_predictions ===
    experts = {
        "Expert A": 0.90,
        "Expert B": 0.80,
        "Expert C": 0.75,
        "Expert D": 0.70,
        "Expert E": 0.55,
    }

    for expert, accuracy in experts.items():
        # Simulate prediction based on accuracy
        prediction = int(random.random() < accuracy)
        confidence = round(random.uniform(0.6, 1.0), 3)
        cursor.execute("""
            INSERT INTO expert_predictions (
                event_id, expert_name, prediction, confidence, stat_threshold
            ) VALUES (%s, %s, %s, %s, %s)
            ON DUPLICATE KEY UPDATE 
                prediction = VALUES(prediction),
                confidence = VALUES(confidence),
                stat_threshold = VALUES(stat_threshold)
        """, (event_id, expert, prediction, confidence, stat_threshold))

    conn.close()
    print(f"✅ Inserted simulated predictions for event: {event_id}")

#insert_lebron_event_prediction()


def get_player_and_stat_type(event_id):
    # Connect to the MySQL database
    conn =get_connection()
    
    cursor = conn.cursor()
    
    # Query to select player_name and stat_type based on event_id
    query = """
        SELECT player_name, stat_type
        FROM events
        WHERE event_id = %s
    """
    
    # Execute the query
    cursor.execute(query, (event_id,))
    
    # Fetch the result
    result = cursor.fetchone()
    
    # Close the database connection
    cursor.close()
    conn.close()
    
    # Return the result (player_name and stat_type)
    if result:
        return result
    else:
        return None  # Return None if no result is found
    

# event_id = '2024-10-22-NBA-JBRUNSON21.5PTS'
# result = get_player_and_stat_type(event_id)
# if result:
#     print(f"Player Name: {result[0]}, Stat Type: {result[1]}")
# else:
#     print("No data found for the given event_id.")

def get_todays_events_with_handicappers(target_date=None):
    from datetime import datetime
    # Import lightweight normalizer to guarantee PT output for prediction_time
    try:
        from services.data_validation import _normalize_prediction_time as _norm_pred_time
    except Exception:
        _norm_pred_time = None  # Fallback if unavailable; return raw values
    date_str = target_date or datetime.now().strftime("%Y-%m-%d")
    results = []
    class EventObject:
        def __init__(self, event_id, event_date, player_name, event_team, handicappers, stat_threshold=None, pick_type=None, team_a=None, team_b=None, stat_type=None, predictions=None, confidence_values=None, prediction_time=None, expert_predictions_map=None):
            self.event_id = event_id
            self.event_date = event_date
            self.player_name = player_name
            self.event_team = event_team
            self.handicappers = handicappers
            self.stat_threshold = stat_threshold
            self.pick_type = pick_type
            self.team_a = team_a
            self.team_b = team_b
            self.stat_type = stat_type
            self.predictions = predictions or []
            self.confidence_values = confidence_values or []
            self.prediction_time = prediction_time
            self.expert_predictions_map = expert_predictions_map or {}
        def to_dict(self):
            return {
                "event_id": self.event_id,
                "event_date": self.event_date,
                "player_name": self.player_name,
                "event_team": self.event_team,
                "handicappers": self.handicappers,
                "stat_threshold": self.stat_threshold,
                "pick_type": self.pick_type,
                "team_a": self.team_a,
                "team_b": self.team_b,
                "stat_type": self.stat_type,
                "predictions": self.predictions,
                "confidence_values": self.confidence_values,
                "prediction_time": self.prediction_time,
                "expert_predictions_map": self.expert_predictions_map
            }
    conn = get_dict_connection()
    with conn:
        cursor = conn.cursor()
        join_query = (
            """
            SELECT e.event_id, e.event_date, e.team_a AS event_team,
                   ep.player_name, e.stat_threshold, e.pick_type, e.team_a, e.team_b, e.stat_type,
                   GROUP_CONCAT(DISTINCT h.name) AS handicappers,
                   MIN(expert_pred.prediction_time) AS prediction_time
            FROM events e
            LEFT JOIN event_picks ep ON e.event_id = ep.event_id
            LEFT JOIN handicappers h ON ep.handicapper_id = h.id
            LEFT JOIN expert_predictions expert_pred ON e.event_id = expert_pred.event_id
            WHERE e.event_date = %s
            GROUP BY e.event_id, ep.player_name, e.team_a, e.stat_threshold, e.pick_type, e.team_b, e.stat_type
            """
        )

        simple_query = (
            """
            SELECT e.event_id, e.event_date, e.player_name, e.team_a AS event_team,
                   e.stat_threshold, e.pick_type, e.team_a, e.team_b, e.stat_type,
                   MIN(ep.prediction_time) AS prediction_time
            FROM events e
            LEFT JOIN expert_predictions ep ON e.event_id = ep.event_id
            WHERE e.event_date = %s
            GROUP BY e.event_id, e.player_name, e.team_a, e.stat_threshold, e.pick_type, e.team_b, e.stat_type
            """
        )

        try:
            cursor.execute(join_query, (date_str,))
            rows = cursor.fetchall()
        except Exception as db_err:
            # If the join table doesn't exist (errno 1146) or any other error occurs,
            # gracefully fall back to a simplified query so the API call still works.
            if hasattr(db_err, "args") and len(db_err.args) > 0 and db_err.args[0] == 1146:
                # Missing table, use fallback
                cursor.execute(simple_query, (date_str,))
                rows = cursor.fetchall()
            else:
                raise

        for row in rows:
            handicappers = row.get("handicappers") if "handicappers" in row else None
            if handicappers:
                handicapper_list = [h.strip() for h in handicappers.split(",") if h.strip()]
            else:
                cursor.execute("SELECT DISTINCT expert_name FROM expert_predictions WHERE event_id = %s", (row["event_id"],))
                expert_rows = cursor.fetchall()
                handicapper_list = [er["expert_name"] for er in expert_rows]
            
            cursor.execute("SELECT expert_name, prediction, confidence FROM expert_predictions WHERE event_id = %s", (row["event_id"],))
            prediction_rows = cursor.fetchall()
            predictions_list = [pr["prediction"] for pr in prediction_rows if pr["prediction"] is not None]
            confidence_list = [pr["confidence"] for pr in prediction_rows if pr["confidence"] is not None]
            expert_predictions_map = {pr["expert_name"]: pr["prediction"] for pr in prediction_rows if pr["prediction"] is not None}
            
            # Ensure prediction_time is consistently returned in Pacific Time
            raw_pred_time = row.get("prediction_time")
            normalized_pred_time = None
            if raw_pred_time:
                if _norm_pred_time is not None:
                    try:
                        normalized_pred_time = _norm_pred_time(str(raw_pred_time), row.get("event_date"))
                    except Exception:
                        normalized_pred_time = str(raw_pred_time)
                else:
                    normalized_pred_time = str(raw_pred_time)

            obj = EventObject(
                event_id=row["event_id"],
                event_date=row["event_date"],
                player_name=row.get("player_name"),
                event_team=row.get("event_team"),
                handicappers=handicapper_list,
                stat_threshold=row.get("stat_threshold"),
                pick_type=row.get("pick_type"),
                team_a=row.get("team_a"),
                team_b=row.get("team_b"),
                stat_type=row.get("stat_type"),
                predictions=predictions_list,
                confidence_values=confidence_list,
                prediction_time=normalized_pred_time,
                expert_predictions_map=expert_predictions_map
            )
            results.append(obj)
    return results

def generate_chart_from_historical_data(event_id: str) -> dict:
    """
    Generate player performance chart directly from historical data when MainModel fails.
    This approach generates KDE distributions and saves them using the existing kde_distributions_port
    system, then uses PlotUtils to generate the chart consistently.
    
    Args:
        event_id: Event ID in format YYYY-MM-DD-LEAGUE-PLAYERNAME##.#STATTYPE
        
    Returns:
        dict: Chart data with base64 encoded image and metadata
    """
    import re
    import numpy as np
    from scipy.stats import gaussian_kde
    from datetime import datetime
    
    try:
        print(f"🔄 Generating distribution data from historical stats for {event_id}")
        
        # Parse event ID to extract components
        pattern = r'^(\d{4}-\d{2}-\d{2})-([A-Z]+)-(.+?)(\d+(?:\.\d+)?)([A-Z]+)$'
        match = re.match(pattern, event_id)
        
        if not match:
            raise ValueError(f"Invalid event ID format: {event_id}")
        
        date_part, league, player_name, threshold_str, stat_type = match.groups()
        threshold = float(threshold_str)
        
        print(f"📊 Parsed: Player={player_name}, League={league}, Stat={stat_type}, Threshold={threshold}")
        
        # Check if this is actually a team name rather than a player name
        team_names = {
            'MLB': ['ORIOLES', 'REDSOX', 'YANKEES', 'RAYS', 'BLUEJAYS', 'WHITESOX', 'GUARDIANS', 'TIGERS', 'ROYALS', 'TWINS', 'ASTROS', 'ANGELS', 'ATHLETICS', 'MARINERS', 'RANGERS', 'BRAVES', 'MARLINS', 'METS', 'PHILLIES', 'NATIONALS', 'CUBS', 'REDS', 'BREWERS', 'PIRATES', 'CARDINALS', 'DIAMONDBACKS', 'ROCKIES', 'DODGERS', 'PADRES', 'GIANTS'],
            'NBA': ['HAWKS', 'CELTICS', 'NETS', 'HORNETS', 'BULLS', 'CAVALIERS', 'MAVERICKS', 'NUGGETS', 'PISTONS', 'WARRIORS', 'ROCKETS', 'PACERS', 'CLIPPERS', 'LAKERS', 'GRIZZLIES', 'HEAT', 'BUCKS', 'TIMBERWOLVES', 'PELICANS', 'KNICKS', 'THUNDER', 'MAGIC', 'SIXERS', 'SUNS', 'BLAZERS', 'KINGS', 'SPURS', 'RAPTORS', 'JAZZ', 'WIZARDS'],
            'NFL': ['BILLS', 'DOLPHINS', 'PATRIOTS', 'JETS', 'RAVENS', 'BENGALS', 'BROWNS', 'STEELERS', 'TEXANS', 'COLTS', 'JAGUARS', 'TITANS', 'BRONCOS', 'CHIEFS', 'RAIDERS', 'CHARGERS', 'COWBOYS', 'GIANTS', 'EAGLES', 'COMMANDERS', 'BEARS', 'LIONS', 'PACKERS', 'VIKINGS', 'FALCONS', 'PANTHERS', 'SAINTS', 'BUCCANEERS', 'CARDINALS', 'RAMS', 'FORTYNINERS', 'SEAHAWKS'],
            'NHL': ['BRUINS', 'SABRES', 'REDWINGS', 'PANTHERS', 'CANADIENS', 'SENATORS', 'LIGHTNING', 'MAPLELEAFS', 'HURRICANES', 'BLUEJACKETS', 'DEVILS', 'ISLANDERS', 'RANGERS', 'FLYERS', 'PENGUINS', 'CAPITALS', 'BLACKHAWKS', 'AVALANCHE', 'STARS', 'WILD', 'PREDATORS', 'BLUES', 'JETS', 'FLAMES', 'OILERS', 'CANUCKS', 'DUCKS', 'SHARKS', 'KINGS', 'KNIGHTS', 'COYOTES', 'KRAKEN']
        }
        
        if player_name.upper() in team_names.get(league, []):
            print(f"⚠️ Detected team name ({player_name}) instead of player name - skipping chart generation")
            return generate_placeholder_chart_data(event_id, f"Chart generation not available for team-based events ({player_name})")
        
        # Get historical player data
        historical_data = fetch_player_historical_stats(player_name, league, stat_type, date_part)
        
        if not historical_data or len(historical_data) < 5:
            print(f"⚠️ Insufficient historical data for {player_name} ({len(historical_data)} games)")
            return generate_placeholder_chart_data(event_id, "Insufficient historical data")
        
        print(f"✅ Found {len(historical_data)} historical games for {player_name}")
        
        # Generate KDE distribution
        stat_values = np.array(historical_data)
        
        # Create KDE with appropriate bandwidth
        kde = gaussian_kde(stat_values)
        kde.set_bandwidth(kde.factor * 0.8)  # Slightly smoother
        
        # Generate x values for distribution data
        x_min = max(0, min(stat_values) - 2)
        x_max = max(stat_values) + 2
        x_range = np.linspace(x_min, x_max, 200)
        
        # Calculate PDF and CDF
        pdf_values = kde(x_range)
        cdf_values = np.array([kde.integrate_box_1d(-np.inf, x) for x in x_range])
        
        # Create distribution data in the format expected by kde_distributions_port
        pdf_dict = {str(x): float(y) for x, y in zip(x_range, pdf_values)}
        cdf_dict = {str(x): float(y) for x, y in zip(x_range, cdf_values)}
        
        # Create odds-based distributions (simplified for now)
        # In a full implementation, these would be calculated from betting odds
        odds_pdf_dict = pdf_dict.copy()  # Placeholder
        odds_cdf_dict = cdf_dict.copy()  # Placeholder
        
        # Save the distribution data using the existing kde_distributions_port system
        from data_science_modules.kde_distributions_port import import_distribution
        
        print(f"💾 Saving distribution data for {event_id}")
        import_distribution(
            event_id=event_id,
            player_name=player_name,
            stat_type=stat_type,
            game_date=date_part,
            pdf_json=pdf_dict,
            cdf_json=cdf_dict,
            odds_pdf_json=odds_pdf_dict,
            odds_cdf_json=odds_cdf_dict
        )
        
        print(f"✅ Distribution data saved for {event_id}")
        
        # Now use the existing PlotUtils to generate the chart
        from modules.HandiCapperAccuracyModel.util.PlotUtils import PlotUtils
        plotter = PlotUtils()
        chart_base64 = plotter.unpack_distribution_data(event_id)
        
        # Calculate some stats for the response
        mean_val = np.mean(stat_values)
        prob_over = 1 - kde.integrate_box_1d(-np.inf, threshold)
        prob_under = kde.integrate_box_1d(-np.inf, threshold)
        
        return {
            "success": True,
            "chart_data": chart_base64,
            "data_source": "historical_data_saved",
            "stats": {
                "games_analyzed": len(historical_data),
                "mean": float(mean_val),
                "median": float(np.median(stat_values)),
                "std_dev": float(np.std(stat_values)),
                "prob_over": float(prob_over),
                "prob_under": float(prob_under)
            }
        }
        
    except Exception as e:
        print(f"❌ Error generating chart from historical data: {e}")
        import traceback
        traceback.print_exc()
        return generate_placeholder_chart_data(event_id, f"Error: {str(e)}")


def fetch_player_historical_stats(player_name: str, league: str, stat_type: str, game_date: str) -> list:
    """
    Fetch historical player statistics for chart generation.
    
    Args:
        player_name: Player name (cleaned, no spaces/periods)
        league: League (NBA, MLB, NFL, NHL)
        stat_type: Stat type abbreviation (PTS, REB, HR, etc.)
        game_date: Game date to exclude future games
        
    Returns:
        list: List of stat values from historical games
    """
    try:
        conn = get_connection()
        cursor = conn.cursor()
        
        # Get the correct table name for the league
        table_name = get_player_logs_table_name(league)
        
        # Map stat types to database column names
        stat_column_map = {
            # NBA stats
            'PTS': 'points',
            'REB': 'rebounds', 
            'AST': 'assists',
            'STL': 'steals',
            'BLK': 'blocks',
            'TO': 'turnovers',
            '3PM': 'threes_made',
            # MLB stats
            'HR': 'home_runs',
            'H': 'hits',
            'RBI': 'rbi',
            'R': 'runs',
            'K': 'strikeouts',
            'BB': 'walks',
            # NFL stats (if applicable)
            'PASSTD': 'passing_touchdowns',
            'RUSHYD': 'rushing_yards',
            'RECYD': 'receiving_yards'
        }
        
        column_name = stat_column_map.get(stat_type.upper())
        if not column_name:
            print(f"⚠️ Unknown stat type: {stat_type}")
            return []
        
        # Query for historical data (last 10 games before the target date)
        query = f"""
            SELECT {column_name}
            FROM {table_name}
            WHERE player_name LIKE %s 
            AND game_date < %s
            AND {column_name} IS NOT NULL
            ORDER BY game_date DESC
            LIMIT 50
        """
        
        # Use LIKE with wildcards to handle name variations
        # Try multiple patterns to handle different name formats
        patterns_to_try = [
            f"%{player_name}%",  # Exact match with wildcards
            f"%{player_name.replace('.', '')}%",  # Without periods
            f"%{player_name.replace('.', '. ')}%",  # With space after period
        ]
        
        print(f"🔍 Searching for player: '{player_name}'")
        print(f"📊 Looking for stat: '{stat_type}' -> column: '{column_name}'")
        print(f"🗓️ Before date: {game_date}")
        print(f"📋 Table: {table_name}")
        
        rows = []
        for pattern in patterns_to_try:
            print(f"🔍 Trying pattern: '{pattern}'")
            cursor.execute(query, (pattern, game_date))
            rows = cursor.fetchall()
            if rows:
                print(f"✅ Found {len(rows)} rows with pattern: '{pattern}'")
                break
        
        if not rows:
            print(f"❌ No data found with any pattern for player: '{player_name}'")
        
        print(f"🔢 Raw query returned {len(rows)} rows")
        
        # Extract stat values
        stat_values = [row[0] for row in rows if row[0] is not None and row[0] >= 0]
        
        print(f"📈 Found {len(stat_values)} valid {stat_type} values for {player_name}")
        if stat_values:
            print(f"📊 Sample values: {stat_values[:5]}...")
        
        conn.close()
        
        return stat_values
        
    except Exception as e:
        print(f"❌ Error fetching historical stats for {player_name}: {e}")
        return []


def generate_placeholder_chart_data(event_id: str, message: str = "No data available") -> dict:
    """
    Generate a placeholder chart when no historical data is available.
    
    Args:
        event_id: Event ID for context
        message: Message to display on chart
        
    Returns:
        dict: Chart data with base64 encoded placeholder image
    """
    import matplotlib
    matplotlib.use('Agg')  # Use non-GUI backend for web server
    import matplotlib.pyplot as plt
    import numpy as np
    import io
    import base64
    import re
    
    try:
        # Parse event ID for context
        pattern = r'^(\d{4}-\d{2}-\d{2})-([A-Z]+)-(.+?)(\d+(?:\.\d+)?)([A-Z]+)$'
        match = re.match(pattern, event_id)
        
        if match:
            date_part, league, player_name, threshold_str, stat_type = match.groups()
            threshold = float(threshold_str)
            title = f"{player_name} - {stat_type} Distribution"
            subtitle = f"Threshold: {threshold} | League: {league} | Date: {date_part}"
        else:
            title = "Player Performance Distribution"
            subtitle = f"Event ID: {event_id}"
            threshold = 5.0
        
        # Create placeholder chart
        fig, ax = plt.subplots(figsize=(10, 6))
        
        # Generate sample distribution for visualization
        x = np.linspace(0, threshold * 2, 100)
        y = np.exp(-(x - threshold)**2 / (2 * (threshold/3)**2)) / np.sqrt(2 * np.pi * (threshold/3)**2)
        
        ax.plot(x, y, color='gray', linewidth=2, alpha=0.5, linestyle='--')
        ax.fill_between(x, y, alpha=0.2, color='gray')
        
        # Add threshold line
        ax.axvline(threshold, color='red', linestyle='-', linewidth=2, 
                  label=f'Threshold: {threshold}')
        ax.legend()
        
        ax.set_xlabel(stat_type if match else 'Stat Value')
        ax.set_ylabel('Probability Density')
        ax.set_title(title)
        ax.text(0.5, 0.95, subtitle, transform=ax.transAxes, ha='center', va='top', 
                fontsize=10, alpha=0.7)
        
        # Add message
        ax.text(0.5, 0.5, message, transform=ax.transAxes, ha='center', va='center', 
                fontsize=12, alpha=0.7, 
                bbox=dict(boxstyle="round,pad=0.5", facecolor="lightyellow", alpha=0.8))
        
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        
        # Convert to base64
        buf = io.BytesIO()
        plt.savefig(buf, format='png', dpi=150, bbox_inches='tight')
        plt.close(fig)
        buf.seek(0)
        
        chart_base64 = base64.b64encode(buf.read()).decode('utf-8')
        
        return {
            "success": True,
            "chart_data": chart_base64,
            "data_source": "placeholder",
            "message": message
        }
        
    except Exception as e:
        print(f"❌ Error generating placeholder chart: {e}")
        return {
            "success": False,
            "chart_data": None,
            "message": f"Error generating chart: {str(e)}"
        }

def debug_player_search(player_name: str, league: str = "NBA") -> list:
    """
    Debug function to see what players exist in the database that might match the search.
    """
    try:
        conn = get_connection()
        cursor = conn.cursor()
        
        table_name = get_player_logs_table_name(league)
        
        # Search for similar player names
        player_pattern = f"%{player_name}%"
        
        query = f"""
            SELECT DISTINCT player_name, COUNT(*) as game_count
            FROM {table_name}
            WHERE player_name LIKE %s
            GROUP BY player_name
            ORDER BY game_count DESC
            LIMIT 10
        """
        
        cursor.execute(query, (player_pattern,))
        rows = cursor.fetchall()
        
        print(f"🔍 Players matching '{player_name}' in {table_name}:")
        for row in rows:
            print(f"  - {row[0]} ({row[1]} games)")
        
        conn.close()
        return [row[0] for row in rows]
        
    except Exception as e:
        print(f"❌ Error searching for players: {e}")
        return []

def get_sample_players(league: str = "NBA", limit: int = 10) -> list:
    """
    Get a sample of players from the database to see what data is available.
    """
    try:
        conn = get_connection()
        cursor = conn.cursor()
        
        table_name = get_player_logs_table_name(league)
        
        query = f"""
            SELECT DISTINCT player_name, COUNT(*) as game_count
            FROM {table_name}
            GROUP BY player_name
            ORDER BY game_count DESC
            LIMIT %s
        """
        
        cursor.execute(query, (limit,))
        rows = cursor.fetchall()
        
        print(f"📊 Sample players in {table_name}:")
        for row in rows:
            print(f"  - {row[0]} ({row[1]} games)")
        
        conn.close()
        return [row[0] for row in rows]
        
    except Exception as e:
        print(f"❌ Error getting sample players: {e}")
        return []