#!/usr/bin/env bash
set -euo pipefail

# Browse AI API base
BASE_URL="https://api.browse.ai/v2"

# API Key: use env var if set, otherwise fallback to provided key
API_KEY="${BROWSEAI_API_KEY:-ce67ec88-ebb5-488e-9424-eef38a460332:5336b06f-a065-4371-98ff-c4e06f591e6c}"

# Robot IDs
ROBOT_IDS=(
  "01982a42-254c-7458-9eeb-b242ae1fddf5" # Sports Corner Daily
  "5590769e-e02b-4a64-be70-296d76b8f396" # Gamescript AI MLB
  "01982ecf-f63b-7a1d-9430-cec30971dbb9" # Gamescript AI NFL
  "0198623c-0f25-773d-b283-b2a648aa2088" # Hammering Hank
  "0198784a-584b-7a99-8abd-c5d3aa558571" # DanGamble AI
  "0198258d-2d7b-7219-bddd-583c78b0f3cc" # dougspicks
)

run_robot() {
  local robot_id="$1"
  echo "Triggering robot ${robot_id}..."
  resp=$(curl -sS -X POST \
    "${BASE_URL}/robots/${robot_id}/tasks" \
    -H "Authorization: Bearer ${API_KEY}" \
    -H "Content-Type: application/json" \
    -d '{}' \
  )
  echo "Response for ${robot_id}: ${resp}"
}

main() {
  for rid in "${ROBOT_IDS[@]}"; do
    run_robot "$rid"
  done
}

main "$@"