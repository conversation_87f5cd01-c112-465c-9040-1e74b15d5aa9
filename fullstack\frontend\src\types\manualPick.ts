export interface ExpertConfidence {
  name: string;
  confidence: number;
  team?: string;
  prediction_time?: string;
}

export interface ManualPick {
  name: string;
  odds: number;
  league: string[];
  pick_origin: ExpertConfidence[];
  reusable: boolean;
  capital_limit: number;
  mutual_exclusion: number;
  pick_type: "Prop" | "Custom" | "__CUSTOM__";
  player_team: string;
  stat_type: string | "__CUSTOM__";
  prediction: 1 | 0 | "__CUSTOM__";
  admin_password?: string;
  player_name?: string;
  stat_threshold?: number;
  eventDate?: string; // Added for the new feature
  team_a?: string; // Team A for the event
  team_b?: string; // Team B for the event
}

export interface DatabaseEvent {
  event_id: string;
  event_date: string;
  league: string;
  team_a: string;
  team_b: string;
  actual_result?: number;
  context_features?: any;
  pick_type: string;
  player_team: string;
  stat_type: string;
  player_name?: string;
  stat_threshold?: number;
}

export type StatType =
  | "Points"
  | "Assists"
  | "Rebounds"
  | "PRA"
  | "Blocks"
  | "Steals"
  | "Turnovers"
  | "3-pt"
  | string;

export type OddsFormat = "American" | "Decimal";

export type OddsSign = "positive" | "negative";

export interface OddsInputData {
  odds: number;
  format: OddsFormat;
  sign: OddsSign;
  source_name: string;
}

export interface OddsInputProps {
  odds: number;
  format?: OddsFormat;
  sign?: OddsSign;
  source_name?: string;
  onChange: (data: OddsInputData) => void;
}

export interface StatTypeSelectorProps {
  availableTypes: StatType[];
  selectedType: StatType;
  onSelect: (type: StatType) => void;
  allowCustom?: boolean;
}
