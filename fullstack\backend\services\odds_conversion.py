"""
Odds Conversion Utility Service

This module provides functions to convert sports betting odds to probability values.
Supports American odds format (positive and negative) with fallback handling.
"""

import logging

logger = logging.getLogger(__name__)


def convert_odds_to_probability(odds_string: str) -> float:
    """
    Convert American odds format to probability.
    
    Args:
        odds_string (str): Odds in American format (e.g., "+150", "-110")
        
    Returns:
        float: Probability value between 0.0 and 1.0, rounded to 4 decimal places
        
    Examples:
        convert_odds_to_probability("+150") -> 0.4000 (40%)
        convert_odds_to_probability("-110") -> 0.5238 (52.38%)
        convert_odds_to_probability("invalid") -> 0.5000 (50% fallback)
    """
    if not odds_string:
        logger.warning("Empty odds string provided, returning default 50%")
        return 0.5
    
    odds_string = odds_string.strip()
    
    try:
        if odds_string.startswith('+'):
            # Positive odds: +150 -> 100/(150+100) = 0.4
            odds_value = float(odds_string[1:])
            if odds_value <= 0:
                raise ValueError("Positive odds value must be greater than 0")
            probability = 100 / (odds_value + 100)
            
        elif odds_string.startswith('-'):
            # Negative odds: -110 -> 110/(110+100) = 0.524
            odds_value = float(odds_string[1:])
            if odds_value <= 0:
                raise ValueError("Negative odds value must be greater than 0")
            probability = odds_value / (odds_value + 100)
            
        else:
            # No sign prefix - invalid format
            logger.warning(f"Invalid odds format (no +/- prefix): '{odds_string}', returning default 50%")
            return 0.5
            
        # Round to 4 decimal places and ensure within valid range
        probability = round(probability, 4)
        
        # Sanity check - probability should be between 0 and 1
        if not (0 < probability < 1):
            logger.warning(f"Calculated probability {probability} outside valid range for odds '{odds_string}', returning default 50%")
            return 0.5
            
        return probability
        
    except (ValueError, TypeError) as e:
        logger.warning(f"Error converting odds '{odds_string}' to probability: {e}, returning default 50%")
        return 0.5
    except Exception as e:
        logger.error(f"Unexpected error converting odds '{odds_string}': {e}, returning default 50%")
        return 0.5


def convert_probability_to_odds(probability: float, format_type: str = "american") -> str:
    """
    Convert probability to odds format (utility function for future use).
    
    Args:
        probability (float): Probability value between 0.0 and 1.0
        format_type (str): Output format ("american" only supported currently)
        
    Returns:
        str: Formatted odds string
        
    Examples:
        convert_probability_to_odds(0.4) -> "+150"
        convert_probability_to_odds(0.6) -> "-150"
    """
    if not (0 < probability < 1):
        raise ValueError("Probability must be between 0 and 1")
    
    if format_type != "american":
        raise ValueError("Only 'american' format is currently supported")
    
    if probability >= 0.5:
        # Negative odds for favorites
        odds_value = (probability * 100) / (100 - probability * 100)
        return f"-{round(odds_value)}"
    else:
        # Positive odds for underdogs
        odds_value = (100 - probability * 100) / (probability * 100)
        return f"+{round(odds_value)}"