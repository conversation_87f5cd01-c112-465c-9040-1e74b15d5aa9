# fullstack/backend/services/auth.py
from functools import wraps
from flask import request, jsonify
import os


def validate_api_key(api_key: str) -> bool:
    """
    Validates the provided API key against the environment variable.
    Mirrors behavior from app.py to ensure zero behavior change.
    """
    expected_key = os.getenv("EXTERNAL_API_KEY")
    if not expected_key:
        print("[ERROR] EXTERNAL_API_KEY not configured in environment variables")
        return False

    is_valid = api_key == expected_key
    if not is_valid:
        print(f"[WARNING] Invalid API key attempt from {request.remote_addr if request else 'unknown'}")
    return is_valid


def require_api_key(f):
    """
    Decorator to require API key authentication for endpoints.
    Expects X-API-Key header with valid API key.
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):
        api_key = request.headers.get('X-API-Key')
        if not api_key:
            print(f"[WARNING] Missing API key in request from {request.remote_addr}")
            return jsonify({
                "success": False,
                "message": "Missing API key. Include X-API-Key header."
            }), 401
        if not validate_api_key(api_key):
            print(f"[WARNING] Invalid API key provided from {request.remote_addr}")
            return jsonify({"success": False, "message": "Invalid API key."}), 401
        return f(*args, **kwargs)
    return decorated_function

