# fullstack/backend/services/db_inference.py
from typing import Op<PERSON>, Tuple
from .teams import _teams_match
from .odds_api import resolve_sport_key
from data_science_modules.planet_scale_port import get_connection


def _fetch_unique_games_by_date(event_date: str):
    try:
        with get_connection() as conn:
            cur = conn.cursor()
            cur.execute(
                """
                SELECT league, team_a, team_b
                FROM events
                WHERE event_date=%s
                  AND (pick_type IS NULL OR pick_type IN ('MoneyLine','Spread','Total'))
                """,
                (event_date,)
            )
            rows = cur.fetchall() or []
    except Exception:
        rows = []

    seen = set()
    unique = []
    for league, ta, tb in rows:
        la = (league or '').strip()
        a = (ta or '').strip()
        b = (tb or '').strip()
        key = (la.upper(),) + tuple(sorted([(a or '').lower(), (b or '').lower()]))
        if key in seen:
            continue
        seen.add(key)
        unique.append({'league': la, 'team_a': a, 'team_b': b})
    return unique


def _find_opponent_from_db_today(league: str, known_team: str, event_date: str):
    if not league or not known_team:
        return None
    known_lc = (known_team or '').strip().lower()
    league_uc = (league or '').strip().upper()
    for g in _fetch_unique_games_by_date(event_date):
        if (g.get('league') or '').strip().upper() != league_uc:
            continue
        a_lc = (g.get('team_a') or '').strip().lower()
        b_lc = (g.get('team_b') or '').strip().lower()
        if known_lc == a_lc:
            return g.get('team_b')
        if known_lc == b_lc:
            return g.get('team_a')
    return None


def fill_missing_teams_from_db(league: str,
                               event_date: Optional[str],
                               team_a: Optional[str],
                               team_b: Optional[str]) -> Tuple[Optional[str], Optional[str]]:
    try:
        if not event_date or ((team_a or '').strip() and (team_b or '').strip()):
            return None, None
        known = None
        known_is_a = False
        def _is_unknown_team(value: Optional[str]) -> bool:
            v = (value or "").strip().lower()
            return v in ("", "unknown", "null", "none", "other")
        if not _is_unknown_team(team_a):
            known = (team_a or '').strip()
            known_is_a = True
        elif not _is_unknown_team(team_b):
            known = (team_b or '').strip()
            known_is_a = False
        else:
            return None, None
        known_lc = known.lower()
        placeholders = {"unknown", "null", "", "none", "over", "under"}
        def is_real(v: Optional[str]) -> bool:
            return (v or "").strip().lower() not in placeholders
        with get_connection() as conn:
            cur = conn.cursor()
            candidates: list[str] = []
            if known_is_a:
                cur.execute(
                    """
                    SELECT team_b, COUNT(*) AS c
                    FROM events
                    WHERE event_date=%s AND league=%s AND LOWER(team_a)=LOWER(%s)
                    GROUP BY team_b
                    ORDER BY c DESC
                    LIMIT 5
                    """,
                    (event_date, league, known),
                )
                for tb, _cnt in cur.fetchall() or []:
                    if is_real(tb):
                        candidates.append(str(tb))
                cur.execute(
                    """
                    SELECT team_a, COUNT(*) AS c
                    FROM events
                    WHERE event_date=%s AND league=%s AND LOWER(team_b)=LOWER(%s)
                    GROUP BY team_a
                    ORDER BY c DESC
                    LIMIT 5
                    """,
                    (event_date, league, known),
                )
                for ta2, _cnt in cur.fetchall() or []:
                    if is_real(ta2):
                        candidates.append(str(ta2))
                if candidates:
                    return candidates[0], None if known_is_a else candidates[0]
            else:
                cur.execute(
                    """
                    SELECT team_a, COUNT(*) AS c
                    FROM events
                    WHERE event_date=%s AND league=%s AND LOWER(team_b)=LOWER(%s)
                    GROUP BY team_a
                    ORDER BY c DESC
                    LIMIT 5
                    """,
                    (event_date, league, known),
                )
                for ta, _cnt in cur.fetchall() or []:
                    if is_real(ta):
                        candidates.append(str(ta))
                cur.execute(
                    """
                    SELECT team_b, COUNT(*) AS c
                    FROM events
                    WHERE event_date=%s AND league=%s AND LOWER(team_a)=LOWER(%s)
                    GROUP BY team_b
                    ORDER BY c DESC
                    LIMIT 5
                    """,
                    (event_date, league, known),
                )
                for tb2, _cnt in cur.fetchall() or []:
                    if is_real(tb2):
                        candidates.append(str(tb2))
                if candidates:
                    return candidates[0], None if not known_is_a else candidates[0]
        return None, None
    except Exception:
        return None, None

