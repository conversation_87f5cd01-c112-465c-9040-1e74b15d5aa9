import React, { useState, useEffect } from "react";
import { StatTypeSelectorProps, StatType } from "../../../types/manualPick";

// Define stat types by league for filtering
const STAT_TYPES_BY_LEAGUE: Record<string, StatType[]> = {
  NBA: [
    "Points",
    "Rebounds",
    "Assists",
    "Steals",
    "Blocks",
    "Turnovers",
    "PRA",
    "Fantasy Points",
    "3-pt Made",
    "Double-doubles",
    "Triple-doubles",
    "FG Made",
    "FT Made",
    "Field Goal %",
    "Free Throw %",
    "3-pt %",
    "Offensive Rebounds",
    "Defensive Rebounds",
    "Minutes",
    "+/-",
  ],
  NFL: [
    "Passing Yards",
    "Rushing Yards",
    "Receiving Yards",
    "Touchdowns",
    "Receptions",
    "Tackles",
    "Interceptions",
    "Sacks",
    "QB Rating",
    "Completion %",
    "Pass Attempts",
    "Rush Attempts",
    "Fumbles",
    "Field Goals",
    "Extra Points",
    "Fantasy Points",
  ],
  MLB: [
    "Hits",
    "Home Runs",
    "RBIs",
    "Strikeouts",
    "Stolen Bases",
    "Runs",
    "Batting Average",
    "On-base %",
    "Slugging %",
    "ERA",
    "WHIP",
    "Innings Pitched",
    "Walks",
    "Doubles",
    "Triples",
    "Saves",
    "Errors",
    "Fantasy Points",
  ],
  NHL: [
    "Goals",
    "Assists",
    "Points",
    "Shots",
    "Saves",
    "Hits",
    "Plus/minus",
    "Penalty Minutes",
    "Power Play Goals",
    "Short-handed Goals",
    "Game-winning Goals",
    "Face-off %",
    "Blocked Shots",
    "Fantasy Points",
  ],
};

// Default stat types when no league is selected or league not found
const DEFAULT_STAT_TYPES: StatType[] = STAT_TYPES_BY_LEAGUE.NBA;

interface StatTypeSelectorExtendedProps extends StatTypeSelectorProps {
  selectedLeague?: string;
}

/**
 * StatTypeSelector component for selecting stat types with league-based filtering
 */
const StatTypeSelector: React.FC<StatTypeSelectorExtendedProps> = ({
  availableTypes,
  selectedType,
  onSelect,
  allowCustom = false,
  selectedLeague,
}) => {
  const [filteredStats, setFilteredStats] = useState<StatType[]>([]);
  const [customStat, setCustomStat] = useState<string>("");
  const [showCustomInput, setShowCustomInput] = useState<boolean>(false);

  // Filter stats based on selected league
  useEffect(() => {
    if (selectedLeague && STAT_TYPES_BY_LEAGUE[selectedLeague]) {
      setFilteredStats(STAT_TYPES_BY_LEAGUE[selectedLeague]);
    } else if (availableTypes && availableTypes.length > 0) {
      setFilteredStats(availableTypes);
    } else {
      setFilteredStats(DEFAULT_STAT_TYPES);
    }
  }, [selectedLeague, availableTypes]);

  // Handle stat selection
  const handleStatSelect = (stat: StatType) => {
    if (stat === "__CUSTOM__") {
      setShowCustomInput(true);
      setCustomStat("");
    } else {
      setShowCustomInput(false);
      onSelect(stat);
    }
  };

  // Handle custom stat submission
  const handleCustomStatSubmit = () => {
    if (customStat.trim()) {
      onSelect(customStat.trim());
      setShowCustomInput(false);
      setCustomStat("");
    }
  };

  // Handle custom stat cancel
  const handleCustomStatCancel = () => {
    setShowCustomInput(false);
    setCustomStat("");
    // If no valid stat was previously selected, select the first available stat
    if (!selectedType || selectedType === "__CUSTOM__") {
      if (filteredStats.length > 0) {
        onSelect(filteredStats[0]);
      }
    }
  };

  // Check if a stat is selected (for single-select behavior)
  const isStatSelected = (stat: StatType): boolean => {
    return selectedType === stat;
  };

  return (
    <div className="space-y-4">
      <div>
        <label className="block text-sm font-medium text-gray-300 mb-1">
          Stat Type *
          {selectedLeague && (
            <span className="text-xs text-gray-500 ml-2">
              (Filtered for {selectedLeague})
            </span>
          )}
        </label>

        {/* Scrollable stat type list */}
        <div className="bg-[#233e6c] rounded-md border border-gray-600 max-h-64 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-400 scrollbar-track-gray-700">
          <div className="p-2 space-y-1">
            {filteredStats.map((stat) => (
              <label
                key={stat}
                className={`flex items-center p-2 rounded cursor-pointer transition-colors hover:bg-[#4a9e0d] ${
                  isStatSelected(stat)
                    ? "bg-[#58C612] text-black"
                    : "text-white"
                }`}
              >
                <input
                  type="checkbox"
                  checked={isStatSelected(stat)}
                  onChange={() => handleStatSelect(stat)}
                  className="sr-only" // Hide actual checkbox, use custom styling
                />
                <div
                  className={`w-4 h-4 rounded border-2 mr-3 flex items-center justify-center ${
                    isStatSelected(stat)
                      ? "bg-white border-black"
                      : "border-gray-400 bg-transparent"
                  }`}
                >
                  {isStatSelected(stat) && (
                    <svg
                      className="w-3 h-3 text-black"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fillRule="evenodd"
                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                        clipRule="evenodd"
                      />
                    </svg>
                  )}
                </div>
                <span className="text-sm">{stat}</span>
              </label>
            ))}

            {/* Custom stat option */}
            {allowCustom && (
              <label
                className={`flex items-center p-2 rounded cursor-pointer transition-colors hover:bg-[#4a9e0d] ${
                  showCustomInput ? "bg-[#58C612] text-black" : "text-white"
                }`}
              >
                <input
                  type="checkbox"
                  checked={showCustomInput}
                  onChange={() => handleStatSelect("__CUSTOM__")}
                  className="sr-only"
                />
                <div
                  className={`w-4 h-4 rounded border-2 mr-3 flex items-center justify-center ${
                    showCustomInput
                      ? "bg-white border-black"
                      : "border-gray-400 bg-transparent"
                  }`}
                >
                  {showCustomInput && (
                    <svg
                      className="w-3 h-3 text-black"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fillRule="evenodd"
                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                        clipRule="evenodd"
                      />
                    </svg>
                  )}
                </div>
                <span className="text-sm">Custom Stat Type</span>
              </label>
            )}
          </div>
        </div>

        {/* Custom stat input */}
        {showCustomInput && (
          <div className="mt-3 p-3 bg-[#1a2d54] rounded-md border border-gray-600">
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Enter Custom Stat Type
            </label>
            <div className="flex gap-2">
              <input
                type="text"
                value={customStat}
                onChange={(e) => setCustomStat(e.target.value)}
                onKeyPress={(e) => {
                  if (e.key === "Enter") {
                    handleCustomStatSubmit();
                  } else if (e.key === "Escape") {
                    handleCustomStatCancel();
                  }
                }}
                className="flex-1 bg-[#233e6c] rounded-md p-2.5 focus:ring-2 focus:ring-[#58C612] outline-none text-white"
                placeholder="e.g., Free Throws Made"
                autoFocus
              />
              <button
                type="button"
                onClick={handleCustomStatSubmit}
                disabled={!customStat.trim()}
                className="px-3 py-2 bg-[#58C612] text-black rounded-md hover:bg-[#4a9e0d] disabled:bg-gray-600 disabled:text-gray-400 disabled:cursor-not-allowed transition-colors text-sm"
              >
                Add
              </button>
              <button
                type="button"
                onClick={handleCustomStatCancel}
                className="px-3 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors text-sm"
              >
                Cancel
              </button>
            </div>
          </div>
        )}

        {/* Selected stat display */}
        {selectedType && selectedType !== "__CUSTOM__" && !showCustomInput && (
          <div className="mt-2 text-xs text-gray-400">
            Selected: <span className="text-[#58C612]">{selectedType}</span>
          </div>
        )}
      </div>
    </div>
  );
};

export default StatTypeSelector;
