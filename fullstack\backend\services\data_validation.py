"""
Data validation and cleaning services for the API Data Insertion Service.

This module provides comprehensive validation and cleaning functions for sports prediction data
submitted through the external API endpoint.
"""

import re
from typing import Dict, List, Tuple, Optional, Any
from datetime import datetime
try:
    from zoneinfo import ZoneInfo  # Python 3.9+
except ImportError:  # pragma: no cover - fallback for older runtimes
    ZoneInfo = None  # type: ignore

# ---------------------------
# Helper: normalize prediction_time to ensure it always conforms to
# YYYY-MM-DD HH:MM:SS. If seconds are missing we append ':00'. If only
# a date is provided we default time to 09:00:00. Returns None if the
# string cannot be parsed.
# ---------------------------
import re as _re
from typing import Optional as _Optional

def _normalize_prediction_time(pred_time: str, event_date: _Optional[str] = None) -> _Optional[str]:
    """Return prediction_time in canonical 'YYYY-MM-DD HH:MM:SS' (Pacific Time).

    Supports inputs like:
    - "YYYY-MM-DD HH:MM[:SS]"
    - "YYYY-MM-DD HH:MM[:SS] America/Los_Angeles"
    - "YYYY-MM-DD HH:MM[:SS] ET|EST|EDT|PT|PST|PDT|CT|CST|CDT|MT|MST|MDT"
    - "YYYY-MM-DD" (defaults to 09:00:00 PT)
    """
    if not pred_time:
        return None

    pred_time = str(pred_time).strip()

    # Quick path: exact formats without timezone
    for fmt in ("%Y-%m-%d %H:%M:%S", "%Y-%m-%d %H:%M"):
        try:
            dt = datetime.strptime(pred_time, fmt)
            # Normalize to full seconds
            return dt.strftime("%Y-%m-%d %H:%M:%S")
        except ValueError:
            pass

    # Date-only
    try:
        dt = datetime.strptime(pred_time, "%Y-%m-%d")
        return dt.strftime("%Y-%m-%d 12:00:00")
    except ValueError:
        pass

    # Handle timezone-suffixed strings
    # Examples: "2025-08-07 19:00:00 America/Los_Angeles", "2025-08-07 22:00 ET"
    tz_abbr_map = {
        "ET": "America/New_York", "EST": "America/New_York", "EDT": "America/New_York",
        "CT": "America/Chicago",  "CST": "America/Chicago",  "CDT": "America/Chicago",
        "MT": "America/Denver",   "MST": "America/Denver",   "MDT": "America/Denver",
        "PT": "America/Los_Angeles", "PST": "America/Los_Angeles", "PDT": "America/Los_Angeles",
    }

    m = _re.match(r"^(\d{4}-\d{2}-\d{2})[ T](\d{1,2}:\d{2}(?::\d{2})?)(?:\s+([A-Za-z_\-/]+))$",
                  pred_time)
    if m:
        date_part, time_part, tz_part = m.group(1), m.group(2), m.group(3)
        # Normalize time to HH:MM:SS
        if len(time_part) == 5:
            time_part = time_part + ":00"

        # Map timezone
        tz_name = tz_abbr_map.get(tz_part.upper(), tz_part)
        try:
            base_dt = datetime.strptime(f"{date_part} {time_part}", "%Y-%m-%d %H:%M:%S")
        except ValueError:
            base_dt = None

        if base_dt and ZoneInfo is not None:
            try:
                src_tz = ZoneInfo(tz_name) if "/" in tz_name else ZoneInfo(tz_abbr_map.get(tz_part.upper(), "America/Los_Angeles"))
                dt_src = base_dt.replace(tzinfo=src_tz)
                dt_pt = dt_src.astimezone(ZoneInfo("America/Los_Angeles"))
                return dt_pt.strftime("%Y-%m-%d %H:%M:%S")
            except Exception:
                pass
        # If ZoneInfo unavailable or mapping failed, return naive normalized string
        if base_dt:
            return base_dt.strftime("%Y-%m-%d %H:%M:%S")

    # Handle 12-hour clock with am/pm and TZ abbr (e.g., "2025-08-07 12:05pm ET")
    m2 = _re.match(r"^(\d{4}-\d{2}-\d{2})[ T](\d{1,2}):(\d{2})\s*(am|pm)\s*([A-Za-z]{2,3})$",
                   pred_time, _re.IGNORECASE)
    if m2:
        date_part, hour, minute, ampm, tz_abbr = m2.groups()
        hour_i = int(hour) % 12
        if ampm.lower() == "pm":
            hour_i += 12
        try:
            base_dt = datetime.strptime(f"{date_part} {hour_i:02d}:{minute}:00", "%Y-%m-%d %H:%M:%S")
        except ValueError:
            base_dt = None
        tz_name = tz_abbr_map.get(tz_abbr.upper())
        if base_dt and ZoneInfo is not None and tz_name:
            try:
                dt_src = base_dt.replace(tzinfo=ZoneInfo(tz_name))
                dt_pt = dt_src.astimezone(ZoneInfo("America/Los_Angeles"))
                return dt_pt.strftime("%Y-%m-%d %H:%M:%S")
            except Exception:
                pass
        if base_dt:
            return base_dt.strftime("%Y-%m-%d %H:%M:%S")

    # No implicit fallback here; caller may decide how to handle missing time
    return None


def validate_and_clean_pick_data(pick_data: dict) -> Tuple[dict, List[str]]:
    """
    Validate and clean pick data for API insertion.
    
    Args:
        pick_data (dict): Raw pick data from API request
        
    Returns:
        tuple: (cleaned_data, list_of_errors)
    """
    errors = []
    cleaned_data = pick_data.copy()
    
    # Convert empty strings to None for optional fields (player_name, stat_type, stat_threshold)
    for field in ['player_name', 'stat_type', 'stat_threshold']:
        if cleaned_data.get(field) == "":
            cleaned_data[field] = None

    # Required field validation
    required_fields = ['league', 'pick_type', 'expert_name', 'event_date']
    for field in required_fields:
        if not pick_data.get(field):
            errors.append(f"Missing required field: {field}")
    
    # Special handling for prediction field (0 is valid)
    if 'prediction' not in pick_data or pick_data['prediction'] is None:
        errors.append("Missing required field: prediction")
    
    # Pick type specific validation
    pick_type = pick_data.get('pick_type', '').strip()
    if pick_type == 'Prop':
        # Check if this is a team total (no player name) or player prop
        player_name = pick_data.get('player_name', '').strip()
        stat_type = pick_data.get('stat_type', '').strip()
        stat_threshold = pick_data.get('stat_threshold')

        if not player_name and stat_type.lower() in ['runs', 'r', 'points', 'pts', 'total', 'o/u']:
            # Convert to a 'Total' pick
            cleaned_data['pick_type'] = 'Total'
            print(f"[DEBUG] Converting team total to 'Total' pick type: {stat_type} {stat_threshold}")
        else:
            # Player prop requires player fields
            for field in ['player_name', 'stat_type', 'stat_threshold']:
                if not pick_data.get(field):
                    errors.append(f"Player prop picks require field: {field}")
    elif pick_type in ['MoneyLine', 'Spread']:
        # Team-based picks require teams; null player/stat fields
        for field in ['team_a', 'team_b']:
            if not pick_data.get(field):
                errors.append(f"{pick_type} picks require field: {field}")
        cleaned_data['player_name'] = None
        cleaned_data['stat_type'] = None
        cleaned_data['stat_threshold'] = None
    elif pick_type == 'Total':
        # Team total requires a threshold; direction comes from Over/Under or prediction value
        if pick_data.get('stat_threshold') is None:
            errors.append("Total picks require field: stat_threshold")
    
    # League normalization (no strict allowlist; downstream Odds API resolver handles mapping)
    league = pick_data.get('league', '').strip().upper()
    cleaned_data['league'] = league
    
    # Pick type validation and normalization
    if pick_type:
        # Normalize common variations
        pick_type_normalized = pick_type.strip()
        if pick_type_normalized in ['PlayerProp', 'Player Prop', 'playerprop']:
            pick_type_normalized = 'Prop'
            cleaned_data['pick_type'] = 'Prop'
        
        valid_pick_types = ['MoneyLine', 'Spread', 'Prop', 'Total']
        if pick_type_normalized not in valid_pick_types:
            errors.append(f"Invalid pick_type: {pick_type}. Must be one of: {', '.join(valid_pick_types)}")
        else:
            cleaned_data['pick_type'] = pick_type_normalized
    
    # Prediction normalization to internal signal
    def _normalize_signal(pick_type_local: str, raw_pred: Any, team_a_val: str, team_b_val: str) -> Optional[int]:
        # Accept ints/strings; map to 1 (Over/Higher) or 0 (Under/Lower)
        if raw_pred is None or raw_pred == "":
            raw = None
        else:
            raw = str(raw_pred).strip().lower()

        # Explicit strings
        if isinstance(raw_pred, str):
            if raw in ["over", "o", "+", "higher", "hi", "more", "yes"]:
                return 1
            if raw in ["under", "u", "-", "lower", "lo", "less", "no"]:
                return 0

        # Team total: team_a='Over'/'Under' is common; fall back to that if present
        if pick_type_local == 'Total':
            ta = (team_a_val or '').strip().lower()
            if ta in ("over", "o"): return 1
            if ta in ("under", "u"): return 0

        # Numeric
        try:
            if raw is not None:
                vi = int(raw)
                if vi in (0, 1):
                    return vi
        except Exception:
            pass
        return None

    normalized_pred = _normalize_signal(cleaned_data.get('pick_type', pick_type),
                                        pick_data.get('prediction'),
                                        pick_data.get('team_a') or '',
                                        pick_data.get('team_b') or '')
    if normalized_pred is None:
        # Only error if pick requires direction
        if cleaned_data.get('pick_type') in ['Prop', 'Total']:
            errors.append("Prediction must be provided as 0/1 or Over/Under for Prop/Total")
    else:
        cleaned_data['prediction'] = normalized_pred
    
    # Expert confidence validation (0.0-1.0)
    expert_confidence = pick_data.get('expert_confidence')
    print(f"[DEBUG] Raw expert_confidence from input: {expert_confidence} (type: {type(expert_confidence)})")
    if expert_confidence is not None:
        try:
            conf_float = float(expert_confidence)
            print(f"[DEBUG] Converted expert_confidence to float: {conf_float}")
            if not (0.0 <= conf_float <= 1.0):
                errors.append("Expert confidence must be between 0.0 and 1.0")
            else:
                cleaned_data['expert_confidence'] = conf_float
                print(f"[DEBUG] Set cleaned expert_confidence: {conf_float}")
        except (ValueError, TypeError):
            errors.append("Expert confidence must be a valid number between 0.0 and 1.0")

    # Provider-specific adapter: Gamescript AI sometimes places confidence in 'odds'
    try:
        expert_name_norm = (pick_data.get('expert_name') or '').strip().lower()
        raw_odds = pick_data.get('odds')
        if expert_name_norm == 'gamescript ai' and raw_odds is not None:
            if 'expert_confidence' not in cleaned_data or cleaned_data.get('expert_confidence') in (None, '', 0):
                try:
                    val = float(str(raw_odds).strip())
                    if 0.0 <= val <= 1.0:
                        cleaned_data['expert_confidence'] = val
                    elif 1.0 < val <= 100.0 and not str(raw_odds).strip().startswith(('+', '-')):
                        cleaned_data['expert_confidence'] = round(val / 100.0, 4)
                except Exception:
                    pass
        if 'odds' in cleaned_data:
            cleaned_data.pop('odds', None)
    except Exception:
        if 'odds' in cleaned_data:
            cleaned_data.pop('odds', None)
    
    # Event date validation
    event_date = pick_data.get('event_date')
    if event_date:
        if not _validate_date_format(event_date):
            errors.append("Event date must be in YYYY-MM-DD format")
    
    # Prediction time normalization & validation
    prediction_time = pick_data.get('prediction_time')
    if prediction_time:
        normalized_pt = _normalize_prediction_time(prediction_time, event_date)
        if normalized_pt:
            cleaned_data['prediction_time'] = normalized_pt
        else:
            errors.append("Prediction time must be in YYYY-MM-DD HH:MM:SS format")
    
    # Player name formatting (for Prop picks)
    if pick_data.get('player_name'):
        cleaned_data['player_name'] = format_player_name_display(pick_data['player_name'])
        
        # For Prop picks, ensure player_team is set properly
        if cleaned_data.get('pick_type') == 'Prop':
            # Use team_a as the player's team if it's a valid team name
            if cleaned_data.get('team_a') and cleaned_data['team_a'] not in ['OVER', 'UNDER', '']:
                # Don't override if player_team is already properly set
                if not pick_data.get('player_team') or pick_data.get('player_team') in ['', 'None']:
                    cleaned_data['player_team'] = cleaned_data['team_a']
    
    # Stat type normalization (for Prop picks)
    if pick_data.get('stat_type'):
        cleaned_data['stat_type'] = normalize_stat_type(pick_data['stat_type'])

    # For Total picks, carry explicit Over/Under direction via stat_type for event_id construction
    if cleaned_data.get('pick_type') == 'Total':
        direction = None
        if 'prediction' in cleaned_data:
            direction = 'Over' if cleaned_data['prediction'] == 1 else 'Under'
        else:
            ta = (pick_data.get('team_a') or '').strip().lower()
            if ta in ('over', 'o'):
                direction = 'Over'
            elif ta in ('under', 'u'):
                direction = 'Under'
        if direction:
            cleaned_data['stat_type'] = direction
    
    # Stat threshold validation (for Prop/Total picks)
    stat_threshold = pick_data.get('stat_threshold')
    if stat_threshold is not None:
        try:
            threshold_float = float(stat_threshold)
            # Allow negative values for spreads (e.g., -5.5)
            cleaned_data['stat_threshold'] = threshold_float
        except (ValueError, TypeError):
            errors.append("Stat threshold must be a valid number")
    
    # Team name cleaning and validation
    if pick_data.get('team_a'):
        cleaned_team_a = clean_team_name(pick_data['team_a'])
        if not cleaned_team_a and pick_type in ['MoneyLine', 'Spread']:
            errors.append("Invalid team_a name - must be a valid team nickname")
        cleaned_data['team_a'] = cleaned_team_a
    if pick_data.get('team_b'):
        cleaned_team_b = clean_team_name(pick_data['team_b'])
        if not cleaned_team_b and pick_type in ['MoneyLine', 'Spread']:
            errors.append("Invalid team_b name - must be a valid team nickname")
        cleaned_data['team_b'] = cleaned_team_b
    
    # Expert name cleaning
    if pick_data.get('expert_name'):
        cleaned_data['expert_name'] = pick_data['expert_name'].strip()
    
    # Ignore inbound odds; external sources may provide invalid/partial formats
    if 'odds' in cleaned_data:
        cleaned_data.pop('odds', None)
    
    return cleaned_data, errors


def format_player_name(player_name: str) -> str:
    """
    Format player name to proper display format (e.g., "J. Altuve", "S. Ohtani").
    
    Args:
        player_name (str): Raw player name (e.g., "JOSEALTUVE", "SHOHEI OHTANI", "Jose Altuve")
        
    Returns:
        str: Formatted player name (e.g., "J. Altuve")
    """
    if not player_name:
        return ""
    
    # Clean and normalize the input
    name = player_name.strip()
    
    # Handle all caps names without spaces (e.g., "JOSEALTUVE")
    if name.isupper() and " " not in name and "." not in name:
        # This is likely an all-caps concatenated name
        # For now, we'll need to make educated guesses about where to split
        # Common patterns: First name usually 3-6 characters
        if len(name) > 6:
            # Try to split at reasonable points
            # Look for common first name endings
            first_name = name[:4] if len(name) > 8 else name[:3]
            last_name = name[len(first_name):]
            return f"{first_name[0]}. {last_name.title()}"
        else:
            # Short name, treat as single name
            return name.title()
    
    # Handle names with spaces (e.g., "Jose Altuve", "JOSE ALTUVE")
    elif " " in name:
        parts = name.split()
        if len(parts) >= 2:
            first_name = parts[0].strip()
            last_name = " ".join(parts[1:]).strip()
            return f"{first_name[0].upper()}. {last_name.title()}"
        else:
            return name.title()
    
    # Handle names with periods already (e.g., "J. Altuve")
    elif "." in name:
        # Already formatted, just ensure proper capitalization
        parts = name.split(".")
        if len(parts) == 2:
            initial = parts[0].strip().upper()
            last_name = parts[1].strip().title()
            return f"{initial}. {last_name}"
        else:
            return name.title()
    
    # Single name or other format
    else:
        return name.title()


def format_player_name_display(player_name: str) -> str:
    """
    Format player name for proper display (e.g., "J. Altuve", "S. Ohtani").
    
    Args:
        player_name (str): Raw player name (could be "JAKEFERGUSON", "Jake Ferguson", etc.)
        
    Returns:
        str: Properly formatted player name
    """
    if not player_name:
        return ""
    
    # Clean and normalize the input
    name = player_name.strip()
    
    # If it's already in the correct format (contains a period), return as-is
    if ". " in name and len(name.split()) == 2:
        return name
    
    # Handle all caps names like "JAKEFERGUSON" or "JONATHANTAYLOR"
    if name.isupper() and " " not in name:
        # Try to split common name patterns
        # This is a simple heuristic - in production you'd want a more sophisticated approach
        if len(name) > 6:
            # Assume first name is 3-5 characters, rest is last name
            first_name = name[:4] if len(name) > 8 else name[:3]
            last_name = name[len(first_name):]
            return f"{first_name[0]}. {last_name.title()}"
    
    # Handle "Jake Ferguson" format
    if " " in name:
        parts = name.split()
        if len(parts) >= 2:
            first_name = parts[0]
            last_name = " ".join(parts[1:])
            return f"{first_name[0].upper()}. {last_name.title()}"
    
    # Fallback - return as-is if we can't parse it
    return name


def clean_player_name(player_name: str) -> str:
    """
    Clean player name by removing spaces and periods for consistent event ID generation.
    
    Args:
        player_name (str): Raw player name
        
    Returns:
        str: Cleaned player name
    """
    if not player_name:
        return ""
    
    # Remove spaces and periods, convert to uppercase for consistency
    return player_name.strip().replace(" ", "").replace(".", "").upper()


def clean_team_name(team_name: str) -> str:
    """
    Clean and validate team name to ensure only proper team nicknames are used.
    
    Args:
        team_name (str): Raw team name
        
    Returns:
        str: Cleaned team name or empty string if invalid
    """
    if not team_name:
        return ""
    
    # List of invalid team names that should be rejected
    invalid_names = {
        'AL', 'NL', 'AMERICAN LEAGUE', 'NATIONAL LEAGUE',
        'EASTERN CONFERENCE', 'WESTERN CONFERENCE', 'AFC', 'NFC',
        'EAST', 'WEST', 'CENTRAL', 'ATLANTIC', 'PACIFIC', 'SOUTHWEST',
        'NORTHWEST', 'SOUTHEAST', 'WS', 'OVER', 'UNDER'
    }
    
    # Valid MLB team nicknames
    valid_mlb_teams = {
        'YANKEES', 'RED SOX', 'BLUE JAYS', 'ORIOLES', 'RAYS',
        'WHITE SOX', 'GUARDIANS', 'TIGERS', 'ROYALS', 'TWINS',
        'ASTROS', 'ANGELS', 'ATHLETICS', 'MARINERS', 'RANGERS',
        'BRAVES', 'MARLINS', 'METS', 'PHILLIES', 'NATIONALS',
        'CUBS', 'REDS', 'BREWERS', 'PIRATES', 'CARDINALS',
        'DIAMONDBACKS', 'ROCKIES', 'DODGERS', 'PADRES', 'GIANTS'
    }
    
    # Valid NBA team nicknames
    valid_nba_teams = {
        'LAKERS', 'WARRIORS', 'CLIPPERS', 'KINGS', 'SUNS',
        'NUGGETS', 'JAZZ', 'TRAIL BLAZERS', 'BLAZERS', 'THUNDER',
        'MAVERICKS', 'ROCKETS', 'SPURS', 'PELICANS', 'GRIZZLIES',
        'TIMBERWOLVES', 'BULLS', 'CAVALIERS', 'PISTONS', 'PACERS',
        'BUCKS', 'CELTICS', 'NETS', 'KNICKS', '76ERS', 'SIXERS',
        'RAPTORS', 'HEAT', 'MAGIC', 'HAWKS', 'HORNETS', 'WIZARDS'
    }
    
    # Clean the input
    cleaned = team_name.strip().upper()
    
    # Remove city names and common prefixes
    city_prefixes = [
        'NEW YORK', 'LOS ANGELES', 'SAN FRANCISCO', 'SAN DIEGO', 'SAN ANTONIO',
        'GOLDEN STATE', 'PORTLAND TRAIL', 'OKLAHOMA CITY', 'MIAMI', 'BOSTON',
        'CHICAGO', 'DETROIT', 'MILWAUKEE', 'PHILADELPHIA', 'TORONTO',
        'ATLANTA', 'CHARLOTTE', 'WASHINGTON', 'CLEVELAND', 'INDIANA',
        'HOUSTON', 'DALLAS', 'MEMPHIS', 'NEW ORLEANS', 'MINNESOTA',
        'DENVER', 'UTAH', 'PHOENIX', 'SACRAMENTO', 'LAS VEGAS'
    ]
    
    for prefix in city_prefixes:
        if cleaned.startswith(prefix + ' '):
            cleaned = cleaned[len(prefix + ' '):]
            break
    
    # Check if it's in the invalid list
    if cleaned in invalid_names:
        return ""
    
    # Check if it's a valid team name and return in proper title case
    if cleaned in valid_mlb_teams or cleaned in valid_nba_teams:
        # Convert to proper title case with special handling for certain teams
        title_cased = cleaned.title()
        
        # Handle special cases that don't work well with .title()
        special_cases = {
            '76Ers': '76ers',
            'Sixers': 'Sixers'  # This one is already correct but keeping for clarity
        }
        
        return special_cases.get(title_cased, title_cased)
    
    # For other leagues or unknown teams, allow if it looks like a valid team name
    # (not in invalid list and contains letters)
    if re.match(r'^[A-Z]+[A-Z\s]*[A-Z]$', cleaned) and len(cleaned) >= 3:
        return cleaned.replace(' ', '').title()
    
    return ""


def normalize_stat_type(stat_type: str) -> str:
    """
    Normalize stat type to standard abbreviations using existing stat_type_map.
    
    Args:
        stat_type (str): Raw stat type string
        
    Returns:
        str: Standardized stat type abbreviation
    """
    if not stat_type:
        return ""
    
    # Map stat types to standard abbreviations
    stat_type_map = {
        # Team pick types (preserve original case)
        'moneyline': 'MoneyLine',
        'spread': 'Spread',
        # NBA/NFL stats
        'points': 'PTS',
        'point': 'PTS',
        'pts': 'PTS',
        'rebounds': 'REB',
        'rebound': 'REB',
        'reb': 'REB',
        'assists': 'AST',
        'assist': 'AST',
        'ast': 'AST',
        'steals': 'STL',
        'steal': 'STL',
        'stl': 'STL',
        'blocks': 'BLK',
        'block': 'BLK',
        'blk': 'BLK',
        'turnovers': 'TO',
        'turnover': 'TO',
        'to': 'TO',
        'threes made': '3PM',
        'three pointers made': '3PM',
        'threesmade': '3PM',
        'threepointsmade': '3PM',
        '3pm': '3PM',
        'threes': '3PM',
        # MLB stats
        'home runs': 'HR',
        'homeruns': 'HR',
        'homerun': 'HR',
        'hr': 'HR',
        'hits': 'H',
        'hit': 'H',
        'h': 'H',
        'rbi': 'RBI',
        'runs': 'R',
        'run': 'R',
        'r': 'R',
        'strikeouts': 'K',
        'strikeout': 'K',
        'k': 'K',
        'walks': 'BB',
        'walk': 'BB',
        'bb': 'BB',
        # NFL stats
        'passing touchdowns': 'PASSTD',
        'passingtouchdowns': 'PASSTD',
        'passtd': 'PASSTD',
        'passingtd': 'PASSTD',
        'rushing yards': 'RUSHYD',
        'rushingyard': 'RUSHYD',
        'rushingyard': 'RUSHYD',
        'rushyd': 'RUSHYD'
    }
    
    # Get standardized stat type
    stat_key = stat_type.strip().lower()
    return stat_type_map.get(stat_key, stat_type.strip())


def convert_odds_to_probability(odds_string: str) -> float:
    """
    Convert odds string to probability for crowd predictions.
    
    Args:
        odds_string (str): Odds in format like "+150", "-110", etc.
        
    Returns:
        float: Probability as decimal (0.0-1.0)
        
    Raises:
        ValueError: If odds format is invalid
    """
    if not odds_string:
        return 0.5  # Default 50% if no odds provided
    
    odds_string = odds_string.strip()
    
    # Validate odds format
    if not re.match(r'^[+-]\d+$', odds_string):
        raise ValueError(f"Invalid odds format: '{odds_string}'. Expected format: +150 or -110")
    
    try:
        if odds_string.startswith('+'):
            # Positive odds: +150 -> 100/(150+100) = 0.4
            odds_value = float(odds_string[1:])
            if odds_value <= 0:
                raise ValueError("Positive odds must be greater than 0")
            probability = 100 / (odds_value + 100)
        elif odds_string.startswith('-'):
            # Negative odds: -110 -> 110/(110+100) = 0.524
            odds_value = float(odds_string[1:])
            if odds_value <= 0:
                raise ValueError("Negative odds must be greater than 0")
            probability = odds_value / (odds_value + 100)
        else:
            raise ValueError("Odds must start with + or -")
        
        # Round to 4 decimal places for consistency
        return round(probability, 4)
        
    except ValueError as e:
        if "could not convert" in str(e):
            raise ValueError(f"Invalid numeric value in odds: '{odds_string}'")
        else:
            raise


def _validate_date_format(date_string: str) -> bool:
    """
    Validate that date string is in YYYY-MM-DD format.
    
    Args:
        date_string (str): Date string to validate
        
    Returns:
        bool: True if valid format, False otherwise
    """
    try:
        datetime.strptime(date_string, '%Y-%m-%d')
        return True
    except ValueError:
        return False


def _validate_datetime_format(datetime_string: str) -> bool:
    """
    Validate that datetime string is in YYYY-MM-DD HH:MM:SS format.
    
    Args:
        datetime_string (str): Datetime string to validate
        
    Returns:
        bool: True if valid format, False otherwise
    """
    try:
        datetime.strptime(datetime_string, '%Y-%m-%d %H:%M:%S')
        return True
    except ValueError:
        return False


def validate_required_fields_by_pick_type(pick_data: dict) -> List[str]:
    """
    Validate that all required fields are present based on pick type.
    
    Args:
        pick_data (dict): Pick data to validate
        
    Returns:
        List[str]: List of validation errors
    """
    errors = []
    pick_type = pick_data.get('pick_type', '').strip()
    
    # Base required fields for all pick types
    base_required = ['league', 'pick_type', 'prediction', 'expert_name', 'event_date']
    
    # Pick type specific required fields
    if pick_type == 'Prop':
        required_fields = base_required + ['player_name', 'stat_type', 'stat_threshold']
    elif pick_type in ['MoneyLine', 'Spread']:
        required_fields = base_required + ['team_a', 'team_b']
    else:
        required_fields = base_required
    
    # Check for missing fields
    for field in required_fields:
        if not pick_data.get(field):
            errors.append(f"Missing required field: {field}")
    
    return errors


def validate_pick_data_types(pick_data: dict) -> List[str]:
    """
    Validate data types for pick data fields.
    
    Args:
        pick_data (dict): Pick data to validate
        
    Returns:
        List[str]: List of validation errors
    """
    errors = []
    
    # String fields
    string_fields = ['league', 'team_a', 'team_b', 'pick_type', 'player_name', 
                    'stat_type', 'expert_name', 'event_date', 'odds', 'prediction_time']
    
    for field in string_fields:
        value = pick_data.get(field)
        if value is not None and not isinstance(value, str):
            errors.append(f"Field '{field}' must be a string")
    
    # Numeric fields
    numeric_fields = ['prediction', 'stat_threshold', 'expert_confidence']
    
    for field in numeric_fields:
        value = pick_data.get(field)
        if value is not None:
            try:
                float(value)
            except (ValueError, TypeError):
                errors.append(f"Field '{field}' must be a valid number")
    
    return errors