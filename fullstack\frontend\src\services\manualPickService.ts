import type { ManualPick } from "../types/manualPick";
import type { <PERSON>pert } from "../types/expert";
import SmartCache from "../utils/cache";

const EXPERT_CACHE = new SmartCache("experts_by_date", {
  freshDuration: 30 * 60 * 1000, // 30 min
  maxAge: 24 * 60 * 60 * 1000,   // 24 h
  version: "1.0.0",
});

/** Fetch unique experts for a given ISO date (YYYY-MM-DD) with caching. */
export const fetchExperts = async (date: string): Promise<Expert[]> => {
  const { data, isFresh } = EXPERT_CACHE.get<Expert[]>(date);

  if (isFresh && data) return data;

  const resp = await fetch(`/api/experts_by_date?date=${date}`);
  if (!resp.ok) throw new Error(`HTTP ${resp.status} ${resp.statusText}`);

  const json = await resp.json();
  if (!json.success) throw new Error(json.message ?? "Server error");

  const experts: Expert[] = json.experts.map((name: string, idx: number) => ({
    id: `expert_${idx + 1}`,
    name,
  }));

  EXPERT_CACHE.set(experts, date);
  return experts;
};

/** Invalidate cached experts for a specific date (or all if no date). */
export const clearExpertsCache = (date?: string): void => {
  if (date) {
    EXPERT_CACHE.invalidate(date);
  } else {
    EXPERT_CACHE.clear();
  }
};

/** Submit a manual pick to backend. */
export const submitManualPick = async (
  pick: ManualPick & { admin_password: string }
) => {
  const resp = await fetch("/api/manual_pick", {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify(pick),
  });
  if (!resp.ok) throw new Error(`HTTP ${resp.status} ${resp.statusText}`);
  return resp.json();
};