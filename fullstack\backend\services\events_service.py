# fullstack/backend/services/events_service.py
import re
from typing import <PERSON><PERSON>, <PERSON><PERSON>, List
from .db_inference import fill_missing_teams_from_db
from .odds_api import get_event_start_from_odds_api
from .teams import _teams_match, team_to_nickname
from data_science_modules.planet_scale_port import get_connection, generate_admin_event_id


def fill_missing_teams(league: str, event_date: Optional[str], team_a: Optional[str], team_b: Optional[str]) -> Tuple[str, str]:
    db_a, db_b = fill_missing_teams_from_db(league, event_date, team_a, team_b)
    if db_a is not None or db_b is not None:
        final_a = team_to_nickname(db_a if db_a is not None else team_a)
        final_b = team_to_nickname(db_b if db_b is not None else team_b)
        return final_a, final_b
    # Fallback to team nickname normalization if DB lookup fails
    normalized_a = team_to_nickname(team_a) if team_a else "Unknown"
    normalized_b = team_to_nickname(team_b) if team_b else "Unknown"
    return normalized_a, normalized_b


def _migrate_event_and_predictions(old_event_id: str, new_event_id: str, new_team_a: str, new_team_b: str) -> None:
    try:
        with get_connection() as conn:
            cur = conn.cursor()
            cur.execute("SELECT 1 FROM events WHERE event_id=%s", (new_event_id,))
            new_exists = cur.fetchone() is not None
            if new_exists:
                cur.execute(
                    """
                    SELECT expert_name, prediction, confidence, team, league, game_date, stat_threshold, prediction_time
                    FROM expert_predictions WHERE event_id=%s
                    """,
                    (old_event_id,),
                )
                rows = cur.fetchall() or []
                for (expert_name, prediction, confidence, team, league, game_date, stat_threshold, prediction_time) in rows:
                    cur.execute(
                        """
                        INSERT INTO expert_predictions
                        (event_id, expert_name, prediction, confidence, team, league, game_date, stat_threshold, prediction_time)
                        VALUES (%s,%s,%s,%s,%s,%s,%s,%s,%s)
                        ON DUPLICATE KEY UPDATE
                          prediction=VALUES(prediction),
                          confidence=VALUES(confidence),
                          team=VALUES(team),
                          league=VALUES(league),
                          game_date=VALUES(game_date),
                          stat_threshold=VALUES(stat_threshold),
                          prediction_time=VALUES(prediction_time)
                        """,
                        (new_event_id, expert_name, prediction, confidence, team, league, game_date, stat_threshold, prediction_time),
                    )
                cur.execute("SELECT crowd_probability FROM crowd_predictions WHERE event_id=%s", (old_event_id,))
                cp = cur.fetchone()
                if cp:
                    (crowd_probability,) = cp
                    cur.execute(
                        """
                        INSERT INTO crowd_predictions (event_id, crowd_probability)
                        VALUES (%s, %s)
                        ON DUPLICATE KEY UPDATE crowd_probability=VALUES(crowd_probability)
                        """,
                        (new_event_id, crowd_probability),
                    )
                cur.execute("DELETE FROM expert_predictions WHERE event_id=%s", (old_event_id,))
                cur.execute("DELETE FROM crowd_predictions WHERE event_id=%s", (old_event_id,))
                cur.execute("DELETE FROM events WHERE event_id=%s", (old_event_id,))
            else:
                cur.execute("UPDATE expert_predictions SET event_id=%s WHERE event_id=%s", (new_event_id, old_event_id))
                cur.execute("UPDATE crowd_predictions  SET event_id=%s WHERE event_id=%s", (new_event_id, old_event_id))
                cur.execute(
                    "UPDATE events SET event_id=%s, team_a=%s, team_b=%s WHERE event_id=%s",
                    (new_event_id, new_team_a, new_team_b, old_event_id),
                )
            cur.execute("UPDATE events SET team_a=%s, team_b=%s WHERE event_id=%s", (new_team_a, new_team_b, new_event_id))
            cur.execute(
                """
                UPDATE expert_predictions 
                SET team=%s 
                WHERE event_id=%s AND (team IS NULL OR team='' OR LOWER(team) IN ('unknown','null','none','other'))
                """,
                (new_team_a, new_event_id),
            )
            conn.commit()
    except Exception:
        return


def cleanup_chart_generation_predictions():
    try:
        with get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("DELETE FROM expert_predictions WHERE expert_name = %s", ("ChartGeneration",))
            conn.commit()
    except Exception:
        pass


def cleanup_unknown_team_rows(limit: int = 100) -> dict:
    details: list[dict] = []
    updated = 0
    migrated = 0
    checked = 0
    with get_connection() as conn:
        cur = conn.cursor()
        cur.execute(
            """
            SELECT event_id, event_date, league, team_a, team_b, pick_type
            FROM events
            WHERE pick_type IN ('MoneyLine','Spread')
              AND (
                    team_a IS NULL OR team_b IS NULL OR
                    LOWER(team_a) IN ('', 'unknown', 'null', 'none', 'other') OR
                    LOWER(team_b) IN ('', 'unknown', 'null', 'none', 'other')
              )
            ORDER BY event_date DESC
            LIMIT %s
            """,
            (int(limit),),
        )
        rows = cur.fetchall() or []
        for (event_id, event_date, league, team_a, team_b, pick_type) in rows:
            checked += 1
            try:
                new_a, new_b = fill_missing_teams(league or "", event_date, team_a, team_b)
                if (new_a or new_b) and ((new_a or '').strip() != (team_a or '').strip() or (new_b or '').strip() != (team_b or '').strip()):
                    cur2 = conn.cursor()
                    cur2.execute("UPDATE events SET team_a=%s, team_b=%s WHERE event_id=%s", (new_a, new_b, event_id))
                    cur2.execute(
                        """
                        UPDATE expert_predictions
                        SET team=%s
                        WHERE event_id=%s AND (team IS NULL OR team='' OR LOWER(team) IN ('unknown','null','none','other'))
                        """,
                        (new_a, event_id),
                    )
                    try:
                        corrected_id = generate_admin_event_id(
                            event_date, league or (event_id or "").split("-")[3], new_a, new_b
                        )
                        if corrected_id and corrected_id != event_id:
                            _migrate_event_and_predictions(event_id, corrected_id, new_a, new_b)
                            migrated += 1
                        updated += 1
                        details.append({"event_id": event_id, "new_team_a": new_a, "new_team_b": new_b})
                    except Exception:
                        pass
            except Exception as e:
                details.append({"event_id": event_id, "error": str(e)})
    return {
        "success": True,
        "checked": checked,
        "updated": updated,
        "migrated": migrated,
        "details": details,
    }


def correct_event_id_format(event_id: str) -> Tuple[str, List[str]]:
    """
    Corrects common event ID formatting issues.

    Args:
        event_id (str): The original event ID

    Returns:
        tuple: (corrected_event_id, list_of_corrections_made)
    """
    corrections = []
    original_event_id = event_id

    # Expected format: YYYY-MM-DD-LEAGUE-PLAYERNAME##.#STATTYPE
    pattern = r'^(\d{4}-\d{2}-\d{2})-([A-Z]+)-(.+?)(\d+(?:\.\d+)?)(.*?)$'
    match = re.match(pattern, event_id.upper())

    if not match:
        corrections.append(f"Invalid event ID format: {event_id}")
        return event_id, corrections

    date_part, league, player_part, threshold_part, stat_part = match.groups()

    # Fix player name: remove periods and spaces
    original_player = player_part
    clean_player = player_part.replace(".", "").replace(" ", "")
    if clean_player != original_player:
        corrections.append(f"Cleaned player name: '{original_player}' -> '{clean_player}'")

    # Fix threshold format: ensure decimal point
    original_threshold = threshold_part
    try:
        threshold_float = float(threshold_part)
        formatted_threshold = f"{threshold_float:.1f}"
        if formatted_threshold != original_threshold:
            corrections.append(f"Fixed threshold format: '{original_threshold}' -> '{formatted_threshold}'")
        threshold_part = formatted_threshold
    except ValueError:
        corrections.append(f"Invalid threshold format: '{original_threshold}'")

    # Map stat types to standard abbreviations by league
    original_stat = stat_part
    stat_type_map = {
        # NBA/NFL stats
        'POINTS': 'PTS', 'POINT': 'PTS', 'PTS': 'PTS',
        'REBOUNDS': 'REB', 'REBOUND': 'REB', 'REB': 'REB',
        'ASSISTS': 'AST', 'ASSIST': 'AST', 'AST': 'AST',
        'STEALS': 'STL', 'STEAL': 'STL', 'STL': 'STL',
        'BLOCKS': 'BLK', 'BLOCK': 'BLK', 'BLK': 'BLK',
        'TURNOVERS': 'TO', 'TURNOVER': 'TO', 'TO': 'TO',
        'THREESMADE': '3PM', 'THREEPOINTSMADE': '3PM', '3PM': '3PM', 'THREES': '3PM',
        # MLB stats
        'HOMERUNS': 'HR', 'HOMERUN': 'HR', 'HR': 'HR',
        'HITS': 'H', 'HIT': 'H', 'H': 'H',
        'RBI': 'RBI', 'RUNS': 'R', 'RUN': 'R', 'R': 'R',
        'STRIKEOUTS': 'K', 'STRIKEOUT': 'K', 'K': 'K',
        'WALKS': 'BB', 'WALK': 'BB', 'BB': 'BB',
        # NFL stats
        'PASSINGTOUCHDOWNS': 'PASSTD', 'PASSTD': 'PASSTD', 'PASSINGTD': 'PASSTD',
        'RUSHINGYARD': 'RUSHYD', 'RUSHYD': 'RUSHYD'
    }

    # Handle special case: MLB events using "POINTS" should map to appropriate MLB stats
    if league == 'MLB' and original_stat == 'POINTS':
        corrected_stat = 'H'
        corrections.append(f"Fixed MLB stat type: 'POINTS' -> 'H' (MLB doesn't use points)")
    else:
        corrected_stat = stat_type_map.get(original_stat.upper(), original_stat.upper())
        if corrected_stat != original_stat.upper():
            corrections.append(f"Standardized stat type: '{original_stat}' -> '{corrected_stat}'")

    # Reconstruct the corrected event ID
    corrected_event_id = f"{date_part}-{league}-{clean_player}{threshold_part}{corrected_stat}"

    if corrected_event_id != original_event_id:
        corrections.append(f"Final correction: '{original_event_id}' -> '{corrected_event_id}'")

    return corrected_event_id, corrections


def _fetch_unique_games_by_date(event_date: str) -> List[dict]:
    """Fetch unique team-vs-team games for a given date from DB (no normalization/tampering)"""
    try:
        with get_connection() as conn:
            cur = conn.cursor()
            cur.execute(
                """
                SELECT league, team_a, team_b
                FROM events
                WHERE event_date=%s
                  AND (pick_type IS NULL OR pick_type IN ('MoneyLine','Spread','Total'))
                """,
                (event_date,)
            )
            rows = cur.fetchall() or []
    except Exception:
        rows = []

    # Deduplicate ignoring order of teams
    seen = set()
    unique = []
    for league, ta, tb in rows:
        la = (league or '').strip()
        a = (ta or '').strip()
        b = (tb or '').strip()
        key = (la.upper(),) + tuple(sorted([(a or '').lower(), (b or '').lower()]))
        if key in seen:
            continue
        seen.add(key)
        unique.append({'league': la, 'team_a': a, 'team_b': b})
    return unique


def _find_opponent_from_db_today(league: str, known_team: str, event_date: str) -> Optional[str]:
    """Given a known team on a date, find its opponent from unique games (if unambiguous)"""
    if not league or not known_team:
        return None
    known_lc = (known_team or '').strip().lower()
    league_uc = (league or '').strip().upper()
    for g in _fetch_unique_games_by_date(event_date):
        if (g.get('league') or '').strip().upper() != league_uc:
            continue
        a_lc = (g.get('team_a') or '').strip().lower()
        b_lc = (g.get('team_b') or '').strip().lower()
        if known_lc == a_lc:
            return g.get('team_b')
        if known_lc == b_lc:
            return g.get('team_a')
    return None


def _is_unknown_team(value: Optional[str]) -> bool:
    """Check if a team value is considered unknown/placeholder."""
    v = (value or "").strip().lower()
    return v in ("", "unknown", "null", "none", "other")


def _format_nickname(name: Optional[str]) -> str:
    """Format a team name to its nickname."""
    try:
        nm = (name or "").strip()
        # Import here to avoid circular imports
        try:
            from services.data_validation import clean_team_name as _clean
            cleaned = _clean(nm)
            return cleaned if cleaned else "Unknown"
        except ImportError:
            return nm if nm else "Unknown"
    except Exception:
        return (name or "Unknown").strip() or "Unknown"


def fill_missing_teams_with_odds_api(league: str,
                                     event_date: Optional[str],
                                     team_a: Optional[str],
                                     team_b: Optional[str]) -> Tuple[str, str]:
    """Fill missing teams using TheOdds API."""
    import os
    import requests
    from datetime import datetime
    from .odds_api import resolve_sport_key
    from .teams import _teams_match

    try:
        # If both are known, just normalize to nicknames
        if not _is_unknown_team(team_a) and not _is_unknown_team(team_b):
            return _format_nickname(team_a), _format_nickname(team_b)

        api_key = os.getenv("ODDS_API_KEY")
        sport_key = resolve_sport_key(league)
        if not api_key or not sport_key:
            return _format_nickname(team_a), _format_nickname(team_b)

        params = {"apiKey": api_key}
        if event_date:
            try:
                from datetime import datetime as _dt
                from zoneinfo import ZoneInfo as _ZI
                dt_start_pt = _dt.strptime(f"{event_date} 00:00:00", "%Y-%m-%d %H:%M:%S").replace(tzinfo=_ZI("America/Los_Angeles"))
                dt_end_pt = _dt.strptime(f"{event_date} 23:59:59", "%Y-%m-%d %H:%M:%S").replace(tzinfo=_ZI("America/Los_Angeles"))
                params["commenceTimeFrom"] = dt_start_pt.astimezone(_ZI("UTC")).strftime("%Y-%m-%dT%H:%M:%SZ")
                params["commenceTimeTo"] = dt_end_pt.astimezone(_ZI("UTC")).strftime("%Y-%m-%dT%H:%M:%SZ")
            except Exception:
                pass

        url = f"https://api.the-odds-api.com/v4/sports/{sport_key}/events"
        resp = requests.get(url, params=params, timeout=10)
        if resp.status_code == 429:
            return _format_nickname(team_a), _format_nickname(team_b)
        resp.raise_for_status()
        events = resp.json() or []

        # Also try scores endpoint for more coverage
        matches = []
        try:
            scores_url = f"https://api.the-odds-api.com/v4/sports/{sport_key}/scores"
            scores_resp = requests.get(scores_url, params={"apiKey": api_key, "daysFrom": 3}, timeout=10)
            if scores_resp.status_code != 429:
                scores_resp.raise_for_status()
                scores = scores_resp.json() or []
                known = team_a if not _is_unknown_team(team_a) else team_b
                if known:
                    for sc in scores:
                        home = sc.get("home_team") or ""
                        away = sc.get("away_team") or ""
                        if _teams_match(known, home) or _teams_match(known, away):
                            matches.append(sc)
        except Exception:
            pass

        if not matches:
            # Use events endpoint
            known = team_a if not _is_unknown_team(team_a) else team_b
            if known:
                for ev in events:
                    home = ev.get("home_team") or ""
                    away = ev.get("away_team") or ""
                    if _teams_match(known, home) or _teams_match(known, away):
                        if event_date:
                            commence = ev.get("commence_time")
                            if commence and "T" in commence:
                                dpart = commence.split("T")[0]
                                tpart = commence.split("T")[1]
                                try:
                                    from datetime import datetime as _dt
                                    from zoneinfo import ZoneInfo as _ZI
                                    dt_utc = _dt.strptime(f"{dpart} {tpart[:8]}", "%Y-%m-%d %H:%M:%S").replace(tzinfo=_ZI("UTC"))
                                    dt_pt = dt_utc.astimezone(_ZI("America/Los_Angeles"))
                                    if dt_pt.strftime("%Y-%m-%d") != event_date:
                                        continue
                                except Exception:
                                    if dpart != event_date:
                                        continue
                        matches.append(ev)
            except Exception:
                pass

        # Prefer the closest to midday if multiple on same day
        def _parse_iso(ts: str) -> Optional[datetime]:
            try:
                if ts.endswith("Z"):
                    ts = ts[:-1]
                return datetime.strptime(ts, "%Y-%m-%dT%H:%M:%S")
            except Exception:
                return None

        midday = None
        if event_date:
            try:
                midday = datetime.strptime(f"{event_date} 12:00:00", "%Y-%m-%d %H:%M:%S")
            except Exception:
                midday = None

        if len(matches) > 1 and midday is not None:
            def time_distance(ev):
                ts = ev.get("commence_time")
                if not ts:
                    return float('inf')
                dt = _parse_iso(ts)
                return abs((dt - midday).total_seconds()) if dt else float('inf')
            matches = [min(matches, key=time_distance)]

        if matches:
            ev = matches[0]
            home = ev.get("home_team") or ""
            away = ev.get("away_team") or ""
            if _teams_match(team_a or "", home):
                return _format_nickname(team_a), away
            if _teams_match(team_a or "", away):
                return _format_nickname(team_a), home
            if _teams_match(team_b or "", home):
                return away, _format_nickname(team_b)
            if _teams_match(team_b or "", away):
                return home, _format_nickname(team_b)
            return (away if not _is_unknown_team(away) else home), _format_nickname(team_b)
    except Exception:
        return _format_nickname(team_a), _format_nickname(team_b)


def _format_nickname(name: Optional[str]) -> str:
    """Format team name to nickname with fallback."""
    try:
        nm = (name or "").strip()
        from .data_validation import clean_team_name as _clean
        cleaned = _clean(nm)
        return cleaned if cleaned else "Unknown"
    except Exception:
        return (name or "Unknown").strip() or "Unknown"

