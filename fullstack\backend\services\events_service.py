# fullstack/backend/services/events_service.py
from typing import <PERSON><PERSON>, <PERSON><PERSON>
from .db_inference import fill_missing_teams_from_db
from .odds_api import fill_missing_teams_with_odds_api
from .teams import _teams_match, team_to_nickname
from data_science_modules.planet_scale_port import get_connection, generate_admin_event_id


def fill_missing_teams(league: str, event_date: Optional[str], team_a: Optional[str], team_b: Optional[str]) -> Tuple[str, str]:
    db_a, db_b = fill_missing_teams_from_db(league, event_date, team_a, team_b)
    if db_a is not None or db_b is not None:
        final_a = team_to_nickname(db_a if db_a is not None else team_a)
        final_b = team_to_nickname(db_b if db_b is not None else team_b)
        return final_a, final_b
    a1, b1 = fill_missing_teams_with_odds_api(league, event_date, team_a, team_b)
    if a1 and b1 and (a1.lower() not in ("unknown", "null", "none", "")) and (b1.lower() not in ("unknown", "null", "none", "")):
        return a1, b1
    normalized_a = a1 if a1 and a1.strip() else team_to_nickname(team_a)
    normalized_b = b1 if b1 and b1.strip() else team_to_nickname(team_b)
    return normalized_a, normalized_b


def _migrate_event_and_predictions(old_event_id: str, new_event_id: str, new_team_a: str, new_team_b: str) -> None:
    try:
        with get_connection() as conn:
            cur = conn.cursor()
            cur.execute("SELECT 1 FROM events WHERE event_id=%s", (new_event_id,))
            new_exists = cur.fetchone() is not None
            if new_exists:
                cur.execute(
                    """
                    SELECT expert_name, prediction, confidence, team, league, game_date, stat_threshold, prediction_time
                    FROM expert_predictions WHERE event_id=%s
                    """,
                    (old_event_id,),
                )
                rows = cur.fetchall() or []
                for (expert_name, prediction, confidence, team, league, game_date, stat_threshold, prediction_time) in rows:
                    cur.execute(
                        """
                        INSERT INTO expert_predictions
                        (event_id, expert_name, prediction, confidence, team, league, game_date, stat_threshold, prediction_time)
                        VALUES (%s,%s,%s,%s,%s,%s,%s,%s,%s)
                        ON DUPLICATE KEY UPDATE
                          prediction=VALUES(prediction),
                          confidence=VALUES(confidence),
                          team=VALUES(team),
                          league=VALUES(league),
                          game_date=VALUES(game_date),
                          stat_threshold=VALUES(stat_threshold),
                          prediction_time=VALUES(prediction_time)
                        """,
                        (new_event_id, expert_name, prediction, confidence, team, league, game_date, stat_threshold, prediction_time),
                    )
                cur.execute("SELECT crowd_probability FROM crowd_predictions WHERE event_id=%s", (old_event_id,))
                cp = cur.fetchone()
                if cp:
                    (crowd_probability,) = cp
                    cur.execute(
                        """
                        INSERT INTO crowd_predictions (event_id, crowd_probability)
                        VALUES (%s, %s)
                        ON DUPLICATE KEY UPDATE crowd_probability=VALUES(crowd_probability)
                        """,
                        (new_event_id, crowd_probability),
                    )
                cur.execute("DELETE FROM expert_predictions WHERE event_id=%s", (old_event_id,))
                cur.execute("DELETE FROM crowd_predictions WHERE event_id=%s", (old_event_id,))
                cur.execute("DELETE FROM events WHERE event_id=%s", (old_event_id,))
            else:
                cur.execute("UPDATE expert_predictions SET event_id=%s WHERE event_id=%s", (new_event_id, old_event_id))
                cur.execute("UPDATE crowd_predictions  SET event_id=%s WHERE event_id=%s", (new_event_id, old_event_id))
                cur.execute(
                    "UPDATE events SET event_id=%s, team_a=%s, team_b=%s WHERE event_id=%s",
                    (new_event_id, new_team_a, new_team_b, old_event_id),
                )
            cur.execute("UPDATE events SET team_a=%s, team_b=%s WHERE event_id=%s", (new_team_a, new_team_b, new_event_id))
            cur.execute(
                """
                UPDATE expert_predictions 
                SET team=%s 
                WHERE event_id=%s AND (team IS NULL OR team='' OR LOWER(team) IN ('unknown','null','none','other'))
                """,
                (new_team_a, new_event_id),
            )
            conn.commit()
    except Exception:
        return


def cleanup_chart_generation_predictions():
    try:
        with get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("DELETE FROM expert_predictions WHERE expert_name = %s", ("ChartGeneration",))
            conn.commit()
    except Exception:
        pass


def cleanup_unknown_team_rows(limit: int = 100) -> dict:
    details: list[dict] = []
    updated = 0
    migrated = 0
    checked = 0
    with get_connection() as conn:
        cur = conn.cursor()
        cur.execute(
            """
            SELECT event_id, event_date, league, team_a, team_b, pick_type
            FROM events
            WHERE pick_type IN ('MoneyLine','Spread')
              AND (
                    team_a IS NULL OR team_b IS NULL OR
                    LOWER(team_a) IN ('', 'unknown', 'null', 'none', 'other') OR
                    LOWER(team_b) IN ('', 'unknown', 'null', 'none', 'other')
              )
            ORDER BY event_date DESC
            LIMIT %s
            """,
            (int(limit),),
        )
        rows = cur.fetchall() or []
        for (event_id, event_date, league, team_a, team_b, pick_type) in rows:
            checked += 1
            try:
                new_a, new_b = fill_missing_teams(league or "", event_date, team_a, team_b)
                if (new_a or new_b) and ((new_a or '').strip() != (team_a or '').strip() or (new_b or '').strip() != (team_b or '').strip()):
                    cur2 = conn.cursor()
                    cur2.execute("UPDATE events SET team_a=%s, team_b=%s WHERE event_id=%s", (new_a, new_b, event_id))
                    cur2.execute(
                        """
                        UPDATE expert_predictions
                        SET team=%s
                        WHERE event_id=%s AND (team IS NULL OR team='' OR LOWER(team) IN ('unknown','null','none','other'))
                        """,
                        (new_a, event_id),
                    )
                    try:
                        corrected_id = generate_admin_event_id(
                            event_date, league or (event_id or "").split("-")[3], new_a, new_b
                        )
                        if corrected_id and corrected_id != event_id:
                            _migrate_event_and_predictions(event_id, corrected_id, new_a, new_b)
                            migrated += 1
                        updated += 1
                        details.append({"event_id": event_id, "new_team_a": new_a, "new_team_b": new_b})
                    except Exception:
                        pass
            except Exception as e:
                details.append({"event_id": event_id, "error": str(e)})
    return {
        "success": True,
        "checked": checked,
        "updated": updated,
        "migrated": migrated,
        "details": details,
    }

