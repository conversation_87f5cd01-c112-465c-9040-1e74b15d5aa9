/**
 * DateService - Utility for managing application-wide date settings
 * Provides methods to get and update the default date setting used across the application
 */

interface DateSettingsResponse {
  success: boolean;
  date?: string;
  auto_update?: boolean;
  message?: string;
  error?: string;
  warning?: string;
  code?: string;
  details?: string;
  last_updated?: string;
  previous_date?: string;
  new_date?: string;
}

interface UpdateDateRequest {
  date: string;
  admin_password: string;
  auto_update?: boolean;
}

interface ValidationResult {
  isValid: boolean;
  error?: string;
}

class DateService {
  private static readonly DEFAULT_FALLBACK_DATE = "2025-07-20";
  private static readonly API_BASE = "/api/settings";
  private static readonly REQUEST_TIMEOUT = 10000; // 10 seconds
  private static readonly MAX_RETRIES = 3;

  /**
   * Validate date format on client side
   * @param date - Date string to validate
   * @returns Validation result with error message if invalid
   */
  private validateDateFormat(date: string): ValidationResult {
    if (!date) {
      return { isValid: false, error: "Date is required" };
    }

    if (typeof date !== "string") {
      return { isValid: false, error: "Date must be a string" };
    }

    // Check YYYY-MM-DD format
    const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
    if (!dateRegex.test(date)) {
      return {
        isValid: false,
        error:
          "Invalid date format. Please use YYYY-MM-DD format (e.g., 2025-07-20)",
      };
    }

    // Validate it's a real date
    const parsedDate = new Date(date);
    if (isNaN(parsedDate.getTime())) {
      return {
        isValid: false,
        error: "Invalid date. Please enter a valid date.",
      };
    }

    // Check if the parsed date matches the input (handles invalid dates like 2025-02-30)
    if (parsedDate.toISOString().slice(0, 10) !== date) {
      return {
        isValid: false,
        error: "Invalid date. Please enter a valid date.",
      };
    }

    // Additional business logic validation
    const year = parsedDate.getFullYear();
    if (year < 2020 || year > 2030) {
      return { isValid: false, error: "Date must be between 2020 and 2030" };
    }

    // Check if date is not too far in the past or future
    const today = new Date();
    const daysDiff = Math.floor(
      (parsedDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24)
    );

    if (daysDiff < -365) {
      return {
        isValid: false,
        error: "Date cannot be more than 1 year in the past",
      };
    }

    if (daysDiff > 365) {
      return {
        isValid: false,
        error: "Date cannot be more than 1 year in the future",
      };
    }

    return { isValid: true };
  }

  /**
   * Create fetch request with timeout and error handling
   * @param url - Request URL
   * @param options - Fetch options
   * @returns Promise with timeout handling
   */
  private async fetchWithTimeout(
    url: string,
    options: RequestInit = {}
  ): Promise<Response> {
    const controller = new AbortController();
    const timeoutId = setTimeout(
      () => controller.abort(),
      DateService.REQUEST_TIMEOUT
    );

    try {
      const response = await fetch(url, {
        ...options,
        signal: controller.signal,
        headers: {
          "Content-Type": "application/json",
          ...options.headers,
        },
      });

      clearTimeout(timeoutId);
      return response;
    } catch (error) {
      clearTimeout(timeoutId);

      if (error instanceof Error && error.name === "AbortError") {
        throw new Error(
          "Request timed out. Please check your connection and try again."
        );
      }

      throw error;
    }
  }

  /**
   * Retry logic for API calls
   * @param operation - Function to retry
   * @param retries - Number of retries remaining
   * @returns Promise with retry logic
   */
  private async withRetry<T>(
    operation: () => Promise<T>,
    retries: number = DateService.MAX_RETRIES
  ): Promise<T> {
    try {
      return await operation();
    } catch (error) {
      if (retries > 0 && this.isRetryableError(error)) {
        console.warn(
          `❌ API call failed, retrying... (${
            DateService.MAX_RETRIES - retries + 1
          }/${DateService.MAX_RETRIES})`,
          error
        );
        await this.delay(1000 * (DateService.MAX_RETRIES - retries + 1)); // Exponential backoff
        return this.withRetry(operation, retries - 1);
      }
      throw error;
    }
  }

  /**
   * Check if error is retryable
   * @param error - Error to check
   * @returns True if error should be retried
   */
  private isRetryableError(error: unknown): boolean {
    if (error instanceof TypeError && error.message.includes("fetch")) {
      return true; // Network errors
    }

    if (error instanceof Error && error.message.includes("timed out")) {
      return true; // Timeout errors
    }

    return false;
  }

  /**
   * Delay utility for retry logic
   * @param ms - Milliseconds to delay
   * @returns Promise that resolves after delay
   */
  private delay(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  /**
   * Get the current default date setting from the backend with comprehensive error handling
   * @returns Promise that resolves to the current date string (YYYY-MM-DD format)
   */
  async getCurrentDate(): Promise<string> {
    try {
      const response = await this.withRetry(async () => {
        return this.fetchWithTimeout(`${DateService.API_BASE}/date`);
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data: DateSettingsResponse = await response.json();

      // Log warnings if present
      if (data.warning) {
        console.warn("⚠️ DateService warning:", data.warning);
      }

      // Always return a valid date, even if there were warnings
      const returnedDate = data.date || this.getDefaultDate();

      // Validate the returned date
      const validation = this.validateDateFormat(returnedDate);
      if (!validation.isValid) {
        console.error(
          "❌ Invalid date received from server:",
          returnedDate,
          validation.error
        );
        return this.getDefaultDate();
      }

      return returnedDate;
    } catch (error) {
      console.error("❌ Error fetching current date setting:", error);

      // Provide specific error messages for different failure types
      if (error instanceof TypeError && error.message.includes("fetch")) {
        console.error("❌ Network error - using fallback date");
      } else if (
        error instanceof Error &&
        error.message.includes("timed out")
      ) {
        console.error("❌ Request timeout - using fallback date");
      } else {
        console.error("❌ Unexpected error - using fallback date");
      }

      // Always return fallback date on any error
      return this.getDefaultDate();
    }
  }

  /**
   * Update the default date setting with comprehensive validation and error handling
   * @param newDate - New date in YYYY-MM-DD format
   * @param adminPassword - Admin password for authentication
   * @returns Promise that resolves to success/error response with detailed information
   */
  async updateDate(
    newDate: string,
    adminPassword: string,
    autoUpdate?: boolean
  ): Promise<{
    success: boolean;
    message: string;
    code?: string;
    details?: any;
  }> {
    try {
      // Client-side validation first
      const validation = this.validateDateFormat(newDate);
      if (!validation.isValid) {
        return {
          success: false,
          message: validation.error || "Invalid date format",
          code: "CLIENT_VALIDATION_ERROR",
        };
      }

      // Validate admin password
      if (!adminPassword || typeof adminPassword !== "string") {
        return {
          success: false,
          message: "Admin password is required",
          code: "MISSING_PASSWORD",
        };
      }

      if (adminPassword.length < 5) {
        return {
          success: false,
          message: "Admin password is too short",
          code: "INVALID_PASSWORD",
        };
      }

      const requestBody: UpdateDateRequest = {
        date: newDate.trim(),
        admin_password: adminPassword,
        ...(autoUpdate !== undefined ? { auto_update: autoUpdate } : {}),
      };

      console.log("🔄 Updating date setting:", newDate);

      const response = await this.withRetry(async () => {
        return this.fetchWithTimeout(`${DateService.API_BASE}/date`, {
          method: "POST",
          body: JSON.stringify(requestBody),
        });
      });

      const data: DateSettingsResponse = await response.json();

      // Handle different HTTP status codes
      if (!response.ok) {
        const errorMessage = this.getErrorMessageFromResponse(
          response.status,
          data
        );
        return {
          success: false,
          message: errorMessage,
          code: data.code || `HTTP_${response.status}`,
          details: data.details,
        };
      }

      if (!data.success) {
        return {
          success: false,
          message:
            data.error || data.message || "Failed to update date setting",
          code: data.code || "SERVER_ERROR",
          details: data.details,
        };
      }

      console.log("✅ Date updated successfully:", data.new_date || newDate);

      return {
        success: true,
        message: data.message || "Date updated successfully",
        details: {
          previous_date: data.previous_date,
          new_date: data.new_date,
          updated_at: new Date().toISOString(),
        },
      };
    } catch (error) {
      console.error("❌ Error updating date setting:", error);

      // Provide specific error messages for different failure types
      if (error instanceof TypeError && error.message.includes("fetch")) {
        return {
          success: false,
          message:
            "Network error. Please check your internet connection and try again.",
          code: "NETWORK_ERROR",
        };
      }

      if (error instanceof Error && error.message.includes("timed out")) {
        return {
          success: false,
          message: "Request timed out. Please try again.",
          code: "TIMEOUT_ERROR",
        };
      }

      if (error instanceof SyntaxError) {
        return {
          success: false,
          message: "Server returned invalid response. Please try again.",
          code: "INVALID_RESPONSE",
        };
      }

      return {
        success: false,
        message:
          error instanceof Error
            ? error.message
            : "An unexpected error occurred",
        code: "UNKNOWN_ERROR",
      };
    }
  }

  /**
   * Get user-friendly error message based on HTTP status and response data
   * @param status - HTTP status code
   * @param data - Response data
   * @returns User-friendly error message
   */
  private getErrorMessageFromResponse(
    status: number,
    data: DateSettingsResponse
  ): string {
    switch (status) {
      case 400:
        return (
          data.error ||
          "Invalid request. Please check your input and try again."
        );
      case 401:
        return (
          data.error ||
          "Invalid admin password. Please check your password and try again."
        );
      case 403:
        return "Access denied. You don't have permission to perform this action.";
      case 404:
        return "Service not found. Please contact support.";
      case 429:
        return "Too many requests. Please wait a moment and try again.";
      case 500:
        return "Server error occurred. Please try again later.";
      case 502:
      case 503:
      case 504:
        return "Service temporarily unavailable. Please try again later.";
      default:
        return data.error || `Server error (${status}). Please try again.`;
    }
  }

  /**
   * Get the current date in California timezone (Pacific Time)
   * @returns The current date string in YYYY-MM-DD format for California
   */
  getDefaultDate(): string {
    try {
      const now = new Date();
      const californiaDate = new Date(
        now.toLocaleString("en-US", { timeZone: "America/Los_Angeles" })
      );

      const year = californiaDate.getFullYear();
      const month = String(californiaDate.getMonth() + 1).padStart(2, "0");
      const day = String(californiaDate.getDate()).padStart(2, "0");

      return `${year}-${month}-${day}`;
    } catch (error) {
      console.error("❌ Error getting California date, using fallback:", error);
      return DateService.DEFAULT_FALLBACK_DATE;
    }
  }

  /**
   * Check if the service is available
   * @returns Promise that resolves to service availability status
   */
  async checkServiceHealth(): Promise<{
    available: boolean;
    message: string;
    responseTime?: number;
  }> {
    const startTime = Date.now();

    try {
      const response = await this.fetchWithTimeout(
        `${DateService.API_BASE}/date`
      );
      const responseTime = Date.now() - startTime;

      if (response.ok) {
        return {
          available: true,
          message: "Service is available",
          responseTime,
        };
      } else {
        return {
          available: false,
          message: `Service returned ${response.status}: ${response.statusText}`,
          responseTime,
        };
      }
    } catch (error) {
      const responseTime = Date.now() - startTime;

      return {
        available: false,
        message:
          error instanceof Error ? error.message : "Service check failed",
        responseTime,
      };
    }
  }

  /**
   * Get detailed service status for debugging
   * @returns Promise with comprehensive service information
   */
  async getServiceStatus(): Promise<{
    service: string;
    available: boolean;
    fallback_date: string;
    current_date?: string;
    last_check: string;
    response_time?: number;
    error?: string;
  }> {
    const lastCheck = new Date().toISOString();

    try {
      const healthCheck = await this.checkServiceHealth();

      if (healthCheck.available) {
        const currentDate = await this.getCurrentDate();

        return {
          service: "DateService",
          available: true,
          fallback_date: this.getDefaultDate(),
          current_date: currentDate,
          last_check: lastCheck,
          response_time: healthCheck.responseTime,
        };
      } else {
        return {
          service: "DateService",
          available: false,
          fallback_date: this.getDefaultDate(),
          last_check: lastCheck,
          response_time: healthCheck.responseTime,
          error: healthCheck.message,
        };
      }
    } catch (error) {
      return {
        service: "DateService",
        available: false,
        fallback_date: DateService.DEFAULT_FALLBACK_DATE,
        last_check: lastCheck,
        error: error instanceof Error ? error.message : "Unknown error",
      };
    }
  }

  public async getSettings(): Promise<{ date: string; auto_update: boolean }> {
    try {
      const response = await this.withRetry(async () =>
        this.fetchWithTimeout(`${DateService.API_BASE}/date`)
      );
      if (!response.ok)
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      const data: DateSettingsResponse = await response.json();
      const returnedDate = data.date || this.getDefaultDate();
      const autoUpdate =
        data.auto_update !== undefined ? data.auto_update : true;
      const validation = this.validateDateFormat(returnedDate);
      if (!validation.isValid) {
        return { date: this.getDefaultDate(), auto_update: autoUpdate };
      }
      return { date: returnedDate, auto_update: autoUpdate };
    } catch {
      return { date: this.getDefaultDate(), auto_update: true };
    }
  }
}

// Export a singleton instance
export const dateService = new DateService();

// Export the class for testing purposes
export { DateService };
