# fullstack/backend/routes/placeholders.py
from flask import Blueprint

from flask import jsonify, request


@placeholders_bp.route('/handicappers', methods=['GET'])
def get_handicappers():
    return jsonify({
        "success": True,
        "message": "Handicappers endpoint placeholder",
        "handicappers": []
    })


@placeholders_bp.route('/handicappers/<int:handicapper_id>', methods=['GET'])
def get_handicapper_profile(handicapper_id):
    return jsonify({
        "success": True,
        "message": f"Handicapper profile for {handicapper_id} (placeholder)",
        "handicapper": {
            "id": handicapper_id,
            "name": f"Handicapper #{handicapper_id}",
            "win_rate": 0.0,
            "picks": []
        }
    })


@placeholders_bp.route('/favorites', methods=['GET'])
def get_favorites():
    return jsonify({
        "success": True,
        "message": "Favorites retrieved (placeholder)",
        "favorites": []
    })


@placeholders_bp.route('/favorites', methods=['POST'])
def save_favorites():
    return jsonify({
        "success": True,
        "message": "Favorites saved (placeholder)",
        "favorites": []
    })

placeholders_bp = Blueprint('placeholders', __name__)

