#!/usr/bin/env sh
set -eu

# Usage:
#   ./clear_todays_events.sh            # uses today's date (YYYY-MM-DD)
#   ./clear_todays_events.sh 2025-08-22 # uses provided date
#   ./clear_todays_events.sh today      # explicit today

DATE_ARG="${1:-today}"

if [ "$DATE_ARG" = "today" ]; then
    TARGET_DATE="$(date +%F)"
else
    TARGET_DATE="$DATE_ARG"
fi

SCRIPT_DIR="$(cd "$(dirname "$0")" && pwd)"

python - "$SCRIPT_DIR" "$TARGET_DATE" <<'PYCODE'
import os
import sys
from datetime import datetime

script_dir = sys.argv[1]
target_date = sys.argv[2]

sys.path.insert(0, os.path.join(script_dir, 'fullstack', 'backend'))

try:
    from data_science_modules.planet_scale_port import get_connection
except ModuleNotFoundError:
    sys.path.insert(0, script_dir)
    from fullstack.backend.data_science_modules.planet_scale_port import get_connection

conn = get_connection()
cur = conn.cursor()

# validate date format (YYYY-MM-DD)
try:
    datetime.strptime(target_date, '%Y-%m-%d')
except ValueError:
    raise SystemExit("Error: date must be in YYYY-MM-DD format or 'today'")

cur.execute('DELETE FROM events WHERE event_date = %s', (target_date,))
events_deleted = cur.rowcount

cur.execute('DELETE FROM expert_predictions WHERE game_date = %s', (target_date,))
expert_deleted = cur.rowcount

conn.commit()

print(f'Cleared date {target_date}: events={events_deleted}, expert_predictions={expert_deleted}')
PYCODE


