# fullstack/backend/routes/model.py
from flask import Blueprint, jsonify
from modules.HandiCapperAccuracyModel import main_model

model_bp = Blueprint('model', __name__)


@model_bp.route("/main_model/<event_id>", methods=["POST"])
def get_main_model_prediction(event_id):
    """
    API endpoint to get main model prediction including team_kde_prob for a specific event.

    Args:
        event_id (str): The event ID to get prediction for.

    Returns:
        JSON response containing the main model prediction results including team_kde_prob.
    """
    try:
        print(f"🔍 Running main_model for event_id: {event_id}")

        # Call the main_model function from HandiCapperAccuracyModel
        result = main_model(event_id)

        if "error" in result:
            return jsonify({
                "success": False,
                "message": f"Model prediction failed: {result['error']}",
                "team_kde_prob": None
            }), 400

        # Extract team_kde_prob and other model results
        team_kde_prob = result.get("team_kde_prob")

        return jsonify({
            "success": True,
            "event_id": event_id,
            "team_kde_prob": team_kde_prob,
            "combined_prob": result.get("combined_prob"),
            "bayesian_prob": result.get("bayesian_prob"),
            "logistic_prob": result.get("logistic_prob"),
            "quality_score": result.get("quality_score"),
            "predicted_class": result.get("predicted_class")
        })

    except Exception as e:
        print(f"❌ Error in /api/main_model endpoint for {event_id}: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({
            "success": False,
            "message": f"Error running main model: {str(e)}",
            "team_kde_prob": None
        }), 500
