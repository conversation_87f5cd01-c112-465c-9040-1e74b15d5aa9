# fullstack/backend/routes/external.py
import os
import json
import re
import threading
from collections import defaultdict
from flask import Blueprint, request, jsonify
from werkzeug.exceptions import BadRequest

from fullstack.backend.services.auth import require_api_key
from fullstack.backend.services.data_validation import validate_and_clean_pick_data
from fullstack.backend.services.confidence import calculate_dynamic_confidence_vs_expert
from fullstack.backend.services.teams import _teams_match
from fullstack.backend.services.odds_api import resolve_sport_key
from data_science_modules.planet_scale_port import (
    get_connection, submit_event, generate_admin_event_id
)
from modules.HandiCapperAccuracyModel import main_model

external_bp = Blueprint('external', __name__)


@external_bp.route("/external/insert_picks", methods=["POST"])
@require_api_key
def insert_picks():
    """
    External API endpoint for inserting sports prediction data.
    Requires X-API-Key header for authentication.

    Expected JSON payload:
    {
        "picks": [
            {
                "league": "NBA|MLB|NFL|NHL",
                "team_a": "string",
                "team_b": "string",
                "pick_type": "MoneyLine|Spread|Prop",
                "player_name": "string (optional)",
                "stat_type": "string (optional)",
                "stat_threshold": "number (optional)",
                "odds": "string (+150, -110, etc.)",
                "prediction": "0|1",
                "expert_name": "string",
                "expert_confidence": "number (0.0-1.0)",
                "event_date": "YYYY-MM-DD",
                "prediction_time": "YYYY-MM-DD HH:MM:SS (optional)"
            }
        ]
    }

    Returns:
    {
        "success": true|false,
        "message": "string",
        "results": {
            "total_picks": "number",
            "successful_inserts": "number",
            "failed_inserts": "number",
            "events_created": "number",
            "events_updated": "number"
        },
        "errors": [
            {
                "pick_index": "number",
                "error": "string",
                "details": "string"
            }
        ]
    }
    """
    try:
        # Ultra-robust JSON parsing with Make.com specific fixes
        data = _parse_json_with_fixes(request)
        if isinstance(data, tuple):  # Error response
            return data

        picks = data.get("picks", [])
        if not _validate_picks_array(picks):
            return jsonify({
                "success": False,
                "message": "Invalid picks array"
            }), 400

        print(f"[INFO] External API: Processing {len(picks)} picks from {request.remote_addr}")

        # Initialize response tracking
        results = {
            "total_picks": len(picks),
            "successful_inserts": 0,
            "failed_inserts": 0,
            "events_created": 0,
            "events_updated": 0
        }
        errors = []

        # Process each pick
        for pick_index, pick_data in enumerate(picks):
            success, result_data, error_data = _process_single_pick(
                pick_index, pick_data
            )
            
            if success:
                results["successful_inserts"] += 1
                if result_data.get("event_created"):
                    results["events_created"] += 1
                else:
                    results["events_updated"] += 1
            else:
                results["failed_inserts"] += 1
                if error_data:
                    errors.append(error_data)

        # Fire-and-forget odds refresh for today's events
        _dispatch_background_odds_refresh()

        # Determine overall success
        overall_success = results["failed_inserts"] == 0
        
        if overall_success:
            message = f"Successfully processed all {results['total_picks']} picks"
        else:
            message = f"Processed {results['successful_inserts']}/{results['total_picks']} picks successfully"

        print(f"[INFO] External API completed: {message}")

        return jsonify({
            "success": overall_success,
            "message": message,
            "results": results,
            "errors": errors
        })

    except Exception as e:
        import traceback
        error_traceback = traceback.format_exc()
        print(f"[ERROR] External API endpoint error: {e}")
        print(f"[ERROR] Full traceback: {error_traceback}")
        return jsonify({
            "success": False,
            "message": "Internal server error",
            "results": {
                "total_picks": 0,
                "successful_inserts": 0,
                "failed_inserts": 0,
                "events_created": 0,
                "events_updated": 0
            },
            "errors": [{
                "pick_index": -1,
                "error": "Internal server error",
                "details": str(e)
            }]
        }), 500


def _parse_json_with_fixes(request):
    """Parse JSON with Make.com specific fixes."""
    try:
        if not request.is_json and request.content_type != 'application/json':
            print(f"[WARNING] Invalid content type: {request.content_type}")
        return request.get_json(force=True, silent=False)
    except (UnicodeDecodeError, ValueError, BadRequest) as json_error:
        print(f"[ERROR] JSON parsing failed: {json_error}")
        raw_data = request.get_data(as_text=True)
        print(f"[DEBUG] Raw request data (first 500 chars): {repr(raw_data[:500]) if raw_data else 'None'}")

        try:
            if raw_data:
                # Step 1: Fix the critical Make.com issues
                raw_data = re.sub(r'"stat_threshold":\s*,', '"stat_threshold": null,', raw_data)
                raw_data = re.sub(r'"stat_threshold":\s*}', '"stat_threshold": null}', raw_data)

                # Step 2: Fix escaped quotes in string values
                raw_data = raw_data.replace('\\"', '"')
                raw_data = raw_data.replace('"\\""', '""')

                # Step 3: Fix any remaining structural issues
                raw_data = re.sub(r',\s*}', '}', raw_data)
                raw_data = re.sub(r',\s*]', ']', raw_data)

                print(f"[DEBUG] Cleaned JSON: {repr(raw_data[:500])}")
                data = json.loads(raw_data)
                print("[DEBUG] Fallback JSON parsing succeeded")
                return data
            else:
                raise ValueError("No data received")
        except Exception as fallback_error:
            print(f"[ERROR] All JSON repair attempts failed: {fallback_error}")
            return jsonify({
                "success": False,
                "message": "Invalid JSON format in request body",
                "error_type": "json_parse_error",
                "error_details": str(fallback_error),
                "raw_data_preview": raw_data[:200] if raw_data else "No data"
            }), 400


def _validate_picks_array(picks):
    """Validate picks array structure."""
    if not picks:
        return False
    if not isinstance(picks, list):
        return False
    return True


def _process_single_pick(pick_index, pick_data):
    """Process a single pick and return success, result_data, error_data."""
    try:
        print(f"[INFO] Processing pick {pick_index + 1}: {pick_data}")

        # Validate and clean pick data
        cleaned_data, validation_errors = validate_and_clean_pick_data(pick_data)
        
        if validation_errors:
            print(f"[ERROR] Validation failed for pick {pick_index}: {validation_errors}")
            return False, None, {
                "pick_index": pick_index,
                "error": "Validation failed",
                "details": "; ".join(validation_errors)
            }

        # Generate event ID and teams based on pick type
        event_id, team_a, team_b, player_team = _generate_event_details(cleaned_data)
        print(f"[INFO] Generated event_id: {event_id}")

        # Submit event to database
        success, message = submit_event(
            event_id=event_id,
            event_date=cleaned_data['event_date'],
            league=cleaned_data['league'],
            team_a=team_a,
            team_b=team_b,
            crowd_probability=cleaned_data.get('crowd_probability', 0.5),
            expert_predictions=[],  # Handle separately
            actual_result=None,
            pick_type=cleaned_data['pick_type'],
            player_team=player_team,
            stat_type=cleaned_data.get('stat_type', ''),
            player_name=cleaned_data.get('player_name'),
            stat_threshold=cleaned_data.get('stat_threshold')
        )

        # Handle expert predictions
        expert_success = False
        if success or "already exists" in message.lower():
            expert_success = _insert_expert_prediction(event_id, cleaned_data, player_team)

        # Determine overall success for this pick
        pick_success = success or ("already exists" in message.lower() and expert_success)
        
        if pick_success:
            result_data = {"event_created": success}
            return True, result_data, None
        else:
            return False, None, {
                "pick_index": pick_index,
                "error": "Database insertion failed",
                "details": message
            }

    except Exception as pick_error:
        import traceback
        pick_traceback = traceback.format_exc()
        print(f"[ERROR] Failed to process pick {pick_index}: {pick_error}")
        print(f"[ERROR] Pick processing traceback: {pick_traceback}")
        return False, None, {
            "pick_index": pick_index,
            "error": str(pick_error),
            "details": f"Error processing pick data: {pick_data}",
            "traceback": pick_traceback
        }


def _generate_event_details(cleaned_data):
    """Generate event ID and team details based on pick type."""
    if cleaned_data.get('pick_type') in ['Prop', 'Total']:
        # For prop picks and team totals, use stat-based event ID
        event_id = generate_admin_event_id(
            event_date=cleaned_data['event_date'],
            league=cleaned_data['league'],
            pick_type=cleaned_data['pick_type'],
            team_a=cleaned_data.get('team_a', ''),
            team_b=cleaned_data.get('team_b', ''),
            player_name=cleaned_data.get('player_name', ''),
            stat_threshold=cleaned_data['stat_threshold'],
            stat_type=cleaned_data['stat_type']
        )
        team_a = "Over"
        team_b = "Under"
        player_team = cleaned_data.get('team_a', '')
    else:
        # For standard game picks
        team_a = cleaned_data.get('team_a')
        team_b = cleaned_data.get('team_b')
        event_id = generate_admin_event_id(
            event_date=cleaned_data['event_date'],
            league=cleaned_data['league'],
            pick_type=cleaned_data['pick_type'],
            team_a=team_a,
            team_b=team_b,
            player_name=None,
            stat_threshold=None,
            stat_type=None
        )
        player_team = team_a
    
    return event_id, team_a, team_b, player_team


def _insert_expert_prediction(event_id, cleaned_data, player_team):
    """Insert or update expert prediction for an event."""
    try:
        conn = get_connection()
        cursor = conn.cursor()

        expert_name = cleaned_data['expert_name']
        prediction = int(cleaned_data.get('prediction', 0))
        initial_confidence = cleaned_data.get('expert_confidence', 0.75)

        # Determine the team for this pick
        if cleaned_data.get('pick_type') == 'Prop':
            pick_team = player_team if player_team not in ['Over', 'Under', ''] else cleaned_data.get('team_a', '')
        else:
            pick_team = cleaned_data.get('team_a', '')

        game_date = cleaned_data.get('event_date')
        prediction_time = cleaned_data.get('prediction_time')

        # Check if expert prediction already exists
        cursor.execute(
            "SELECT event_id FROM expert_predictions WHERE event_id = %s AND expert_name = %s",
            (event_id, expert_name)
        )
        existing_prediction = cursor.fetchone()

        if existing_prediction:
            # Update existing expert prediction
            cursor.execute(
                """
                UPDATE expert_predictions SET
                    prediction = %s, confidence = %s, team = %s,
                    league = %s, game_date = %s, stat_threshold = %s,
                    prediction_time = %s
                WHERE event_id = %s AND expert_name = %s
                """,
                (prediction, initial_confidence, pick_team,
                 cleaned_data['league'], game_date,
                 cleaned_data.get('stat_threshold'),
                 prediction_time, event_id, expert_name)
            )
        else:
            # Insert new expert prediction
            cursor.execute(
                """
                INSERT INTO expert_predictions (
                    event_id, expert_name, prediction, confidence,
                    team, league, game_date, stat_threshold, prediction_time
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                """,
                (event_id, expert_name, prediction, initial_confidence,
                 pick_team, cleaned_data['league'], game_date,
                 cleaned_data.get('stat_threshold'), prediction_time)
            )

        # Calculate dynamic confidence
        try:
            dynamic_confidence_percentage = calculate_dynamic_confidence_vs_expert(
                event_id, initial_confidence * 100
            )
            if dynamic_confidence_percentage != 50.0:  # 50.0 is fallback value
                dynamic_confidence_decimal = dynamic_confidence_percentage / 100.0
                cursor.execute(
                    "UPDATE expert_predictions SET confidence = %s WHERE event_id = %s AND expert_name = %s",
                    (dynamic_confidence_decimal, event_id, expert_name)
                )
        except Exception as conf_error:
            print(f"[WARNING] Dynamic confidence calculation failed: {conf_error}")

        # Ensure expert exists in reliability table
        cursor.execute(
            "SELECT 1 FROM expert_reliability WHERE expert_name = %s",
            (expert_name,)
        )
        if not cursor.fetchone():
            cursor.execute(
                "INSERT INTO expert_reliability (expert_name) VALUES (%s)",
                (expert_name,)
            )

        conn.commit()
        conn.close()
        return True
        
    except Exception as ep_error:
        print(f"[ERROR] Failed to insert expert prediction: {ep_error}")
        return False


def _dispatch_background_odds_refresh():
    """Fire-and-forget odds refresh and model run for today's events."""
    try:
        def _refresh_todays_odds_and_rerun_model():
            # Implementation moved to services/odds_api.py
            from fullstack.backend.services.odds_api import refresh_todays_odds_and_models
            refresh_todays_odds_and_models()

        threading.Thread(target=_refresh_todays_odds_and_rerun_model, daemon=True).start()
    except Exception as bg_error:
        print(f"[WARNING] Failed to dispatch background odds refresh: {bg_error}")


@external_bp.route("/external/mlb_projections", methods=["POST"])
@require_api_key
def insert_mlb_projections():
    """
    External API endpoint for inserting/updating MLB projection sheet data.
    Requires X-API-Key header for authentication.

    Expected JSON payload:
    {
        "projections": [
            {
                "game_date": "YYYY-MM-DD",
                "away_team": "string",
                "home_team": "string",
                "away_sp": "string",
                "home_sp": "string",
                "away_score": decimal,
                "home_score": decimal,
                "total_runs_scored": decimal,
                "home_team_run_diff": decimal,
                "winning_team": "string",
                "game_xHRs": decimal,
                "home_win_pct": decimal,
                "xRFI": decimal,
                "player_name": "string",
                "is_pitcher": 0|1,
                "AB": decimal,
                "Hits": decimal,
                "Singles": decimal,
                "Doubles": decimal,
                "Triples": decimal,
                "Homeruns": decimal,
                "TB": decimal,
                "Walks": decimal,
                "SB": decimal,
                "Runs": decimal,
                "RBIs": decimal,
                "HRR": decimal,
                "OBP": decimal,
                "HitterStrikeouts": decimal,
                "Pitches": decimal,
                "Outs": decimal,
                "BF": decimal,
                "Strikeouts": decimal,
                "PitcherWalks": decimal,
                "ER": decimal,
                "HA": decimal,
                "xRFI_Chance": decimal
            }
        ]
    }

    Returns:
    {
        "success": true|false,
        "message": "string",
        "results": {
            "total_projections": number,
            "successful_inserts": number,
            "successful_updates": number,
            "failed_operations": number
        },
        "errors": [...]
    }
    """
    try:
        from fullstack.backend.services.mlb_projections import repair_json_data, process_mlb_projections

        # Ultra-robust JSON parsing with Make.com specific fixes
        try:
            if not request.is_json and request.content_type != 'application/json':
                print(f"[WARNING] Invalid content type: {request.content_type}")
            data = request.get_json(force=True, silent=False)
        except (UnicodeDecodeError, ValueError, BadRequest) as json_error:
            print(f"[ERROR] JSON parsing failed: {json_error}")
            raw_data = request.get_data(as_text=True)
            print(f"[DEBUG] Raw request data (first 500 chars): {repr(raw_data[:500]) if raw_data else 'None'}")

            try:
                if raw_data:
                    print(f"[DEBUG] Cleaned JSON: {repr(raw_data[:500])}")
                    data = json.loads(repair_json_data(raw_data))
                    print("[DEBUG] Fallback JSON parsing succeeded")
                else:
                    raise ValueError("No data received")
            except Exception as fallback_error:
                print(f"[ERROR] All JSON repair attempts failed: {fallback_error}")
                return jsonify({
                    "success": False,
                    "message": "Invalid JSON format in request body",
                    "error_type": "json_parse_error",
                    "error_details": str(fallback_error),
                    "raw_data_preview": raw_data[:200] if raw_data else "No data"
                }), 400

        if not data:
            return jsonify({
                "success": False,
                "message": "No JSON data provided or empty request body"
            }), 400

        # Accept either a top-level array of projection objects or an object with a 'projections' array.
        if isinstance(data, list):
            projections = data
        elif isinstance(data, dict):
            projections = data.get("projections", [])
        else:
            return jsonify({
                "success": False,
                "message": "Request body must be a JSON array or an object with 'projections'",
                "error_type": "json_parse_error"
            }), 400

        if isinstance(projections, dict):
            if "projections" in projections:
                projections = projections["projections"]
            else:
                projections = [projections]

        if not projections:
            return jsonify({
                "success": False,
                "message": "No projections provided in request"
            }), 400

        if not isinstance(projections, list):
            return jsonify({
                "success": False,
                "message": "Projections must be an array"
            }), 400

        print(f"[INFO] MLB Projections API: Processing {len(projections)} projections from {request.remote_addr}")

        # Process projections using the service
        result = process_mlb_projections(projections)
        results = result["results"]
        errors = result["errors"]
        processing_errors = result["processing_errors"]

        total_ok = results["successful_inserts"] + results["successful_updates"]
        success = results["failed_operations"] == 0
        message = (
            f"Successfully processed all {results['total_projections']} projections"
            if success else
            f"Processed {total_ok}/{results['total_projections']} projections")
        print(f"[INFO] {message}")

        return jsonify(
            success=success,
            message=message,
            results=results,
            errors=errors + processing_errors,
        )

    except Exception as e:
        print(f"[ERROR] {e}")
        return jsonify(success=False, message="Internal server error"), 500


@external_bp.route("/external/todays_games", methods=["GET"])
@require_api_key
def external_todays_games():
    """
    Returns a deduplicated list of today's unique games by league with optional start times when available.

    Query params:
      - date (optional, YYYY-MM-DD). Defaults to current default date.
    """
    try:
        from fullstack.backend.services.events_service import _fetch_unique_games_by_date
        from fullstack.backend.services.settings_service import get_default_date
        from datetime import datetime

        custom_date = request.args.get('date')
        if custom_date:
            try:
                datetime.strptime(custom_date, "%Y-%m-%d")
                target_date = custom_date
            except ValueError:
                return jsonify({
                    "success": False,
                    "message": "Invalid date format. Use YYYY-MM-DD.",
                    "games": []
                }), 400
        else:
            target_date = get_default_date()

        games = _fetch_unique_games_by_date(target_date)

        # Build response without performing any time lookups
        enriched = []
        flattened_rows = []
        for g in games:
            league = g.get('league')
            ta = g.get('team_a')
            tb = g.get('team_b')
            enriched.append({
                'league': league,
                'team_a': ta,
                'team_b': tb,
                'game_date': target_date
            })
            gd = target_date or ''
            # Single-line CSV-style entry per game (no headers), rows separated by '; '
            flattened_rows.append(f"{league},{ta},{tb},{gd}")

        return jsonify({
            'success': True,
            'message': f"Retrieved {len(enriched)} unique games for {target_date}",
            'date': target_date,
            'games': enriched,
            'games_flattened_str': "; ".join(flattened_rows)
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f"Error retrieving games: {str(e)}",
            'games': []
        }), 500
