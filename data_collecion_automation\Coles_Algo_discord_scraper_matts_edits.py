import discord
import requests

TOKEN = "MTM5NzI4ODg3NzM5OTYwNTI1OQ.GbjChY.WQAyO6uGlmctv_m5g8YH8c7KXoI1ZCRs07J6pU"
WEBHOOK_URL = "https://hook.us2.make.com/d7a6ulpg26gw4zbwsmykqx44v1yahilp"
TARGET_CHANNEL_IDS = [
    "1358106129611100309",  # 🚨official-picks🚨
    "1358106091761700956",  # 🏀nba-sheets🏀
    "1358105984224198818",  # ⚾mlb-sheets⚾
    "1364336675546726520",  # 🏒nhl-sheets🏒
    "1380264720082276523",  # 💬premium-chat💬
    "1358105866204872906",  # winnings-screenshots
    "1358106853485314299",  # testimonial
    "1358106364139802714",  # general
    "1358118720278368598",  # community_slips-and-live_bets
    "1358106251065819367",  # mlb-chat
    "1358106269814358276",  # nba-chat
    "1358106330929561610",  # nhl-chat
    "1358110319687241840",  # random-chat
    "1359355196395880544",  # bad-beats
    "1365127288877875282",  # nfl-chat
    "1361160617029013627",  # soccer-chat
    "1358106306401013887",  # college-chat
    "1380045834376450099",  # tennis-chat
    "1358868108772638781",  # props-cash-request
    "1359618861439123566",  # gambly-bot-requests
    "1390510994501009429"   # 🔒community-pod🔒
]

intents = discord.Intents.default()
intents.message_content = True
intents.messages = True
client = discord.Client(intents=intents)

@client.event
async def on_ready():
    print(f"Logged in as {client.user} - Ready to forward images to webhook")

@client.event
async def on_message(message):
    if message.author.bot:
        return

    if str(message.channel.id) in TARGET_CHANNEL_IDS:
        # Send all images directly through webhook without processing
        if message.attachments:
            for attachment in message.attachments:
                if attachment.content_type and attachment.content_type.startswith('image/'):
                    data = {
                        "channel": message.channel.name,
                        "content": message.content,
                        "timestamp": message.created_at.isoformat(),
                        "image_url": attachment.url,
                        "image_filename": attachment.filename
                    }
                    
                    try:
                        print(f"Sending image to webhook: {attachment.filename}")
                        response = requests.post(WEBHOOK_URL, json=data)
                        response.raise_for_status()
                        print(f"Successfully sent to Make: {attachment.filename}")
                    except requests.exceptions.RequestException as e:
                        print(f"Error sending to Make.com: {e}")



client.run(TOKEN)