# fullstack/backend/routes/picks.py
from flask import Blueprint, jsonify, request
from fullstack.backend import state
from modules.ppObjects import Pick
import sqlite3
from data_science_modules.planet_scale_port import generate_event_id
from fullstack.backend.services.confidence import confidence_score

picks_bp = Blueprint('picks', __name__)


@picks_bp.route('/get_picks', methods=['GET'])
def get_picks():
    objs = getattr(state, 'pick_objects', [])
    try:
        if isinstance(objs, dict):
            objs_list = list(objs.values())
        else:
            objs_list = objs
        serialized = []
        for p in objs_list:
            try:
                serialized.append(p.to_dict())
            except Exception:
                serialized.append(p)
        return jsonify({"objects": serialized})
    except Exception as e:
        return jsonify({"objects": [], "error": str(e)}), 500


@picks_bp.route('/clear_picks', methods=['POST'])
def clear_picks():
    try:
        if hasattr(state, 'reset_state'):
            state.reset_state()
        else:
            if isinstance(getattr(state, 'pick_objects', None), dict):
                state.pick_objects = {}
            else:
                state.pick_objects = []
            if hasattr(state, 'next_id'):
                state.next_id = 1
        try:
            from modules.ppObjects import Pick
            Pick.pID_counter = 0
        except Exception:
            pass
        return jsonify({"message": "All picks cleared.", "objects": getattr(state, 'pick_objects', [])})

@picks_bp.route('/load_user_picks', methods=['POST'])
def load_user_picks():
    try:
        data = request.get_json()
        if not data:
            return jsonify({"message": "No data provided.", "objects": []}), 400
        user_picks = data.get("picks", [])
        state.pick_objects = []
        try:
            Pick.pID_counter = 0
        except Exception:
            pass
        for pick_data in user_picks:
            try:
                new_pick = Pick(
                    name=pick_data.get("playerName", "Unknown Player"),
                    odds=1.5,
                    confidence=pick_data.get("confidence", 75),
                    mutual_exclusion_group=-1,
                    league=["Unknown"],
                    event_id=pick_data.get("id", f"user_pick_{len(state.pick_objects)}"),
                    bayesian_prob=0.5,
                    logistic_prob=0.5,
                    bayesian_conf=0.5,
                    stat_type=pick_data.get("betType", "Unknown"),
                    reusable=True,
                    capital_limit=0
                )
                state.pick_objects.append(new_pick)
            except Exception as e:
                print(f"Error processing pick {pick_data}: {e}")
                continue
        return jsonify({
            "message": f"Loaded {len(state.pick_objects)} user picks successfully.",
            "objects": [p.to_dict() for p in state.pick_objects]
        })
    except Exception as e:
        return jsonify({"message": f"Error loading user picks: {str(e)}", "objects": []}), 500


@picks_bp.route('/edit', methods=['POST'])
def edit():
    data = request.get_json()
    obj_id = data.get("id")
    for obj in state.pick_objects:
        if getattr(obj, 'pID', None) == obj_id:
            obj.name = data.get("name", obj.name)
            obj.decimalOdds = float(data.get("odds", obj.decimalOdds))
            obj.pick_origin = data.get("pick_origin", obj.pick_origin)
            obj.league = data.get("league", obj.league)
            obj.reusable = data.get("reusable", obj.reusable)
            obj.capital_limit = int(data.get("capital_limit", obj.capital_limit))
            obj.gameID = int(data.get("mutual_exclusion", obj.gameID))
            obj.pick_type = data.get("pick_type", obj.pick_type)
            obj.player_team = data.get("player_team", obj.player_team)
            obj.stat_type = data.get("stat_type", obj.stat_type)

            name = obj.name
            odds = obj.decimalOdds
            leagues = obj.league
            pick_origins = obj.pick_origin
            pick_type = obj.pick_type
            player_team = obj.player_team
            stat_type = obj.stat_type

            if pick_type == "MoneyLine":
                team_a = name
                team_b = "Other"
                player_team = "None"
            else:
                team_a = "Over"
                team_b = "Under"

            implied_prob = round(1 / odds, 4)
            today = __import__('datetime').datetime.today().strftime("%Y-%m-%d")

            expert_predictions = []
            total_score = 0
            from fullstack.backend.app import origin_profiles  # keep behavior
            for origin_obj in pick_origins:
                origin = origin_obj.get("name")
                origin_conf = origin_obj.get("confidence", None)
                prediction = origin_obj.get("prediction", 1)
                origin_accuracy = origin_profiles[origin]() if callable(origin_profiles[origin]) else origin_profiles[origin]
                norm_conf = origin_conf / 100 if origin_conf is not None else None
                expert_predictions.append((origin, prediction, norm_conf))
                used_conf = origin_conf if origin_conf is not None else 75.0
                score = confidence_score(odds, used_conf, origin_accuracy)
                total_score += score

            final_score = round(total_score / len(expert_predictions), 2) if expert_predictions else 0
            obj.confidence = None
            obj.confidence_score = final_score

            for league in leagues:
                event_id = generate_event_id(name, league)
                obj.event_id = event_id
                try:
                    import sqlite3
                    conn = sqlite3.connect(state.DB_PATH)
                    cursor = conn.cursor()
                    cursor.execute(
                        """
                        INSERT OR REPLACE INTO events (
                            event_id, event_date, league,
                            team_a, team_b, actual_result,
                            pick_type, player_team, stat_type
                        ) VALUES (?, ?, ?, ?, ?, COALESCE(
                            (SELECT actual_result FROM events WHERE event_id = ?), NULL
                        ), ?, ?, ?)
                        """,
                        (event_id, today, league, team_a, team_b, event_id, pick_type, player_team, stat_type)
                    )
                    cursor.execute(
                        """
                        INSERT OR REPLACE INTO crowd_predictions (event_id, crowd_probability)
                        VALUES (?, ?)
                        """,
                        (event_id, implied_prob)
                    )
                    cursor.execute("DELETE FROM expert_predictions WHERE event_id = ?", (event_id,))
                    for origin, prediction, confidence in expert_predictions:
                        if origin.lower() == "chartgeneration":
                            continue
                        cursor.execute(
                            """
                            INSERT OR REPLACE INTO expert_predictions (event_id, expert_name, prediction, confidence)
                            VALUES (?, ?, ?, ?)
                            """,
                            (event_id, origin, prediction, confidence)
                        )
                        cursor.execute("SELECT 1 FROM expert_reliability WHERE expert_name = ?", (origin,))
                        if not cursor.fetchone():
                            cursor.execute("INSERT INTO expert_reliability (expert_name) VALUES (?)", (origin,))
                    conn.commit()
                    conn.close()
                except Exception as e:
                    print(f"[ERROR] DB Error while editing {event_id}: {e}")
            break
    return jsonify({"objects": [p.__dict__ for p in state.pick_objects]})


@picks_bp.route('/delete', methods=['POST'])
def delete():
    data = request.get_json()
    obj_id = data.get("id")
    deleted_pick = None
    for obj in list(state.pick_objects):
        try:
            if isinstance(obj, dict) and obj.get("id") == obj_id:
                deleted_pick = obj
                break
        except Exception:
            pass
    if deleted_pick:
        name = deleted_pick.get("name")
        leagues = deleted_pick.get("league", [])
        for league in leagues:
            event_id = generate_event_id(name, league)
            try:
                import sqlite3
                conn = sqlite3.connect(state.DB_PATH)
                cursor = conn.cursor()
                cursor.execute("DELETE FROM expert_predictions WHERE event_id = ?", (event_id,))
                cursor.execute("DELETE FROM crowd_predictions WHERE event_id = ?", (event_id,))
                cursor.execute("DELETE FROM events WHERE event_id = ?", (event_id,))
                conn.commit()
                conn.close()
            except Exception as e:
                print(f"DB error while deleting event {event_id}: {e}")
        state.pick_objects = [obj for obj in state.pick_objects if not (isinstance(obj, dict) and obj.get("id") == obj_id)]
    return jsonify({"objects": [p.to_dict() for p in state.pick_objects]})


@picks_bp.route('/update_accuracy', methods=['POST'])
def update_accuracy():
    data = request.get_json()
    try:
        state.user_accuracy = float(data.get("accuracy", 0.0))
    except ValueError:
        state.user_accuracy = 0.0
    return jsonify({"message": f"Accuracy updated to {state.user_accuracy}", "user_accuracy": state.user_accuracy})

    try:
        # Prefer a centralized reset helper if present
        if hasattr(state, 'reset_state'):
            state.reset_state()
        else:
            # Best-effort fallback depending on current shape
            if isinstance(getattr(state, 'pick_objects', None), dict):
                state.pick_objects = {}
            else:
                state.pick_objects = []
            if hasattr(state, 'next_id'):
                state.next_id = 1

        # Try to reset Pick class counter if available (non-fatal)
        try:
            from modules.ppObjects import Pick
            Pick.pID_counter = 0
        except Exception:
            pass

        return jsonify({"message": "All picks cleared.", "objects": getattr(state, 'pick_objects', [])})
    except Exception as e:
        return jsonify({"success": False, "message": str(e)}), 500


@picks_bp.route('/edit', methods=['POST'])
def edit():
    """Edit an existing pick object and update the database."""
    from datetime import datetime
    from fullstack.backend.services.confidence import confidence_score

    data = request.get_json()
    obj_id = data.get("id")

    pick_objects = getattr(state, 'pick_objects', [])

    for obj in pick_objects:
        if obj.pID == obj_id:
            # Update fields on the Pick object
            obj.name = data.get("name", obj.name)
            obj.decimalOdds = float(data.get("odds", obj.decimalOdds))
            obj.pick_origin = data.get("pick_origin", obj.pick_origin)
            obj.league = data.get("league", obj.league)
            obj.reusable = data.get("reusable", obj.reusable)
            obj.capital_limit = int(data.get("capital_limit", obj.capital_limit))
            obj.gameID = int(data.get("mutual_exclusion", obj.gameID))
            obj.pick_type = data.get("pick_type", obj.pick_type)
            obj.player_team = data.get("player_team", obj.player_team)
            obj.stat_type = data.get("stat_type", obj.stat_type)

            name = obj.name
            odds = obj.decimalOdds
            leagues = obj.league
            pick_origins = obj.pick_origin
            pick_type = obj.pick_type
            player_team = obj.player_team
            stat_type = obj.stat_type

            # Determine team_a and team_b based on pick_type
            if pick_type == "MoneyLine":
                team_a = name
                team_b = "Other"
                player_team = "None"
            else:
                team_a = "Over"
                team_b = "Under"

            implied_prob = round(1 / odds, 4)
            today = datetime.today().strftime("%Y-%m-%d")

            # Recalculate expert prediction score
            expert_predictions = []
            total_score = 0

            for origin_obj in pick_origins:
                origin = origin_obj.get("name")
                origin_conf = origin_obj.get("confidence", None)
                prediction = origin_obj.get("prediction", 1)  # default to Higher

                origin_accuracy = state.origin_profiles[origin]() if callable(
                    state.origin_profiles[origin]) else state.origin_profiles[origin]
                norm_conf = origin_conf / 100 if origin_conf is not None else None

                expert_predictions.append((origin, prediction, norm_conf))

                used_conf = origin_conf if origin_conf is not None else 75.0
                score = confidence_score(odds, used_conf, origin_accuracy)
                total_score += score

            final_score = round(total_score / len(expert_predictions), 2) if expert_predictions else 0
            obj.confidence = None
            obj.confidence_score = final_score

            # Update the database
            for league in leagues:
                event_id = generate_event_id(name, league)
                obj.event_id = event_id

                try:
                    conn = sqlite3.connect(state.DB_PATH)
                    cursor = conn.cursor()

                    cursor.execute(
                        """
                        INSERT OR REPLACE INTO events (
                            event_id, event_date, league,
                            team_a, team_b, actual_result,
                            pick_type, player_team, stat_type
                        ) VALUES (?, ?, ?, ?, ?, COALESCE(
                            (SELECT actual_result FROM events WHERE event_id = ?), NULL
                        ), ?, ?, ?)
                    """, (event_id, today, league, team_a, team_b, event_id,
                          pick_type, player_team, stat_type))

                    cursor.execute(
                        """
                        INSERT OR REPLACE INTO crowd_predictions (event_id, crowd_probability)
                        VALUES (?, ?)
                    """, (event_id, implied_prob))

                    cursor.execute(
                        "DELETE FROM expert_predictions WHERE event_id = ?",
                        (event_id, ))
                    for origin, prediction, confidence in expert_predictions:
                        # Prevent phantom ChartGeneration predictions
                        if origin.lower() == "chartgeneration":
                            continue

                        cursor.execute(
                            """
                            INSERT OR REPLACE INTO expert_predictions (event_id, expert_name, prediction, confidence)
                            VALUES (?, ?, ?, ?)
                        """, (event_id, origin, prediction, confidence))

                        cursor.execute(
                            "SELECT 1 FROM expert_reliability WHERE expert_name = ?",
                            (origin, ))
                        if not cursor.fetchone():
                            cursor.execute(
                                """
                                INSERT INTO expert_reliability (expert_name)
                                VALUES (?)
                            """, (origin, ))

                    conn.commit()
                    conn.close()

                except Exception as e:
                    print(f"[ERROR] DB Error while editing {event_id}: {e}")

            break

    return jsonify({"objects": [p.__dict__ for p in pick_objects]})