# Project Parlay - Cursor Rules

You are an AI assistant working on Project Parlay, a sports betting analytics platform.

## Project Architecture
- Frontend: React/TypeScript/Vite in `fullstack/frontend/`
- Backend: Python Flask/FastAPI in `fullstack/backend/`
- Database: PlanetScale MySQL via `planet_scale_port.py`
- Automation: Discord scrapers in `data_collection_automation/`

## CRITICAL RULES

### File Management
- NEVER create new files unless explicitly building a new feature
- ALWAYS edit existing files when possible
- Use inline Python commands for one-off database operations: `python -c "..."`

### Code Style
- Indentation: 4 spaces (NEVER tabs)
- Functions/variables: snake_case
- Classes: PascalCase
- Imports at top: standard → third-party → local
- No debug prints, console.logs, or unnecessary logging

### Code Quality
- Minimal, working solutions only
- No extra "fluff" code
- Proactive fixes without asking permission
- Follow existing patterns in the codebase

## Key APIs & Database

### External API Endpoints
- `/api/external/mlb_projections` - MLB projection data (POST, upsert logic)
- `/api/external/insert_picks` - Sports picks with odds-to-confidence mapping

### Database Tables
- `mlb_projection_sheet` - Unique on (game_date, away_team, home_team, player_name)
- `events`, `expert_predictions`, `crowd_predictions`

### Database Pattern
```python
from fullstack.backend.modules.planet_scale_port import get_connection
conn = get_connection()
cursor = conn.cursor()
# Use INSERT ... ON DUPLICATE KEY UPDATE for upserts
```

## Development Workflow

### Frontend
- Components: `fullstack/frontend/src/components/`
- Contexts: `fullstack/frontend/src/contexts/`
- Services: `fullstack/frontend/src/services/`
- Styling: TailwindCSS utilities
- Testing: `npm run lint`

### Backend
- Routes: `fullstack/backend/routes/`
- Services: `fullstack/backend/services/`
- Data Science: `fullstack/backend/data_science_modules/`
- Testing: `pytest`

## Security
- NO hardcoded secrets or API keys
- Use environment variables from `.env`
- Validate and sanitize all inputs
- Follow principle of least privilege

## Git Workflow
- Branch: `feature-description-yourname`
- Clear commit messages
- Tag @hartel for review

## Testing Requirements
- Frontend: Run `npm run lint`
- Backend: Run `pytest`
- Minimum 3 base + 3 edge cases per function

## User Preferences
- Wants minimal, efficient solutions
- Prefers proactive implementation
- Dislikes unnecessary file creation
- Values clean, working code

## Remember
1. Edit don't create
2. Minimal solutions
3. No debug output
4. Follow patterns
5. Be proactive
