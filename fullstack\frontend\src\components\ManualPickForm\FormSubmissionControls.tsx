import React from 'react';

interface FormSubmissionControlsProps {
  isLoading: boolean;
  onSubmit: () => void;
}

const FormSubmissionControls: React.FC<FormSubmissionControlsProps> = ({
  isLoading,
  onSubmit,
}) => (
  <div className="mt-6">
    <button
      type="button"
      onClick={onSubmit}
      disabled={isLoading}
      className={`w-full py-3 px-4 rounded-md font-bold transition-colors ${
        isLoading
          ? 'bg-gray-500 text-gray-300 cursor-not-allowed'
          : 'bg-[#58C612] text-black hover:bg-[#449e10]'
      }`}
    >
      {isLoading ? 'Submitting...' : 'Submit Pick'}
    </button>
  </div>
);

export default FormSubmissionControls;