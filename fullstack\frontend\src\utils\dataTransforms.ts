import { ManualPick, ExpertConfidence } from "../types/manualPick";
import { EventData } from "./api";

// Extended ExpertConfidence interface for backend submission
interface ExpertConfidenceWithThreshold extends ExpertConfidence {
  stat_threshold?: number;
}

// Utility functions for stat type normalization, direction detection and formatting
// Detect "Over" or "Under" tokens inside a raw stat type string (client-side cleaning)
export const detectDirectionInStatType = (
  rawStatType: string | undefined | null
): { direction: "Over" | "Under" | null; cleaned: string } => {
  if (!rawStatType) return { direction: null, cleaned: "" };
  // Capture only a standalone Over/Under token once
  const directionMatch = rawStatType.match(/\b(over|under)\b/i);
  const direction = directionMatch
    ? directionMatch[1].toLowerCase() === "over"
      ? "Over"
      : "Under"
    : null;

  // Remove any Over/Under tokens (even if repeated), common connectors, and trim
  const cleaned = rawStatType
    .replace(/\b(over|under)\b/gi, " ")
    .replace(/\bvs\b/gi, " ")
    .replace(/\bo\/u\b/gi, " ")
    .replace(/\s+/g, " ")
    .trim();
  return { direction, cleaned };
};

// Utility functions for stat type normalization and formatting
export const normalizeStatType = (statType: string): string => {
  // Convert to lowercase and trim whitespace for database storage
  return statType.trim().toLowerCase();
};

// Helper function to format team names from ALL CAPS to proper case
export const formatTeamName = (teamName: string): string => {
  if (!teamName) return teamName;

  // Convert from ALL CAPS to proper case
  return teamName
    .toLowerCase()
    .split(" ")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(" ");
};

export const formatStatTypeForDisplay = (statType: string): string => {
  // Convert database stat type (lowercase) to proper case for display
  if (!statType) return "Points"; // Default fallback

  const normalized = statType.trim().toLowerCase();

  // Handle common stat type mappings (including legacy uppercase variants)
  const statTypeMap: Record<string, string> = {
    // Lowercase (database format)
    points: "Points",
    assists: "Assists",
    rebounds: "Rebounds",
    blocks: "Blocks",
    steals: "Steals",
    turnovers: "Turnovers",
    pra: "PRA",
    "p+a": "P+A",
    "p+r": "P+R",
    "r+a": "R+A",
    "b+s": "B+S",
    "fg made": "FG Made",
    "fg attempted": "FG Attempted",
    "ft made": "FT Made",
    "ft attempted": "FT Attempted",
    "3-pt's": "3-pt's",
    "3-pt's attempted": "3-pt's Attempted",
    fpts: "Fpts",
    "d rebounds": "D Rebounds",
    "o rebounds": "O Rebounds",
    dunks: "Dunks",
    // Legacy uppercase variants (for backward compatibility)
    POINTS: "Points",
    ASSISTS: "Assists",
    REBOUNDS: "Rebounds",
    BLOCKS: "Blocks",
    STEALS: "Steals",
    TURNOVERS: "Turnovers",
    PRA: "PRA",
    PTS: "Points",
    AST: "Assists",
    REB: "Rebounds",
    BLK: "Blocks",
    STL: "Steals",
    TO: "Turnovers",
    TOV: "Turnovers",
    // Mixed case variants
    Points: "Points",
    Assists: "Assists",
    Rebounds: "Rebounds",
  };

  // For single letter abbreviations, keep them as-is (uppercase)
  if (statType.length === 1) {
    return statType.toUpperCase();
  }

  // Return mapped value or capitalize first letter as fallback
  return (
    statTypeMap[normalized] ||
    statTypeMap[statType] || // Check original case for legacy data
    statType.charAt(0).toUpperCase() + statType.slice(1).toLowerCase()
  );
};

// Friendly labels for stat types by league
const getFriendlyStatLabel = (
  rawStatType?: string,
  league?: string
): string => {
  if (!rawStatType) return "";
  const key = rawStatType.trim().toUpperCase();
  const leagueKey = (league || "").toUpperCase();

  const common: Record<string, string> = {
    PTS: "Points",
    REB: "Rebounds",
    AST: "Assists",
    STL: "Steals",
    BLK: "Blocks",
    TO: "Turnovers",
    "3PM": "3-Pointers Made",
  };

  const mlb: Record<string, string> = {
    H: "Hits",
    HR: "Home Runs",
    R: "Runs",
    RBI: "RBIs",
    K: "Strikeouts",
    BB: "Walks",
    TB: "Total Bases",
  };

  const nfl: Record<string, string> = {
    PASSTD: "Passing TDs",
    RUSHYD: "Rushing Yards",
    RECYD: "Receiving Yards",
  };

  if (leagueKey === "MLB" && mlb[key]) return mlb[key];
  if (leagueKey === "NFL" && nfl[key]) return nfl[key];
  if (common[key]) return common[key];

  // Fallback to existing formatter
  const fallback = formatStatTypeForDisplay(rawStatType);
  if (leagueKey === "MLB") {
    const minimal: Record<string, string> = {
      H: "Hits",
      HR: "Home Runs",
      K: "Strikeouts",
      BB: "Walks",
      R: "Runs",
      RBI: "RBIs",
    };
    return minimal[fallback.toUpperCase()] || fallback;
  }
  return fallback;
};

// Helper to sanitize stat_type and optionally extract embedded direction and strip thresholds
export const getCleanStatTypeAndDirection = (
  raw?: string | null
): { label: string; direction: "Over" | "Under" | null } => {
  if (!raw) return { label: "", direction: null };
  const { direction, cleaned } = detectDirectionInStatType(raw);
  // Remove standalone numbers (e.g., 7.5) that sometimes leak into stat_type
  const noNums = (cleaned || "").replace(/\b\d+(?:\.\d+)?\b/g, "");
  // Remove phrases that imply totals/comparisons
  const strippedComparators = noNums
    .replace(/\b(over|under)\b/gi, " ")
    .replace(/\b(versus|vs\.?|v\.)\b/gi, " ")
    .replace(/\s+/g, " ")
    .trim();

  let label = "";
  if (
    strippedComparators &&
    !["over", "under", "o", "u"].includes(strippedComparators.toLowerCase())
  ) {
    // Normalize common total wording to "Total"
    if (/\btotal\b/i.test(strippedComparators)) {
      label = "Total";
    } else {
      label = formatStatTypeForDisplay(strippedComparators);
    }
  }
  return { label, direction };
};

export const formatManualPickForSubmission = (
  formData: ManualPick,
  oddsData: { odds: number; format: string; sign: string; source_name: string },
  customInputs: Record<number, string>,
  customPredictionInput: string
): ManualPick & {
  admin_password?: string;
  crowd_probability: number;
  source_name: string;
  // Ensure backend expects these fields for manual picks
  odds_format: string;
  odds_sign: string;
  event_id: string; // Add event_id to the type
} => {
  const CUSTOM_SENTINEL = "__CUSTOM__";

  // 1. Convert confidence from percentage (0-100) to decimal (0.0-1.0)
  // and add global stat_threshold to all experts
  const formattedPickOrigin: ExpertConfidenceWithThreshold[] =
    formData.pick_origin.map((expert, index) => {
      const resolvedName =
        expert.name === CUSTOM_SENTINEL
          ? customInputs[index] || ""
          : expert.name;
      const confidenceAsDecimal = expert.confidence / 100;

      return {
        ...expert,
        name: resolvedName,
        confidence: confidenceAsDecimal,
        stat_threshold: formData.stat_threshold, // Use global stat_threshold for all experts
      };
    });

  // 2. Generate event_id: "date-league-player-stat-threshold"
  // Example: "2025-07-20-MLB-S.Ohtani-home_runs-2"

  // Date
  const eventDate = formData.eventDate || "YYYY-MM-DD"; // Fallback, though it should always be present

  // League (assume single selection from frontend)
  const league =
    formData.league.length > 0 ? formData.league[0].toUpperCase() : "UNKNOWN";

  // Player Name: Convert to a URL-friendly format
  const playerNameRaw = formData.name || "UNKNOWN";
  const playerName = playerNameRaw
    .replace(/\s+/g, "_")
    .replace(/[^a-zA-Z0-9_.]/g, ""); // Basic sanitization

  // Stat Type: Normalize and format
  const statTypeRaw =
    formData.stat_type === CUSTOM_SENTINEL
      ? customInputs[-2] || "custom"
      : formData.stat_type; // Adjusted index for custom stat type
  const statType = normalizeStatType(statTypeRaw).replace(/\s+/g, "_"); // and URL-friendly

  // Stat Threshold: Handle optional and format nicely if present
  const statThreshold =
    formData.stat_threshold !== undefined && formData.stat_threshold !== null
      ? `-${formData.stat_threshold}`
      : "";

  const event_id = `${eventDate}-${league}-${playerName}-${statType}${statThreshold}`;

  // Resolve custom pick_type, stat_type, and prediction if applicable
  const resolvedPickType =
    formData.pick_type === CUSTOM_SENTINEL
      ? ((customInputs[-1] || "Custom") as "Prop" | "Custom")
      : formData.pick_type;

  const resolvedStatType = normalizeStatType(
    formData.stat_type === CUSTOM_SENTINEL
      ? customInputs[-2] || "custom"
      : formData.stat_type
  );

  const resolvedPrediction =
    formData.prediction === CUSTOM_SENTINEL
      ? ((customPredictionInput || "custom") as any) // Assuming backend can handle string for custom
      : formData.prediction;

  // Calculate implied probability for crowd prediction (already happening in ManualPickForm)
  const calculateImpliedProbability = (
    odds: number,
    format: string
  ): number => {
    if (format === "Decimal") {
      return 1 / odds;
    } else {
      // American odds
      if (odds > 0) {
        return 100 / (odds + 100);
      } else {
        return Math.abs(odds) / (Math.abs(odds) + 100);
      }
    }
  };

  const impliedProbability = calculateImpliedProbability(
    oddsData.odds,
    oddsData.format
  );

  return {
    ...formData,
    pick_origin: formattedPickOrigin,
    event_id: event_id, // Add the generated event_id
    eventDate: eventDate, // Ensure eventDate is explicitly passed
    player_name: formData.name, // Ensure player_name is set from form.name
    pick_type: resolvedPickType,
    stat_type: resolvedStatType,
    prediction: resolvedPrediction,
    odds: oddsData.odds,
    crowd_probability: impliedProbability, // Already calculated in ManualPickForm, but ensure it's here
    source_name: oddsData.source_name || "Manual",
    odds_format: oddsData.format,
    odds_sign: oddsData.sign,
    // admin_password needs to be added later as it's not part of formData
  };
};

// Type for the Pick format expected by PicksView
export interface Pick {
  id: number;
  playerName: string;
  playerNumber: string;
  betType: string;
  gameInfo: string;
  confidence: number;
  expertCount: number;
  additionalExperts: number;
  handicapperNames: string[]; // Add handicapper names for expanded view
}

// Helper function to extract player name from event_id
export const extractPlayerName = (eventId: string): string => {
  // Parse event_id pattern: "2025-07-20-NBA-KTOWNS14.0PTS"
  const parts = eventId.split("-");

  if (parts.length >= 5) {
    const playerPart = parts[4];
    const playerName = playerPart.replace(/\d+(?:\.\d+)?[A-Z]+$/, "");

    if (playerName.length === 0) {
      return "";
    }

    if (playerName.length <= 2) {
      return playerName.split("").join(". ") + ".";
    }

    return formatTeamName(playerName);
  }

  return "Unknown Player";
};

// Helper function to extract player number from event_id or return default
export const extractPlayerNumber = (eventId: string): string => {
  // Try to extract jersey number from the player part of event_id
  const parts = eventId.split("-");
  if (parts.length >= 5) {
    const playerPart = parts[4];
    // Look for number between player name and stat (e.g., "KTOWNS32PTS" -> "32")
    const numberMatch = playerPart.match(/[A-Z]+(\d+)(?:\.\d+)?[A-Z]+$/);
    if (numberMatch) {
      return numberMatch[1];
    }
  }

  // Default fallback
  return "0";
};

export const formatBetType = (event: EventData): string => {
  // Special case for MoneyLine picks
  if (event.pick_type === "MoneyLine" || event.stat_type === "MoneyLine") {
    return "MoneyLine";
  }

  // Unified cleaning: determine direction and label once to avoid duplication like "Over Over 7.5 Total"
  const { label: rawLabel, direction: dirFromStat } =
    getCleanStatTypeAndDirection(event.stat_type);
  // If pick_type is Total, force label to "Total" regardless of stat_type noise
  const baseLabel = event.pick_type === "Total" ? "Total" : rawLabel;
  // For totals: only include team name if it's a true team (not Over/Under placeholder)
  const teamPrefix =
    event.pick_type === "Total" &&
    event.team_a &&
    !/^(over|under)$/i.test(event.team_a.trim())
      ? `${formatTeamName(event.team_a)} `
      : "";

  // First try: Use modern structured data format
  if (event.pick_type && event.stat_threshold && event.predictions) {
    const predictions = event.predictions ?? [];

    if (predictions.length > 0) {
      const overCount = predictions.filter((p) => p === 1).length;
      const underCount = predictions.filter((p) => p === 0).length;
      const inferredDirection = overCount > underCount ? "Over" : "Under";

      let statType = baseLabel;
      if (!statType) {
        const statMatch = event.event_id.match(/([A-Z]+)$/);
        if (statMatch)
          statType = getFriendlyStatLabel(
            statMatch[1],
            (event as any).league || event.event_id.split("-")[3]
          );
      }

      const direction = dirFromStat ?? inferredDirection;
      const friendly = getFriendlyStatLabel(
        statType,
        (event as any).league || event.event_id.split("-")[3]
      );
      const cleanedStatType = friendly ? ` ${friendly}` : "";

      // For Totals, include team when it's a real team
      if (event.pick_type === "Total" && teamPrefix) {
        return `${teamPrefix}${direction} ${event.stat_threshold}${cleanedStatType}`.trim();
      }
      return `${direction} ${event.stat_threshold}${cleanedStatType}`;
    }
  }

  // Second try: Use legacy threshold and predictions arrays
  const thresholds = event.stat_thresholds ?? [];
  const predictions = event.predictions ?? [];

  if (thresholds.length > 0 && predictions.length > 0) {
    const threshold = thresholds[0];

    if (threshold === null || threshold === undefined) {
      // Try to extract threshold from event_id as fallback
      const thresholdMatch = event.event_id.match(/(\d+(?:\.\d+)?)[A-Z]+$/);
      if (thresholdMatch) {
        const extractedThreshold = thresholdMatch[1];
        const overCount = predictions.filter((p) => p === 1).length;
        const underCount = predictions.filter((p) => p === 0).length;
        const inferredDirection = overCount > underCount ? "Over" : "Under";

        let statType = baseLabel;
        if (!statType) {
          const statMatch = event.event_id.match(/([A-Z]+)$/);
          if (statMatch)
            statType = getFriendlyStatLabel(
              statMatch[1],
              (event as any).league || event.event_id.split("-")[3]
            );
        }

        const direction = dirFromStat ?? inferredDirection;
        const friendly = getFriendlyStatLabel(
          statType,
          (event as any).league || event.event_id.split("-")[3]
        );
        const cleanedStatType = friendly ? ` ${friendly}` : "";
        if (event.pick_type === "Total" && teamPrefix) {
          return `${teamPrefix}${direction} ${extractedThreshold}${cleanedStatType}`.trim();
        }
        return `${direction} ${extractedThreshold}${cleanedStatType}`;
      }

      // If we can't extract threshold but have other info, provide what we can
      if (event.stat_type || event.event_id.match(/([A-Z]+)$/)) {
        let statType = "Points";
        if (event.stat_type) {
          statType = formatStatTypeForDisplay(event.stat_type);
        } else {
          const statMatch = event.event_id.match(/([A-Z]+)$/);
          if (statMatch) {
            statType = formatStatTypeForDisplay(statMatch[1]);
          }
        }
        return `${statType}`;
      }

      return "Standard Bet";
    }

    const overCount = predictions.filter((p) => p === 1).length;
    const underCount = predictions.filter((p) => p === 0).length;
    const inferredDirection = overCount > underCount ? "Over" : "Under";

    let statType = baseLabel;
    if (!statType) {
      const statMatch = event.event_id.match(/([A-Z]+)$/);
      if (statMatch)
        statType = getFriendlyStatLabel(
          statMatch[1],
          (event as any).league || event.event_id.split("-")[3]
        );
    }

    const direction = dirFromStat ?? inferredDirection;
    const friendly = getFriendlyStatLabel(
      statType,
      (event as any).league || event.event_id.split("-")[3]
    );
    const cleanedStatType = friendly ? ` ${friendly}` : "";
    if (event.pick_type === "Total" && teamPrefix) {
      return `${teamPrefix}${direction} ${threshold}${cleanedStatType}`.trim();
    }
    return `${direction} ${threshold}${cleanedStatType}`;
  }

  // Third try: Extract as much as we can from event_id alone
  if (event.event_id) {
    // Pattern: "2025-07-20-NBA-KTOWNS14.0PTS" or similar
    const thresholdStatMatch = event.event_id.match(/(\d+(?:\.\d+)?)([A-Z]+)$/);
    if (thresholdStatMatch) {
      const threshold = thresholdStatMatch[1];
      const rawStat = thresholdStatMatch[2];
      const statType = getFriendlyStatLabel(
        rawStat,
        (event as any).league || event.event_id.split("-")[3]
      );

      // If we have predictions, use them to determine direction
      if (predictions && predictions.length > 0) {
        const overCount = predictions.filter((p) => p === 1).length;
        const underCount = predictions.filter((p) => p === 0).length;
        const direction = overCount > underCount ? "Over" : "Under";
        const cleanedStatType = statType ? ` ${statType}` : "";
        return `${direction} ${threshold}${cleanedStatType}`;
      }

      // No predictions, but we have threshold and stat type
      const cleanedStatType = statType ? ` ${statType}` : "";
      return `${threshold}${cleanedStatType}`;
    }

    // Try to extract just stat type without threshold
    const statOnlyMatch = event.event_id.match(/([A-Z]+)$/);
    if (statOnlyMatch) {
      const statType = formatStatTypeForDisplay(statOnlyMatch[1]);
      return `${statType}`;
    }
  }

  // Final fallback: Try using any available stat_type
  if (event.stat_type) {
    const { label } = getCleanStatTypeAndDirection(event.stat_type);
    if (label) return `${label}`;
  }

  // Last resort
  return "Standard Bet";
};

// Helper: robustly parse any incoming timestamp and convert to Pacific Time
const toPacificDate = (input: string): Date | null => {
  if (!input) return null;
  const trimmed = String(input).trim();
  // Handle ISO UTC like 2025-08-13T23:05:00Z
  if (/\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}Z/.test(trimmed)) {
    const d = new Date(trimmed);
    if (isNaN(d.getTime())) return null;
    return d;
  }
  // Handle "YYYY-MM-DD HH:MM:SS" optionally followed by TZ token
  const tzMatch = trimmed.match(
    /^(\d{4}-\d{2}-\d{2})[ T](\d{2}:\d{2}:\d{2})(?:\s+(ET|EST|EDT|CT|CST|CDT|MT|MST|MDT|PT|PST|PDT|America\/[^\s]+|GMT))?$/
  );
  if (tzMatch) {
    const [, datePart, timePart, tz] = tzMatch;

    // Build a Date for a local wall time in a given IANA zone by deriving its UTC offset
    const buildDateInZone = (iana: string): Date | null => {
      try {
        const y = Number(datePart.slice(0, 4));
        const m = Number(datePart.slice(5, 7));
        const d = Number(datePart.slice(8, 10));
        const hh = Number(timePart.slice(0, 2));
        const mm = Number(timePart.slice(3, 5));
        const ss = Number(timePart.slice(6, 8));
        const probeUtc = new Date(Date.UTC(y, m - 1, d, hh, mm, ss));
        const parts = new Intl.DateTimeFormat("en-US", {
          timeZone: iana,
          timeZoneName: "shortOffset",
          year: "numeric",
          month: "2-digit",
          day: "2-digit",
          hour: "2-digit",
          minute: "2-digit",
          second: "2-digit",
          hour12: false,
        }).formatToParts(probeUtc);
        const tzName =
          parts.find((p) => p.type === "timeZoneName")?.value || "GMT-07";
        const match = tzName.match(/GMT([+-]\d{1,2})(?::?(\d{2}))?/i);
        let sign = "-";
        let oh = "07";
        let om = "00";
        if (match) {
          sign = match[1].startsWith("-") ? "-" : "+";
          oh = match[1].replace("+", "").replace("-", "").padStart(2, "0");
          om = (match[2] || "00").padStart(2, "0");
        }
        const isoWithOffset = `${datePart}T${timePart}${sign}${oh}:${om}`;
        const dObj = new Date(isoWithOffset);
        return isNaN(dObj.getTime()) ? null : dObj;
      } catch {
        return null;
      }
    };

    // If no TZ provided, treat naive string as Pacific Time rather than UTC
    if (!tz || tz === "GMT") {
      return buildDateInZone("America/Los_Angeles");
    }

    // Map simple TZ abbreviations to IANA
    const map: Record<string, string> = {
      ET: "America/New_York",
      EST: "America/New_York",
      EDT: "America/New_York",
      CT: "America/Chicago",
      CST: "America/Chicago",
      CDT: "America/Chicago",
      MT: "America/Denver",
      MST: "America/Denver",
      MDT: "America/Denver",
      PT: "America/Los_Angeles",
      PST: "America/Los_Angeles",
      PDT: "America/Los_Angeles",
    };
    const iana = map[tz as keyof typeof map] || tz;
    const zoned = buildDateInZone(iana);
    if (zoned) return zoned;
    // Fallback to interpreting as UTC
    return new Date(`${datePart}T${timePart}Z`);
  }
  // Try generic Date parser
  const d = new Date(trimmed.replace(" GMT", "Z"));
  return isNaN(d.getTime()) ? null : d;
};

const formatPacificTime = (date: Date): string => {
  return new Intl.DateTimeFormat("en-US", {
    timeZone: "America/Los_Angeles",
    hour: "numeric",
    minute: "2-digit",
    hour12: true,
  }).format(date);
};

// Helper function to format game info
export const formatGameInfo = (event: EventData): string => {
  // Extract date from event_id since event_date is null
  const dateParts = event.event_id.split("-");
  let dateStr = "Today";
  let timeStr = "";

  // Use actual prediction_time if available, otherwise fallback to old logic
  if (event.prediction_time) {
    try {
      console.log("🎯 formatGameInfo processing:", event.prediction_time);
      const parsed = toPacificDate(event.prediction_time);
      if (parsed) {
        // format both day and time strictly in Pacific Time
        const dayStr = new Intl.DateTimeFormat("en-US", {
          timeZone: "America/Los_Angeles",
          weekday: "short",
        }).format(parsed);
        timeStr = formatPacificTime(parsed);
        dateStr = timeStr ? `${dayStr}, ${timeStr}` : `${dayStr}`;
      }
    } catch (e) {
      console.log("❌ formatGameInfo caught error:", e);
      console.log(
        "❌ Failed processing prediction_time:",
        event.prediction_time
      );
      // Fall back to old parsing logic if prediction_time is invalid
      if (dateParts.length >= 3) {
        try {
          const [year, month, day] = dateParts.map(Number);
          const date = new Date(Date.UTC(year, month - 1, day, 12, 0, 0));
          const dayStr = new Intl.DateTimeFormat("en-US", {
            timeZone: "America/Los_Angeles",
            weekday: "short",
          }).format(date);
          dateStr = `${dayStr}`;
          timeStr = "";
        } catch (e) {
          dateStr = "Today";
        }
      }
    }
  } else {
    // Original fallback logic when prediction_time is not available
    // Show only day when time is unknown to avoid a misleading 9:00 AM default
    if (dateParts.length >= 3) {
      try {
        const [year, month, day] = dateParts.map(Number);
        const date = new Date(Date.UTC(year, month - 1, day, 12, 0, 0));
        const dayStr = new Intl.DateTimeFormat("en-US", {
          timeZone: "America/Los_Angeles",
          weekday: "short",
        }).format(date);
        dateStr = `${dayStr}`;
        timeStr = "";
      } catch (e) {
        dateStr = "Today";
        timeStr = "";
      }
    }
  }

  // Extract sport from event_id (e.g., "NBA") - it's at index 3, not index 4
  const sport = dateParts.length >= 4 ? dateParts[3] : "NBA";

  // For MoneyLine bets with team_a and team_b, show "Team A vs Team B"
  if (event.team_a && event.team_b) {
    const suffix = timeStr ? `${dateStr}` : `${dateStr}`;
    return `${sport} | ${formatTeamName(event.team_a)} vs ${formatTeamName(
      event.team_b
    )} | ${suffix}`;
  }

  // Use database player_name field instead of parsing event_id
  const playerName = event.player_name || extractPlayerName(event.event_id);
  const suffix2 = timeStr ? `${dateStr}` : `${dateStr}`;
  return `${sport} | ${playerName} | ${suffix2}`;
};

export const calculateConfidence = (predictions?: number[]): number => {
  if (!predictions || predictions.length === 0) return 50;

  const agreementCount = predictions.filter((p) => p === predictions[0]).length;
  const agreementRatio = agreementCount / predictions.length;

  return Math.round(50 + agreementRatio * 45);
};

// New function to get actual confidence from stored values
export const getActualConfidence = (confidenceValues?: number[]): number => {
  if (!confidenceValues || confidenceValues.length === 0) return 50;

  // Calculate average confidence from stored values
  const sum = confidenceValues.reduce((acc, conf) => acc + conf, 0);
  const average = sum / confidenceValues.length;

  // Convert from decimal (0.0-1.0) to percentage (0-100) if needed
  const percentage = average <= 1.0 ? average * 100 : average;

  return Math.round(percentage);
};

// Main conversion function to convert EventData to Pick format expected by PicksView
export const convertEventToPick = (event: EventData, index: number): Pick => {
  // Use database fields instead of parsing event_id
  const playerName = event.player_name || extractPlayerName(event.event_id);
  const playerNumber = extractPlayerNumber(event.event_id);
  // If there is exactly one prediction (from a single expert), use it to avoid
  // majority inference flipping direction.
  const singleExpertPred =
    event.expert_predictions_map &&
    Object.values(event.expert_predictions_map).length === 1
      ? Object.values(event.expert_predictions_map)[0]
      : undefined;
  const eventForFormatting: EventData =
    singleExpertPred === 0 || singleExpertPred === 1
      ? { ...event, predictions: [singleExpertPred] }
      : event;
  const betType = formatBetType(eventForFormatting);
  const gameInfo = formatGameInfo(event);

  // Use actual stored confidence values if available, otherwise fall back to calculated confidence
  const confidence =
    event.confidence_values && event.confidence_values.length > 0
      ? getActualConfidence(event.confidence_values)
      : calculateConfidence(event.predictions);

  const handicappers = event.handicappers ?? [];

  return {
    id: index + 1,
    playerName: playerName,
    playerNumber: playerNumber,
    betType: betType,
    gameInfo: gameInfo,
    confidence: confidence,
    expertCount: Math.min(handicappers.length, 9), // Cap at 9 for display
    additionalExperts: Math.max(0, handicappers.length - 9),
    handicapperNames: handicappers, // Include all handicapper names
  };
};

// Function to convert multiple events to picks
export const convertEventsToPicks = (events: EventData[]): Pick[] => {
  return events.map((event, index) => convertEventToPick(event, index));
};

// Interface for Handicapper data format expected by HandicappersView
export interface HandicapperPick {
  id: number;
  playerName: string;
  playerNumber: string;
  betType: string;
  gameInfo: string;
  confidence?: number;
}

export interface Handicapper {
  id: number;
  name: string;
  sports: string;
  rating: number;
  accuracy: string;
  profileImage: string;
  picks: HandicapperPick[];
}

// Function to convert events to handicappers format
export const convertEventsToHandicappers = (
  events: EventData[]
): Handicapper[] => {
  if (events.length === 0) {
    return [];
  }

  const handicapperMap = new Map<string, EventData[]>();

  events.forEach((event) => {
    const handicapperList = event.handicappers ?? [];
    handicapperList.forEach((handicapperName) => {
      if (!handicapperMap.has(handicapperName)) {
        handicapperMap.set(handicapperName, []);
      }
      handicapperMap.get(handicapperName)!.push(event);
    });
  });

  const handicappers: Handicapper[] = [];

  handicapperMap.forEach((handicapperEvents, handicapperName) => {
    // Use number of events (picks) instead of total predictions for consistency with HandicapperProfile
    const numberOfEvents = handicapperEvents.length;
    const accuracy = Math.min(95, 70 + Math.floor(numberOfEvents * 2));

    const picks: HandicapperPick[] = handicapperEvents.map((event, index) => {
      // Use database fields instead of parsing event_id
      const playerName = event.player_name || extractPlayerName(event.event_id);
      const playerNumber = extractPlayerNumber(event.event_id);
      // Prefer the specific handicapper's prediction when available to avoid
      // majority-vote skew flipping Over/Under on the card.
      let eventForFormatting: EventData = event;
      const expertMap = event.expert_predictions_map || {};
      const specific = expertMap[handicapperName];
      if (specific === 0 || specific === 1) {
        eventForFormatting = {
          ...event,
          predictions: [specific],
        };
      }

      const betType = formatBetType(eventForFormatting);
      const gameInfo = formatGameInfo(event);

      // Use actual stored confidence values if available, otherwise fall back to calculated confidence
      const confidence =
        event.confidence_values && event.confidence_values.length > 0
          ? getActualConfidence(event.confidence_values)
          : calculateConfidence(event.predictions);

      return {
        id: index + 1,
        playerName,
        playerNumber,
        betType,
        gameInfo,
        confidence,
      };
    });

    handicappers.push({
      id: generateHandicapperIdFromName(handicapperName),
      name: handicapperName,
      sports: "NBA, NFL, NHL",
      rating: Math.min(5, Math.max(3, Math.floor(accuracy / 20))),
      accuracy: `${accuracy}%`,
      profileImage: `/profile-${handicapperName
        .toLowerCase()
        .replace(/\s+/g, "-")}.jpg`,
      picks,
    });
  });

  return handicappers;
};

export const getHandicapperIdByName = (
  handicapperName: string,
  handicappers: Handicapper[]
): number | null => {
  const handicapper = handicappers.find((h) => h.name === handicapperName);
  return handicapper ? handicapper.id : null;
};

// Generate a deterministic ID from handicapper name
export const generateHandicapperIdFromName = (
  handicapperName: string
): number => {
  let hash = 0;
  for (let i = 0; i < handicapperName.length; i++) {
    const char = handicapperName.charCodeAt(i);
    hash = (hash << 5) - hash + char;
    hash = hash & hash; // Convert to 32-bit integer
  }
  return (Math.abs(hash) % 1000) + 1; // Ensure positive ID between 1-1000
};
