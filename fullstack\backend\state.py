# fullstack/backend/state.py
"""
Central place for in-memory global state used by handlers.
Move references from app.py to this module to make later refactors easier.
"""

# In-memory global state — mirrored from app.py
pick_objects = []
boost_promo_objects = []
protected_promo_objects = []
user_accuracy: float = 0.0

import os, sys
_WORKSPACE_ROOT = os.path.dirname(os.path.abspath(__file__))
if _WORKSPACE_ROOT not in sys.path:
    sys.path.append(_WORKSPACE_ROOT)
_PROJECT_ROOT = os.path.dirname(_WORKSPACE_ROOT)
if _PROJECT_ROOT not in sys.path:
    sys.path.append(_PROJECT_ROOT)
DB_PATH = os.path.join(_WORKSPACE_ROOT, "data", "pick_confidence.db")

next_id: int = 1
origin_profiles = {
    "ChalkBoardPI": 0.80,
    "HarryLock": 0.71,
    "DanGamblePOD": 0.78,
    "DanGambleAIEdge": 90.0,
    "GameScript": 0.80,
    "Winible": 0.83,
    "DoberMan": 0.76,
    "<PERSON><PERSON>ille<PERSON>": 0.60,
    "Me": lambda: user_accuracy,
}



def reset_state() -> None:
    global pick_objects, next_id, boost_promo_objects, protected_promo_objects
    pick_objects = []
    next_id = 1
    boost_promo_objects = []
    protected_promo_objects = []


# Date management globals
current_default_date = None  # Will be set by app.py
auto_update_enabled = True


def get_current_california_date():
    """Get the current date in California timezone (Pacific Time)."""
    try:
        from .services.date_utils import get_current_california_date as _get_ca_date
        return _get_ca_date()
    except Exception as e:
        print(f"❌ Error getting California date, using fallback: {e}")
        return "2025-07-20"  # Fallback date


def get_default_date():
    """Get the default date based on auto-update setting."""
    global current_default_date, auto_update_enabled
    if auto_update_enabled:
        return get_current_california_date()
    return current_default_date or get_current_california_date()


def update_default_date(new_date, admin_password, auto_update=None):
    """Update the default date setting with comprehensive validation."""
    global current_default_date, auto_update_enabled

    try:
        # Validate admin password
        if not admin_password:
            return {
                "success": False,
                "error": "Admin password is required",
                "code": "MISSING_PASSWORD"
            }

        if not isinstance(admin_password, str):
            return {
                "success": False,
                "error": "Admin password must be a string",
                "code": "INVALID_PASSWORD_TYPE"
            }

        if admin_password != "ppadmin42":
            return {
                "success": False,
                "error": "Invalid admin password",
                "code": "INVALID_PASSWORD"
            }

        # Comprehensive date validation
        from .services.date_utils import validate_date_format
        is_valid, sanitized_date, error_message = validate_date_format(new_date)

        if not is_valid:
            return {
                "success": False,
                "error": error_message,
                "code": "INVALID_DATE"
            }

        # Store the previous date for rollback if needed
        previous_date = current_default_date

        # Update the global date setting
        current_default_date = sanitized_date

        if auto_update is not None:
            auto_update_enabled = bool(auto_update)

        print(f"[INFO] Date setting updated: {previous_date} -> {sanitized_date}")

        return {
            "success": True,
            "message": f"Date updated successfully from {previous_date} to {sanitized_date}",
            "previous_date": previous_date,
            "new_date": sanitized_date,
            "auto_update": auto_update_enabled
        }

    except Exception as e:
        print(f"[ERROR] Unexpected error in update_default_date: {e}")
        return {
            "success": False,
            "error": "Internal server error occurred while updating date",
            "code": "INTERNAL_ERROR"
        }