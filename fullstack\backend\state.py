# fullstack/backend/state.py
"""
Central place for in-memory global state used by handlers.
Move references from app.py to this module to make later refactors easier.
"""

# In-memory global state — mirrored from app.py
pick_objects = []
boost_promo_objects = []
protected_promo_objects = []
user_accuracy: float = 0.0

import os, sys
_WORKSPACE_ROOT = os.path.dirname(os.path.abspath(__file__))
if _WORKSPACE_ROOT not in sys.path:
    sys.path.append(_WORKSPACE_ROOT)
_PROJECT_ROOT = os.path.dirname(_WORKSPACE_ROOT)
if _PROJECT_ROOT not in sys.path:
    sys.path.append(_PROJECT_ROOT)
DB_PATH = os.path.join(_WORKSPACE_ROOT, "data", "pick_confidence.db")

next_id: int = 1
origin_profiles = {
    "ChalkBoardPI": 0.80,
    "HarryLock": 0.71,
    "DanGamblePOD": 0.78,
    "<PERSON><PERSON><PERSON>AIEdge": 90.0,
    "GameScript": 0.80,
    "Winible": 0.83,
    "DoberMan": 0.76,
    "<PERSON><PERSON><PERSON><PERSON>": 0.60,
    "Me": lambda: user_accuracy,
}



def reset_state() -> None:
    global pick_objects, next_id, boost_promo_objects, protected_promo_objects
    pick_objects = []
    next_id = 1
    boost_promo_objects = []
    protected_promo_objects = []