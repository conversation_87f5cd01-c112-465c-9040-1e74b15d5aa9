# fullstack/backend/services/settings_service.py
from typing import Optional, <PERSON><PERSON>
from datetime import datetime
from fullstack.backend.services.date_utils import get_current_california_date, validate_date_format

# Global date storage for admin settings - defaults to current California date
current_default_date = get_current_california_date()
auto_update_enabled = True


def get_default_date() -> str:
    if auto_update_enabled:
        return get_current_california_date()
    return current_default_date


def update_default_date(new_date: Optional[str], admin_password: Optional[str], auto_update: Optional[bool] = None) -> dict:
    global current_default_date, auto_update_enabled
    try:
        if not admin_password:
            return {"success": False, "error": "Admin password is required", "code": "MISSING_PASSWORD"}
        if not isinstance(admin_password, str):
            return {"success": False, "error": "Admin password must be a string", "code": "INVALID_PASSWORD_TYPE"}
        if admin_password != "ppadmin42":
            return {"success": False, "error": "Invalid admin password", "code": "INVALID_PASSWORD"}
        if not new_date:
            return {"success": False, "error": "Date is required", "code": "MISSING_DATE"}
        is_valid, sanitized_date, error_message = validate_date_format(new_date)
        if not is_valid:
            return {"success": False, "error": error_message or "Invalid date", "code": "INVALID_DATE"}
        previous_date = current_default_date
        current_default_date = sanitized_date
        if auto_update is not None:
            auto_update_enabled = bool(auto_update)
        print(f"[INFO] Date setting updated: {previous_date} -> {sanitized_date}")
        return {
            "success": True,
            "message": f"Date updated successfully from {previous_date} to {sanitized_date}",
            "previous_date": previous_date,
            "new_date": sanitized_date,
            "auto_update": auto_update_enabled,
        }
    except Exception as e:
        print(f"[ERROR] Unexpected error in update_default_date: {e}")
        return {"success": False, "error": "Unexpected server error", "code": "INTERNAL_ERROR"}

