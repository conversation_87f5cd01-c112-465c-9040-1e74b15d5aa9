import numpy as np
from fullstack.backend.modules.HandiCapperAccuracyModel.database.AccuracyModelDatabase import (
    AccuracyModelDatabase,
)
from fullstack.backend.modules.HandiCapperAccuracyModel.models.BayesianLogisticPredictor import (
    BayesianLogisticPredictor,
)

from sklearn.model_selection import GridSearchCV
from sklearn.neighbors import KernelDensity
from scipy.interpolate import interp1d



class KDECDFShifter:
    def __init__(self):
        self.bayesian_logistic_predictor = BayesianLogisticPredictor()
        self.db = AccuracyModelDatabase()


    def close(self):
        self.db.close()

    def compute_kde_probabilities(
        self, expert_df, stat_column, original_data, metadata, logistic_model, logistic_expert_names
    ):
        print("📈 Computing team KDE CDF with mean-shift adjustment...")
        cdf_result, x_lower, x_upper, sample_range, normalized_density, cdf = self.compute_team_cdf_with_kde_shift(
            expert_df,
            stat_column,
            original_data,
            player_name=metadata["player_name"],
            stat_type=metadata["stat_type"].lower(),
            logistic_model=logistic_model,
            logistic_expert_names=logistic_expert_names,
        )

        kde_output = {
            "team_kde_prob": None,
            "team_kde_confidence_bounds": None,
        }

        if cdf_result:
            kde_output = {
                "team_kde_prob": 1 - float(cdf_result(metadata["stat_threshold"])),
                "team_kde_confidence_bounds": [round(x_lower, 1), round(x_upper, 1)],
                "sample_range": sample_range,
                "pdf": normalized_density,
                "cdf": cdf
            }
            x_values = np.linspace(0, 70, 140)
            # TODO: delete this or implement this somewhere else
            # The variable is not used
            top_95_confidence_interval = [x for x in x_values if float(cdf_result(x)) > 0.70]

            print("✅ KDE probability interpolation successful.")
        else:
            print("⚠️ KDE probability interpolation failed.")

        return kde_output

    # TODO: delete stat_column since it is not used here.
    # TODO: delete player_name, stat_type
    def compute_team_cdf_with_kde_shift(
        self,
        expert_df,
        stat_column,
        original_data,
        player_name: str,
        stat_type: str,
        logistic_model=None,
        logistic_expert_names=None,
    ):
        """
        Creates a KDE-interpolated continuous CDF for a player's target stat
        by modeling the team's multivariate distribution and shifting the mean
        based on weighted expert predictions.

        This function builds a **custom statistical distribution** for a specific player's stat
        (e.g., Tyler Herro's Points) by considering the behavior of all teammates on the same team.
        It adjusts the distribution based on expert predictions for that stat and returns a
        **continuous CDF (cumulative distribution function)** that reflects the likelihood of
        different outcomes.

        Step-by-step explanation:

        1. 🔍 Look up the player's team on the target date (e.g., "MIA" for Miami Heat).
        2. 🧑‍🤝‍🧑 Get a list of all active teammates on that team from a helper function.
        3. 📊 Use `analyze_multiplayer_stats()` to gather past game logs for all those players and
           build a multivariate distribution of their stats. Each stat is labeled like
           "Tyler Herro's Points" to keep them separate.
        4. 🎯 Focus in on the stat of interest (like "Tyler Herro's Points") and extract its
           historical values from the dataset.
        5. 🗣️ Pull all expert predictions from your database related to the same team and date.
           Filter down to predictions about the exact player and stat.
        6. ⚖️ For each matching prediction, use its probability (combined_prob) as a weight
           and compute a weighted average predicted value.
        7. 📈 Shift the mean of the original stat data toward this weighted average — a
           "mean shift" — to reflect what the experts expect to happen.
        8. 🧮 Use kernel density estimation (KDE) to fit a smooth probability curve (PDF)
           to this shifted data, then convert it into a CDF.
        9. 🔁 Return a callable function that interpolates this CDF so you can plug in any
           stat value and get back the cumulative probability (i.e., likelihood that the
           player stays under that value).

        This is useful for building probability curves that are:
        ✅ Data-driven (based on actual player/team stats),
        ✅ Adaptable (influenced by expert insights), and
        ✅ Continuous (supporting fine-grained probability analysis for any stat threshold).
        """

        # Step 1: Identify the player's current team
        # TODO: Resolved! getting from parameter
        # These steps could be removed

        # Step 2-4: Get historical stat distribution from teammates
        # TODO: Resolved! getting from parameter
        # These steps could be removed
        if original_data is None:
            return None

        original_mean = np.mean(original_data)

        # Step 5-6: Expert-based KDE mean shift
        weighted_shifts, total_weight = self.compute_weighted_shifts(
            expert_df=expert_df,
            player_name=player_name,
            stat_type=stat_type,
            original_mean=original_mean,
            logistic_model=logistic_model,
            logistic_expert_names=logistic_expert_names,
        )

        # Step 7: Adjust KDE confidence and shift strength
        if total_weight > 0:
            new_mean = sum(weighted_shifts) / total_weight
            shift_amount = new_mean - original_mean

            # ✅ Agreement score: closer = more reinforcement
            agreement_score = np.exp(-abs(new_mean - original_mean))

            # ✅ Dynamic adjustments
            shift_scale = 1 - 0.7 * agreement_score  # reduce shift if reinforced
            bandwidth_scale = (
                1 - 0.5 * agreement_score
            )  # reduce bandwidth if reinforced

            shifted_data = original_data + (shift_amount * shift_scale)
            print(
                f"📌 PDF Mean After Scaled Shift: {round(original_mean + shift_amount * shift_scale, 2)}"
            )
        else:
            shifted_data = original_data
            bandwidth_scale = 1.0  # default bandwidth if no expert adjustment
            print(f"📌 PDF Mean (No Shift Applied): {round(original_mean, 2)}")

        # Step 8-9: Build KDE CDF with scaled bandwidth
        return self.build_kde_cdf(
            shifted_data=shifted_data,
            bandwidth_scale=bandwidth_scale,  # ➕ pass scaling into KDE
        )

    # TODO: delete player_name, stat_type
    def compute_weighted_shifts(
        self,
        expert_df,
        player_name,
        stat_type,
        original_mean,
        logistic_model,
        logistic_expert_names,
    ):
        """
        Step 5-6: Compute weighted shift from expert predictions.
        Only include predictions that reinforce a directional expectation.
        """
        # TODO: move this to where we are actually getting the expert_df
        if expert_df.empty:
            print("⚠️ No expert predictions found.")
            return [], 0.0

        weighted_shifts = []
        total_weight = 0.0

        for _, row in expert_df.iterrows():
            if row["player_name"] != player_name or row["stat_type"] != stat_type:
                continue

            prediction = self.bayesian_logistic_predictor.run_prediction(
                row["event_id"],
                logistic_model=logistic_model,
                logistic_expert_names=logistic_expert_names,
            )

            if (
                prediction
                and prediction.get("combined_prob") is not None
                and prediction.get("predicted_class") is not None
            ):
                weight = prediction["combined_prob"]
                # prediction of expert prediction"
                # this does not change based on expert prediction
                predicted_class = row["prediction"]
                threshold = row["stat_threshold"]

                if predicted_class == 1 and threshold > original_mean:
                    print(
                        f"[DEBUG] Over prediction accepted: threshold={threshold}, mean={original_mean}, weight={weight}"
                    )
                    weighted_shifts.append(weight * threshold)
                    total_weight += weight
                elif predicted_class == 0 and threshold < original_mean:
                    print(
                        f"[DEBUG] Under prediction accepted: threshold={threshold}, mean={original_mean}, weight={weight}"
                    )
                    weighted_shifts.append(weight * threshold)
                    total_weight += weight
                elif predicted_class == 1 and threshold < original_mean:
                    adjustment_factor = 0.5
                    adjusted_weight = weight * adjustment_factor
                    print(
                        f"[DEBUG] Reinforcing OVER prediction added: threshold={threshold}, mean={original_mean}, adjusted_weight={adjusted_weight}"
                    )
                    weighted_shifts.append(
                        adjusted_weight * original_mean
                    )  # 👈 reinforce current mean
                    total_weight += adjusted_weight
                elif predicted_class == 0 and threshold > original_mean:
                    adjustment_factor = 0.5
                    adjusted_weight = weight * adjustment_factor
                    print(
                        f"[DEBUG] Reinforcing UNDER prediction added: threshold={threshold}, mean={original_mean}, adjusted_weight={adjusted_weight}"
                    )
                    weighted_shifts.append(adjusted_weight * original_mean)
                    total_weight += adjusted_weight
                else:
                    print(
                        f"[DEBUG] Prediction ignored: predicted_class={predicted_class}, threshold={threshold}, mean={original_mean}"
                    )

        return weighted_shifts, total_weight

    def build_kde_cdf(
        self, shifted_data, bandwidth_scale=1.0
    ):
        """
        Step 7-8: Build KDE-based CDF from shifted data.
        - Uses grid search to tune bandwidth
        - Builds interpolated cumulative distribution function (CDF)
        - Also visualizes PDF & CDF and saves to file
        - Returns interpolated CDF + confidence bounds
        """

        # Reshape shifted_data to be a 2D array (n_samples, 1)
        shifted_data = shifted_data.reshape(-1, 1)

        # TODO: Test which bandwidth option is best for accuracy in backtester
        bandwidths = np.linspace(0.1 * bandwidth_scale, 2.0 * bandwidth_scale, 20)
        grid = GridSearchCV(
            KernelDensity(kernel="gaussian"), {"bandwidth": bandwidths}, cv=5
        )
        grid.fit(shifted_data)
        kde = grid.best_estimator_

        # Create samples for PDF/CDF Calcualtion
        # TODO: Test Sample Range Options for accuracy in backtester
        sample_range = self._create_sampels_for_PDF_CDF_Calculation(shifted_data)

        density = self._evaluate_kde(kde, sample_range)
        cdf = self._calculate_cdf(density)
        sample_range, density = self._flatten_inputs(sample_range, density)
        normalized_density = self._normalize_pdf(density, sample_range)

        # Pair x and normalized density
        x_density_pairs = list(zip(sample_range, normalized_density))

        # Sort by density descending (most likely values first)
        x_density_pairs.sort(key=lambda x: -x[1])

        # Accumulate probability mass until 15% is reached
        cumulative = 0.0
        included_x = []
        for x, dens in x_density_pairs:
            width = sample_range[1] - sample_range[0]  # uniform spacing
            mass = dens * width
            cumulative += mass
            included_x.append(x)
            if cumulative >= 0.15:
                break

        # Step 9: Get bounds
        x_lower = min(included_x)
        x_upper = max(included_x)

        # Return interpolated CDF function + bounds
        return (
            interp1d(sample_range, cdf, bounds_error=False, fill_value=(0.0, 1.0)),
            x_lower,  # confidence bound start
            x_upper,  # confidence bound end
            sample_range,  # X-axis for PDF/CDF
            normalized_density,  # PDF values
            cdf  # CDF values
        )

    def _create_sampels_for_PDF_CDF_Calculation(self, shifted_data):
        upper = np.percentile(shifted_data, 99) + 5  # avoid tail effects
        sample_range = np.linspace(0, upper, 1000).reshape(-1, 1)
        return sample_range

    def _evaluate_kde(self, kde, sample_range):
        """Apply KDE to get raw density values."""
        log_dens = kde.score_samples(sample_range)
        return np.exp(log_dens)

    def _calculate_cdf(self, density):
        """Compute and normalize CDF."""
        cdf = np.cumsum(density)
        return cdf / cdf[-1]

    def _normalize_pdf(self, density, sample_range):
        """Normalize PDF so area ≈ 1 over range."""
        pdf_area = np.trapz(density, sample_range)
        return density / pdf_area

    def _flatten_inputs(self, sample_range, density):
        """Ensure sample_range and density are 1D vectors."""
        return sample_range.ravel(), density.ravel()


